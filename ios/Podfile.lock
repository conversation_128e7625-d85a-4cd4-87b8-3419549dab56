PODS:
  - app_settings (5.1.1):
    - Flutter
  - blufi_plugin (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/CoreOnly (10.24.0):
    - FirebaseCore (= 10.24.0)
  - Firebase/Messaging (10.24.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.24.0)
  - firebase_core (2.30.0):
    - Firebase/CoreOnly (= 10.24.0)
    - Flutter
  - firebase_messaging (14.8.2):
    - Firebase/Messaging (= 10.24.0)
    - firebase_core
    - Flutter
  - FirebaseCore (10.24.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.25.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.25.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.24.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - Flutter (1.0.0)
  - flutter_blue_plus (0.0.1):
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_js (0.1.0):
    - Flutter
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_mxlogger (0.0.1):
    - Flutter
    - MXLogger (= 1.2.8)
  - flutter_timezone (0.0.1):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleMLKit/BarcodeScanning (4.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 3.0.0)
  - GoogleMLKit/MLKitCore (4.0.0):
    - MLKitCommon (~> 9.0.0)
  - GoogleToolboxForMac/DebugUtils (2.3.2):
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - GoogleToolboxForMac/Defines (2.3.2)
  - GoogleToolboxForMac/Logger (2.3.2):
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - "GoogleToolboxForMac/NSData+zlib (2.3.2)":
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - "GoogleToolboxForMac/NSDictionary+URLArguments (2.3.2)":
    - GoogleToolboxForMac/DebugUtils (= 2.3.2)
    - GoogleToolboxForMac/Defines (= 2.3.2)
    - "GoogleToolboxForMac/NSString+URLArguments (= 2.3.2)"
  - "GoogleToolboxForMac/NSString+URLArguments (2.3.2)"
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTMSessionFetcher/Core (2.3.0)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - locale_plus (1.0.1):
    - Flutter
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - MLImage (1.0.0-beta4)
  - MLKitBarcodeScanning (3.0.0):
    - MLKitCommon (~> 9.0)
    - MLKitVision (~> 5.0)
  - MLKitCommon (9.0.0):
    - GoogleDataTransport (~> 9.0)
    - GoogleToolboxForMac/Logger (~> 2.1)
    - "GoogleToolboxForMac/NSData+zlib (~> 2.1)"
    - "GoogleToolboxForMac/NSDictionary+URLArguments (~> 2.1)"
    - GoogleUtilities/UserDefaults (~> 7.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
  - MLKitVision (5.0.0):
    - GoogleToolboxForMac/Logger (~> 2.1)
    - "GoogleToolboxForMac/NSData+zlib (~> 2.1)"
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
    - MLImage (= 1.0.0-beta4)
    - MLKitCommon (~> 9.0)
  - mobile_scanner (3.5.5):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 4.0.0)
  - MXLogger (1.2.8):
    - MXLoggerCore (= 1.2.8)
  - MXLoggerCore (1.2.8)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - network_info_plus (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - pdf_render (0.0.1):
    - Flutter
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - Protobuf (3.26.1)
  - reactive_ble_mobile (0.0.1):
    - Flutter
    - Protobuf (~> 3.5)
    - SwiftProtobuf (~> 1.0)
  - SDWebImage (5.19.2):
    - SDWebImage/Core (= 5.19.2)
  - SDWebImage/Core (5.19.2)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - sensors_plus (0.0.1):
    - Flutter
  - share (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - SwiftProtobuf (1.26.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - blufi_plugin (from `.symlinks/plugins/blufi_plugin/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_blue_plus (from `.symlinks/plugins/flutter_blue_plus/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_js (from `.symlinks/plugins/flutter_js/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_mxlogger (from `.symlinks/plugins/flutter_mxlogger/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - locale_plus (from `.symlinks/plugins/locale_plus/ios`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - network_info_plus (from `.symlinks/plugins/network_info_plus/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - pdf_render (from `.symlinks/plugins/pdf_render/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - reactive_ble_mobile (from `.symlinks/plugins/reactive_ble_mobile/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - share (from `.symlinks/plugins/share/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - Google-Maps-iOS-Utils
    - GoogleDataTransport
    - GoogleMaps
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTMSessionFetcher
    - libwebp
    - Mantle
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - MXLogger
    - MXLoggerCore
    - nanopb
    - PromisesObjC
    - Protobuf
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftProtobuf

EXTERNAL SOURCES:
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  blufi_plugin:
    :path: ".symlinks/plugins/blufi_plugin/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_blue_plus:
    :path: ".symlinks/plugins/flutter_blue_plus/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_js:
    :path: ".symlinks/plugins/flutter_js/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_mxlogger:
    :path: ".symlinks/plugins/flutter_mxlogger/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  locale_plus:
    :path: ".symlinks/plugins/locale_plus/ios"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  network_info_plus:
    :path: ".symlinks/plugins/network_info_plus/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  pdf_render:
    :path: ".symlinks/plugins/pdf_render/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  reactive_ble_mobile:
    :path: ".symlinks/plugins/reactive_ble_mobile/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  share:
    :path: ".symlinks/plugins/share/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  app_settings: 3507c575c2b18a462c99948f61d5de21d4420999
  blufi_plugin: fd516cc307d14cb5650cf4973058a9e50124a0ad
  camera_avfoundation: 04b44aeb14070126c6529e5ab82cc7c9fca107cf
  connectivity_plus: 2256d3e20624a7749ed21653aafe291a46446fee
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  Firebase: 91fefd38712feb9186ea8996af6cbdef41473442
  firebase_core: 0559c0a77072c773b7da2ef3fcc38f328dc60fea
  firebase_messaging: ca77b8387bdf7de4bc814aac57b1c0e9b009e58d
  FirebaseCore: 11dc8a16dfb7c5e3c3f45ba0e191a33ac4f50894
  FirebaseCoreInternal: 910a81992c33715fec9263ca7381d59ab3a750b7
  FirebaseInstallations: 91950fe859846fff0fbd296180909dd273103b09
  FirebaseMessaging: 4d52717dd820707cc4eadec5eb981b4832ec8d5d
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_blue_plus: e5808fc4e5ebc58bb911635f8fdaf5e2b4da2754
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_js: 867813c1830e1d9b2c5d547cdb6f61801e2f2145
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_mxlogger: 2f7f518b0412f9ebdce4b05138c3ee1abf03503b
  flutter_timezone: ee50ce7786b5fde27e2fe5375bbc8c9661ffc13f
  geolocator_apple: 1560c3c875af2a412242c7a923e15d0d401966ff
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  google_maps_flutter_ios: 0291eb2aa252298a769b04d075e4a9d747ff7264
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleMLKit: 2bd0dc6253c4d4f227aad460f69215a504b2980e
  GoogleToolboxForMac: 8bef7c7c5cf7291c687cf5354f39f9db6399ad34
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTMSessionFetcher: 3a63d75eecd6aa32c2fc79f578064e1214dfdec2
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  locale_plus: ed29e4100d650f84061473f1b9c6178a920da4c5
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  MLImage: 7bb7c4264164ade9bf64f679b40fb29c8f33ee9b
  MLKitBarcodeScanning: 04e264482c5f3810cb89ebc134ef6b61e67db505
  MLKitCommon: c1b791c3e667091918d91bda4bba69a91011e390
  MLKitVision: 8baa5f46ee3352614169b85250574fde38c36f49
  mobile_scanner: f130726f4c914ebc95752b442eb8be2b89b75723
  MXLogger: 3faeff5e06ff7af0c7d90ce9cdd868ebe8876cdb
  MXLoggerCore: 8ec85ade559f68d408618a05f42163cd686ac68d
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  network_info_plus: 16d8832ab43b7bc22b168d76fffcad9da35486d2
  package_info_plus: 580e9a5f1b6ca5594e7c9ed5f92d1dfb2a66b5e1
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  pdf_render: 5f9586e5fd49519bf59ea14a6da4851616e91c81
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  photo_manager: d2fbcc0f2d82458700ee6256a15018210a81d413
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  Protobuf: a53f5173a603075b3522a5c50be63a67a5f3353a
  reactive_ble_mobile: 027a1eb013960fbedab38150e314254fdce8887f
  SDWebImage: dfe95b2466a9823cf9f0c6d01217c06550d7b29a
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  sensors_plus: 1c5f0a01ce21c609a4df404c4e6879d62bce287f
  share: a34936589f3090d59481bcdc5c30cc9dd47c75f6
  share_plus: 011d6fb4f9d2576b83179a3a5c5e323202cdabcf
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite: c35dad70033b8862124f8337cc994a809fcd9fa3
  SwiftProtobuf: 5e8349171e7c2f88f5b9e683cb3cb79d1dc780b3
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: fd58c82b1388f4afe3fe8aa2c856503a262a5b03
  webview_flutter_wkwebview: 45a041c7831641076618876de3ba75c712860c6b

PODFILE CHECKSUM: 9afccf56739e3eec9f8102fd4b00350b3c099e18

COCOAPODS: 1.14.3
