import 'dart:async';
import 'dart:io';

import 'package:flutter_basic/components/animate_loading.dart';
import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/components/cus_InkWell/CusInkWell.dart';
import 'package:flutter_basic/components/custom_card.dart';
import 'package:flutter_basic/components/error/custom_common_fail.dart';
import 'package:flutter_basic/components/home_page_top_background.dart';
import 'package:flutter_basic/components/no_network_bar.dart';
import 'package:flutter_basic/components/no_system_widget.dart';
import 'package:flutter_basic/components/page_loading.dart';
import 'package:flutter_basic/components/section_list_view/sectionView.dart';
import 'package:flutter_basic/components/single_select_popup/single_select_popup.dart';
import 'package:flutter_basic/event/create_diy_done_event.dart';
import 'package:flutter_basic/event/modify_system_name_event.dart';
import 'package:flutter_basic/event/refresh_devices_page_event.dart';
import 'package:flutter_basic/event/switch_system_event.dart';
import 'package:flutter_basic/event/update_device_name_event.dart';
import 'package:flutter_basic/features/devices/config/device_config.dart';
import 'package:flutter_basic/features/system_detail/config/SystemConfig.dart';
import 'package:flutter_basic/platform/bluetooth/blufi_manager.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/config/system_type.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/platform/utils/dialog_utils.dart';
import 'package:flutter_basic/platform/utils/event_bus_manager.dart';
import 'package:flutter_basic/platform/utils/sp_utils.dart';
import 'package:flutter_basic/repositories/combine_repository/model/combine_common_model.dart';
import 'package:flutter_basic/repositories/device_repository/model/device_list_response.dart';
import 'package:flutter_basic/router/devices_feature_routes/devices_feature_routes.dart';
import 'package:flutter_basic/router/diy_feature_routes/diy_route_type.dart';
import 'package:flutter_basic/router/diy_feature_routes/diy_routes.dart';
import 'package:flutter_basic/router/system_feature_routes/system_feature_routes.dart';
import 'package:flutter_js/quickjs/ffi.dart';
import 'package:flutter_page_lifecycle/flutter_page_lifecycle.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:permission_handler/permission_handler.dart';

import '../bloc/bloc.dart';

class DevicesPage extends StatefulWidget {
  final bool isRootNavigation;
  const DevicesPage({super.key, this.isRootNavigation = true});

  @override
  State<StatefulWidget> createState() {
    return DevicesPageState();
  }
}

class DevicesPageState extends State<DevicesPage> {
  int messageCounts = 0;
  List<DeviceGroup> devices = [];
  List<DeviceItem> epcDevices = [];
  String? systemId;
  String? systemName;
  SystemType systemType = SystemType.diy;
  int? csq4G;
  int? csqWifi;
  bool hasSystem = true;
  bool appear = true;
  bool fetchDeviceListError = false;
  int? netType;
  List<Permission> permissionList = [];
  bool startConnect = false;

  /// 是否是并机
  bool clustered = false;
  List<ClusterDeviceListResponse>? clusterList;
  String? selectedDeviceSN = null;

  /// 是否已经加载过列表
  bool isLoaded = false;

  final List<StreamSubscription> _subscriptions = [];

  /// 刷新完成状态，下拉刷新使用
  Completer? _refreshCompleter;

  /// 一体机状态
  int get gwStatus {
    if (devices.isEmpty) return 0;
    var gwDevice =
        devices.firstWhereOrNull((e) => e.productKey == oneMachineProductKey);
    return gwDevice?.devices
            ?.firstWhereOrNull((e) => e.productKey == oneMachineProductKey)
            ?.status ??
        0;
  }

  @override
  void initState() {
    super.initState();
    // appear 的时候 请求了 这里不重复请求
    String? systemId = SpUtil.getString(currentSystemIdKey, defValue: null);
    systemType =
        SystemConfig.getSystemType(SystemConfig.getCurrentSystemSeries());
    if (systemId != null) {
      this.systemId = systemId;
      _fetchDeviceList(this.systemId);
    }
    _subscriptions.addAll([
      EventBusManager.eventBus.on<SwitchSystemEvent>().listen((event) {
        this.systemId = event.systemId;
        _fetchDeviceList(systemId, needLoading: false);
      }),
      EventBusManager.eventBus.on<RefreshDevicesPageEvent>().listen((event) {
        _fetchDeviceList(systemId, needLoading: false);
      }),
      EventBusManager.eventBus.on<CreateDiyDoneEvent>().listen((event) {
        this.systemId = event.systemId;
        // 创建系统完成刷新
        _fetchDeviceList(event.systemId);
        setState(() {});
      }),
      EventBusManager.eventBus.on<ModifySystemName>().listen((event) {
        systemName = event.systemName;
        setState(() {});
      }),
      EventBusManager.eventBus.on<UpdateDeviceNameEvent>().listen((event) {
        if (devices.isEmpty) return;
        for (var element in devices) {
          element.devices?.forEach((device) {
            if (device.deviceNo == event.deviceNo) {
              setState(() {
                device.deviceName = event.newName;
              });
              return;
            }
          });
        }
      }),
    ]);
  }

  @override
  void dispose() {
    for (var element in _subscriptions) {
      element.cancel();
    }
    super.dispose();
  }

  /// 一体机sn
  String? get oneMachineDeviceSn {
    if (clustered) {
      return selectedDeviceSN;
    }
    var oneMachineGroup =
        devices.firstWhereOrNull((p0) => p0.productKey == oneMachineProductKey);
    if (oneMachineGroup == null) return null;
    var device = oneMachineGroup.devices
        ?.firstWhereOrNull((p0) => p0.productKey == oneMachineProductKey);
    return device?.deviceNo;
  }

  _checkPermissionAndConnectDevice() {
    if (Platform.isAndroid) {
      permissionList.add(Permission.location);
      permissionList.add(Permission.bluetoothScan);
      permissionList.add(Permission.bluetoothConnect);
    }
    PermissionUtils.checkPermission(
        permissionList: permissionList,
        onSuccess: () {
          if (oneMachineDeviceSn == null) {
            CustomToast.showToast(
                context, S.current.text('diyNew.fetch_device_info_fail'));
            return;
          }
          context.read<DevicesFetchBloc>().add(OneMachineBleConnectEvent(
                systemNo: oneMachineDeviceSn!,
                systemId: systemId ?? '',
              ));
        },
        onFailed: () => {});
  }

  _fetchDeviceList(String? systemId, {bool needLoading = true}) {
    BlocProvider.of<DevicesFetchBloc>(context)
        .add(DevicesInfoFetched(systemId, needLoading: needLoading));
  }

  _diyConnectDevice() {
    if (startConnect) {
      return;
    }
    _checkPermissionAndConnectDevice();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(context),
      backgroundColor: ColorsUtil.backgroundColor,
      body: BlocConsumer<DevicesFetchBloc, DevicesFetchState>(
        listener: (context, state) {
          if (state is DevicesFetchInProgress) {
            fetchDeviceListError = false;
          } else if (state is DevicesFetchFailure) {
            fetchDeviceListError = true;
            if (appear) {
              CustomLoading.dismissLoading();
            }
            if (_refreshCompleter?.isCompleted == false) {
              _refreshCompleter?.complete();
            }
          } else if (state is DevicesFetchSuccess) {
            fetchDeviceListError = false;
            isLoaded = true;
            messageCounts = state.deviceAlarmNumResponse.alarmNumber +
                state.deviceAlarmNumResponse.breakdownNumber;
            if (systemType == SystemType.diy) {
              devices = state.deviceListResponse.deviceGroup ?? [];
              systemName = state.deviceListResponse.systemName;
              csqWifi = state.deviceListResponse.csqWifi;
              csq4G = state.deviceListResponse.csq4G;
              netType = state.deviceListResponse.netType;
              clustered =
                  DeviceConfig.clustered(state.deviceListResponse.clustered);
              clusterList = state.deviceListResponse.clusterList;
            } else {
              epcDevices = state
                      .epcDeviceListResponse.epcDeviceListVO?.epcDeviceVOList ??
                  [];
              systemName =
                  state.epcDeviceListResponse.epcDeviceListVO?.systemName ?? '';
            }
            if (appear) {
              CustomLoading.dismissLoading();
            }
            if (_refreshCompleter?.isCompleted == false) {
              _refreshCompleter?.complete();
            }
            setState(() {});
          } else if (state is DevicesFetchConnectStart) {
            startConnect = true;
            CustomLoading.showLoading($t('device.add_accessories_loading_msg'));
            setState(() {});
          } else if (state is DevicesFetchConnectSuccess) {
            startConnect = false;
            if (appear) {
              CustomLoading.dismissLoading();
            }
            setState(() {});
            _navToAddDevicePage(state);
          } else if (state is DevicesFetchConnectFailure) {
            startConnect = false;
            CustomLoading.dismissLoading();
            CustomToast.showToast(context, $t('common.connection_failed'));
            setState(() {});
          } else if (state is BluetoothEnableFailure) {
            startConnect = false;
            CustomLoading.dismissLoading();
            setState(() {});
            DialogUtils.showAppBluetoothEnable(context);
          }
        },
        builder: (context, state) {
          hasSystem = systemId != null;
          return PageLifecycle(
            stateChanged: (appear) async {
              this.appear = appear;
              logger.d("DevicesPage is ${appear ? "appeared" : "disappeared"}");
              if (appear && isLoaded) {
                var newSystemId =
                    SpUtil.getString(currentSystemIdKey, defValue: null);
                // 客户要求  每次进入页面就要刷新
                _fetchDeviceList(newSystemId, needLoading: false);
                systemId = newSystemId;
                setState(() {});
              }
            },
            child: Stack(children: [
              if (widget.isRootNavigation) const HomePageTopBackground(),
              SafeArea(bottom: true, child: _buildBody(state))
            ]),
          );
        },
      ),
    );
  }

  CustomAppBar _buildAppBar(BuildContext context) {
    var actions = _buildAppbarActions(context);

    if (!widget.isRootNavigation) {
      return CustomAppBar(
        titleText: $t('tabbar.device_page_title'),
        actions: actions,
      );
    }

    return CustomAppBar(
      leadingWidth: isWhite ? 110.w : 134.w,
      toolbarHeight: 44.h,
      elevation: 0,
      backgroundColor: ColorsUtil.transparentColor,
      shadowColor: ColorsUtil.backgroundColor,
      leading: Container(
        margin: EdgeInsets.only(left: 20.w),
        child: CusInkWell(
          onTap: () {},
          child: CustomImageAsset(
            'icon_logo',
            width: isWhite ? 90.w : 114.w,
            height: isWhite ? 29.h : 16.h,
            fit: BoxFit.contain,
          ),
        ),
      ),
      actions: actions,
    );
  }

  List<Widget> _buildAppbarActions(BuildContext context) {
    if (systemId == null) return [];

    return [
      CusInkWell(
        onTap: () async {
          if (systemId == null) {
            CustomToast.showToast(context, $t('device.notice_no_message'));
            return;
          }
          await Navigator.of(context).push(
            IntergratedRoutes.onGenerateRoute(
              const RouteSettings(
                name: IntergratedRoutesType.deviceAlarmAndFault,
              ),
            ),
          );
        },
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            CustomImageAsset('icon_notifications', width: 24.w),
            _buildNotifications(messageCounts)
          ],
        ),
      ),
      if (systemType == SystemType.diy) ...[
        SizedBox(width: 15.w),
        CusInkWell(
          onTap: () => clustered ? _showSelectDevice() : _diyConnectDevice(),
          child: startConnect
              ? AnimateLoadingWidget(width: 24.w)
              : CustomImageAsset('icon_add', width: 24.w),
        ),
      ],
      SizedBox(width: 20.w),
    ];
  }

  Widget _buildBody(DevicesFetchState state) {
    if (!SystemConfig.singleton.hasNet) {
      return Center(
        child: CustomCommonFail(
          onTryAgainTap: () => _fetchDeviceList(systemId),
        ),
      );
    }
    return Column(
      children: [
        const NoNetworkBar(),
        Expanded(
          child: hasSystem ? _buildContent(state) : const NoSystemWidget(),
        )
      ],
    );
  }

  Widget _buildContent(DevicesFetchState state) {
    if (state is DevicesFetchInProgress && state.needLoading) {
      return const PageLoading();
    }
    return Container(
      margin: EdgeInsets.only(left: 20.w, right: 20.w, top: 10.h),
      child: Column(
        children: [
          systemName != null
              ? InkResponse(
                  onTap: () {
                    CustomLoading.dismissLoading();
                    var path = systemType == SystemType.epc
                        ? SystemRoutesType.epcSystemDetail
                        : SystemRoutesType.systemDetailRoute;
                    Navigator.of(context).push(
                      SystemRoutes.onGenerateRoute(
                        RouteSettings(
                          name: path,
                          arguments: {'systemId': systemId},
                        ),
                      ),
                    );
                  },
                  child: CustomCard(
                      child: Container(
                    padding: EdgeInsets.all(15.w),
                    child: Row(
                      children: [
                        Text(
                          systemName!,
                          style: TextStyle(
                              fontSize: 14.sp,
                              color: ColorsUtil.textColor,
                              fontWeight: FontWeight.w600),
                        ),
                        // DeviceConfig.getNetStatus(netType, csqWifi, csq4G),
                        Expanded(child: Container()),
                        CustomImageAsset('icon_arrow_right_black', width: 20.w),
                      ],
                    ),
                  )),
                )
              : const SizedBox.shrink(),
          Flexible(
            flex: 1,
            child: RefreshIndicator(
              color: Colors.grey,
              onRefresh: () {
                _fetchDeviceList(systemId, needLoading: false);
                _refreshCompleter = Completer();
                return _refreshCompleter!.future;
              },
              child: _buildList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildList() {
    systemType =
        SystemConfig.getSystemType(SystemConfig.getCurrentSystemSeries());
    if (systemType == SystemType.epc) {
      return _buildEpcList();
    } else {
      return _buildDiyList();
    }
  }

  Widget _buildEpcList() {
    return Column(
      children: [
        SizedBox(height: 10.w),
        Expanded(
          child: ListView.builder(
              scrollDirection: Axis.vertical,
              itemCount: epcDevices.length,
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.only(bottom: 20).w,
              itemBuilder: (context, index) {
                return _buildItem(context, epcDevices[index], index, '', '');
              }),
        ),
      ],
    );
  }

  Widget _buildDiyClusteredList() {
    List<DeviceItem> allDevice = [];
    clusterList?.forEach((device) {
      if (device.diyGwDeviceVO?.device != null) {
        // TODO: 0 不是主机，1 是主机
        device.diyGwDeviceVO?.device.mastered = device.mastered == 1
            ? Mastered.host.toInt()
            : Mastered.client.toInt();
        allDevice.add(device.diyGwDeviceVO!.device);
      }
      device.deviceList?.forEach((childDevice) {
        allDevice.add(childDevice.device);
      });
    });
    allDevice.sort((a, b) => (a.mastered ?? Mastered.client.toInt())
        .compareTo(b.mastered ?? Mastered.client.toInt()));
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.only(bottom: 20).w,
      child: Column(
          children:
              allDevice.map((e) => _buildItem(context, e, 1, '', '')).toList()),
    );
  }

  Widget _buildDiyList() {
    if (clustered) {
      return _buildDiyClusteredList();
    }
    return SectionView<DeviceGroup, DeviceItem>(
      bottomWidget: SizedBox(height: 20.w),
      physics: const AlwaysScrollableScrollPhysics(),
      source:
          devices.where((item) => item.devices?.isNotEmpty == true).toList(),
      enableSticky: false,
      onFetchListData: (header) => header.devices!,
      headerBuilder: getDefaultHeaderBuilder(
        builder: (header, headerIndex) => Container(
          margin: EdgeInsets.only(bottom: 6.h, top: 20.h),
          child: Text(
            S.current.text(header.languageKey ?? ''),
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.contentColor,
            ),
          ),
        ),
      ),
      itemBuilder: _buildItem,
    );
  }

  Widget _buildItem(context, itemData, itemIndex, headerData, headerIndex) {
    // 如果一体机离线，其他设备也显示离线
    int status = gwStatus == deviceStatusOffline
        ? deviceStatusOffline
        : (itemData.status ?? deviceStatusOnline);

    var deviceIcon = SizedBox.fromSize(
      size: Size(70.w, 70.w),
      child: itemData.deviceImg == null
          ? null
          : FadeInImage.assetNetwork(
              placeholder: CustomImageAsset.getImagePathByName('default_image'),
              image: itemData.deviceImg!,
              imageErrorBuilder: (context, error, stackTrace) {
                return CustomImageAsset('default_image');
              },
            ),
    );

    var isLocal = LocalModeManger.instance.isLocalModeNow;

    /// 设备名称
    var deivceName = Text(
      itemData.deviceName?.replaceFirst(RegExp(r'[^_]*_'), '') ?? '',
      style: TextStyle(
        color: isLocal && status == deviceStatusOffline
            ? ColorsUtil.contentColor
            : ColorsUtil.textColor,
        fontWeight: FontWeight.w500,
        fontSize: 13.sp,
      ),
    );
    // bms_1_dfdfdfd3343
    // bms2_dfdfdfdfdf
    // 假如包含 bms_ 则显示 dfdfdfdfdf
    String inInfoTitle = itemData.deviceNo ?? '';
    if (itemData.deviceNo?.contains('bms') == true) {
      inInfoTitle =
          itemData.deviceNo?.replaceFirst(RegExp(r'^bms(?:_\d+)?_'), '') ?? '';
      inInfoTitle = inInfoTitle?.replaceFirst(RegExp(r'^bms[^_]*_'), '') ?? '';
    }
    var infoTextList = [
      S.current.text('device.sn') + inInfoTitle,
      if (!isLocal || [deviceStatusAlarm, deviceStatusFault].contains(status))
        S.current.text(deviceStatusText[status] ?? ""),
    ];

    var deviceInfo = Text(
      infoTextList.join(' | '),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: DeviceConfig.getDeviceStatusColor(status),
        fontSize: 13.sp,
      ),
    );
    return InkResponse(
      onTap: () => _itemClick(itemData),
      child: CustomCard(
        margin: itemIndex != 0 ? EdgeInsets.only(top: 10.w) : EdgeInsets.zero,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 5.w, horizontal: 10.w),
          child: Stack(
            children: [
              Row(
                children: [
                  deviceIcon,
                  SizedBox(width: 20.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [deivceName, SizedBox(height: 3.w), deviceInfo],
                    ),
                  ),
                ],
              ),
              (itemData.productKey == oneMachineProductKey && clustered)
                  ? Positioned(
                      right: 0.w,
                      top: 2.w,
                      child: Container(
                          alignment: Alignment.centerLeft,
                          padding: EdgeInsets.symmetric(
                              horizontal: 6.w, vertical: 2.h),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.all(
                                Radius.circular(9.r),
                              ),
                              color: ColorsUtil.systemStatusBackgroundColor),
                          child: Row(
                            children: [
                              CustomImageAsset(
                                  itemData.status == deviceStatusOnline
                                      ? 'icon_device_primary'
                                      : 'icon_device_node',
                                  width: 15.r),
                              SizedBox(width: 3.w),
                              Text(
                                S.current.text(
                                    DeviceConfig.mastered(itemData.mastered)
                                        ? 'device.primary'
                                        : 'device.node'),
                                style: TextStyle(
                                    color: itemData.status == deviceStatusOnline
                                        ? ColorsUtil.systemWaitingColor
                                        : ColorsUtil.systemFaultColor,
                                    fontSize: 9.sp),
                              )
                            ],
                          )))
                  : Container()
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotifications(int messageCount) {
    double countWidth = messageCounts > 0 && messageCounts <= 9
        ? 20
        : messageCounts > 99
            ? 32
            : 26;
    double countHeight = 20;
    return messageCounts > 0
        ? Positioned(
            left: 10,
            top: -7,
            child: Container(
              height: countHeight.h,
              width: countWidth.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: ColorsUtil.themeColor,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: ColorsUtil.themeColor, width: 1)),
              child: Text(
                messageCount > 99 ? '99+' : messageCounts.toString(),
                style: TextStyle(
                    fontSize: 11.sp, color: ColorsUtil.buttonTextColor),
              ),
            ))
        : const SizedBox();
  }

  _itemClick(DeviceItem itemData) {
    CustomLoading.dismissLoading();
    if (systemType == SystemType.epc) {
      Navigator.of(context).push(
        IntergratedRoutes.onGenerateRoute(
          RouteSettings(
              name: IntergratedRoutesType.epcDeviceDetail,
              arguments: {
                'deviceNo': itemData.deviceNo,
              }),
        ),
      );
      return;
    }
    switch (itemData.productKey) {
      // 一体机
      case oneMachineProductKey:
        var deviceGroup = devices
            .firstWhereOrNull((v) => v.productKey == oneMachineProductKey);
        Navigator.of(context).push(
          IntergratedRoutes.onGenerateRoute(
            RouteSettings(
                name: IntergratedRoutesType.intergratedMachine,
                arguments: {
                  'pageName': deviceGroup?.languageKey ??
                      'devices.ems_gw_machine_title',
                  'deviceNo': itemData.deviceNo,
                  'productKey': itemData.productKey
                }),
          ),
        );
        break;
      // 烟感
      case shellySmokeProductKey:
        Navigator.of(context).push(
          IntergratedRoutes.onGenerateRoute(
            RouteSettings(name: IntergratedRoutesType.shellySmoke, arguments: {
              'deviceNo': itemData.deviceNo,
              'productKey': itemData.productKey
            }),
          ),
        );
      // 水浸
      case shellyFloodProductKey:
        Navigator.of(context).push(
          IntergratedRoutes.onGenerateRoute(
            RouteSettings(name: IntergratedRoutesType.shellyFlood, arguments: {
              'deviceNo': itemData.deviceNo,
              'productKey': itemData.productKey
            }),
          ),
        );
        break;
      case plugProductKey:
        Navigator.of(context).push(
          IntergratedRoutes.onGenerateRoute(
            RouteSettings(
                name: IntergratedRoutesType.smartSocketDetail,
                arguments: {
                  'deviceNo': itemData.deviceNo,
                  'productKey': itemData.productKey
                }),
          ),
        );
        break;
      case ctProductKey:
        Navigator.of(context).push(
          IntergratedRoutes.onGenerateRoute(
            RouteSettings(name: IntergratedRoutesType.ctDetail, arguments: {
              'deviceNo': itemData.deviceNo,
              'productKey': itemData.productKey
            }),
          ),
        );
        break;
      case bmsProductKey:
      case dcProductKey:
        Navigator.of(context).push(
          IntergratedRoutes.onGenerateRoute(
            RouteSettings(
                name: IntergratedRoutesType.bmsMachineDetail,
                arguments: {
                  'deviceNo': itemData.deviceNo,
                  'productKey': itemData.productKey
                }),
          ),
        );
        break;
    }
  }

  ///跳转一体机添加辅件
  void _navToAddDevicePage(DevicesFetchConnectSuccess state) async {
    await Navigator.of(context).push(
      DiyRoutes.onGenerateRoute(
        RouteSettings(name: DiyRouteType.diyAddShellyRoute, arguments: {
          'systemNo': state.systemNo,
          'systemId': state.systemId,
          'status': 1,
          'clustered': clustered,
        }),
      ),
    );
    BlufiManager.instance.close();
    // 添加后 更新列表
    // _fetchDeviceList(systemId);
  }

  _showSelectDevice() async {
    var phase = PhaseType.single;
    // 相位类型取自主机的类型
    clusterList?.forEach((e) {
      if (e.mastered == Mastered.host.toInt()) {
        phase = PhaseType.fromInt(e.phase);
      }
    });
    // 这里先置空
    selectedDeviceSN = null;
    final list = clusterList?.map((element) {
          return SingleItemMode<DeviceItem>(
              name: element.diyGwDeviceVO?.device.deviceName ?? '',
              checked:
                  selectedDeviceSN == element.diyGwDeviceVO?.device.deviceNo,
              originData: element.diyGwDeviceVO!.device,
              tags: [
                phase == PhaseType.single
                    ? (DeviceConfig.mastered(
                            element.diyGwDeviceVO?.device.mastered)
                        ? S.current.text('device.primary')
                        : S.current.text('device.node'))
                    : Phase.fromInt(element.phase).toPhaseText(),
                if (phase == PhaseType.three &&
                    DeviceConfig.mastered(
                        element.diyGwDeviceVO?.device.mastered))
                  S.current.text('device.primary')
              ]);
        }).toList() ??
        [];
    final result = await SingleSelectDialog.showSingleDialog<DeviceItem>(
        context,
        SingleData(
            title: S.current.text('device.select_device'),
            desc: S.current.text('device.select_device_desc'),
            list: list));
    if (result != null) {
      selectedDeviceSN = result.originData.deviceNo;

      /// 静态页面所以注释了连接方法
      _diyConnectDevice();
    }
  }
}
