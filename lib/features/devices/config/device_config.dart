import 'package:flutter_basic/components/custom_wired_network.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/repositories/combine_repository/model/combine_common_model.dart';

import '../../../components/custom_cellular_signal.dart';
import '../../../components/custom_wifi_signal.dart';
import '../../../platform/platform.dart';

// DC 负载开关
enum DcSwitchStatus {
  on(1), //on  开
  off(0); //off 关

  final int value;
  const DcSwitchStatus(this.value);
  toInt() => value;
  static DcSwitchStatus fromInt(int status) {
    switch (status) {
      case 1:
        return DcSwitchStatus.on;
      case 0:
        return DcSwitchStatus.off;
      default:
        return DcSwitchStatus.off;
    }
  }
}

// DC 通信状态  设备状态 0正常 1离线
enum DcCommunicationStatus {
  normal(0), //正常
  offline(1); //离线

  final int value;
  const DcCommunicationStatus(this.value);
  toInt() => value;
  static DcCommunicationStatus fromInt(int status) {
    switch (status) {
      case 0:
        return DcCommunicationStatus.normal;
      case 1:
        return DcCommunicationStatus.offline;
      default:
        return DcCommunicationStatus.normal;
    }
  }
}

enum PlugSwitchStatus {
  on(1), //on  开
  off(0); //off 关

  final int value;
  const PlugSwitchStatus(this.value);
  toInt() => value;
  static PlugSwitchStatus fromInt(int status) {
    switch (status) {
      case 1:
        return PlugSwitchStatus.on;
      case 0:
        return PlugSwitchStatus.off;
      default:
        return PlugSwitchStatus.off;
    }
  }
}

const DeviceNetType_WIFI = 2;
const DeviceNetType_4G = 1;
const DeviceNetType_WIRE = 0;
// 有线网络 + wifi
const DeviceNetType_WIRE_WIFI = 3;

const Map<int, String> deviceStatusText = {
  1: 'device.Online',
  2: 'device.Offline',
  3: 'device.Alarm',
  4: 'device.Fault',
  -1: 'device.Offline'
};

enum DeviceStatus {
  NOT_ACTIVE(0),
  NORMAL(1),
  OFFLINE(2);

  final int status;
  const DeviceStatus(this.status);
}

const energyStatusUnknow = -1;
const deviceStatusOnline = 1;
const deviceStatusOffline = 2;
const deviceStatusAlarm = 3;
const deviceStatusFault = 4;

const deviceWorkStatusNormal = 0;
const deviceWorkStatusWait = 1;
const deviceWorkStatusAlarm = 2;
const deviceWorkStatusFault = 3;

enum DeviceType { oneMachine }

const String oneMachineProductKey = 'HB-EMS-GW';
const String plugProductKey = 'HB-PLUG';
const String bmsProductKey = 'HB-BMS';
const String dcProductKey = 'HB-DC';
const String ctProductKey = 'HB-CT';

// 烟感
const String shellySmokeProductKey = 'HB-SMOKING';
// 水浸
const String shellyFloodProductKey = 'HB-WATER';
const Map<String, DeviceType> deviceMapping = {
  oneMachineProductKey: DeviceType.oneMachine
};
const List<String> mainDeviceList = [oneMachineProductKey];

class DeviceConfig {
  static bool clustered(int? clustered) {
    return clustered == Clustered.yes.toInt();
  }

  static bool mastered(int? mastered) {
    return mastered == Mastered.host.toInt();
  }

  // 是否是中央主设备
  static bool isMainDevice(String productKey) {
    return mainDeviceList.contains(productKey);
  }

  static Color getEnergyStatusBorderColor(int? status) {
    switch (status) {
      case deviceWorkStatusAlarm:
        return ColorsUtil.systemAlarmColor;
      case deviceWorkStatusFault:
        return ColorsUtil.systemFaultColor;
      default:
        return ColorsUtil.dividerColor;
    }
  }

  static Color getEnergyStatusContentColor(int? status) {
    switch (status) {
      case deviceWorkStatusAlarm:
        return ColorsUtil.systemAlarmColor.withOpacity(0.05);
      case deviceWorkStatusFault:
        return ColorsUtil.systemFaultColor.withOpacity(0.05);
      default:
        return ColorsUtil.aboutCardColor;
    }
  }

  static Color getDeviceStatusColor(int status) {
    switch (status) {
      case energyStatusUnknow:
        return ColorsUtil.pickerHintColor;
      case deviceStatusOffline:
        return ColorsUtil.pickerHintColor;
      case deviceStatusAlarm:
        return ColorsUtil.systemAlarmColor;
      case deviceStatusFault:
        return ColorsUtil.systemFaultColor;
      case deviceStatusOnline:
        return ColorsUtil.contentColor;
      default:
        return ColorsUtil.systemWaitingColor;
    }
  }

  static Widget getNetStatus(
      int? netType, int? csqWifi, int? csq4G, bool isOffline,
      {Color? color}) {
    switch (netType) {
      case DeviceNetType_WIFI:
        return CustomWifiSignal(csqWifi ?? -1000, color: color);
      case DeviceNetType_4G:
        return CustomCellularSignal(csq4G ?? -1000, color: color);
      case DeviceNetType_WIRE:
      case DeviceNetType_WIRE_WIFI:
        return CustomWiredNetwork(csqWifi ?? -50,
            color: color, isOffline: isOffline);
      default:
        // 默认是有线的
        return CustomWiredNetwork(csqWifi ?? -50,
            color: color, isOffline: isOffline);
    }
  }

  static String getPowerByDouble(double? power) {
    return power?.toInt().toString() ?? '-';
  }
}
