import 'package:flutter_basic/components/bottom_button.dart';
import 'package:flutter_basic/components/custom_card_list.dart';
import 'package:flutter_basic/components/custom_image_asset.dart';
import 'package:flutter_basic/event/main_page_tab_switch_event.dart';
import 'package:flutter_basic/features/device_detail/modify_device_name/routes/modify_device_name_routes.dart';
import 'package:flutter_basic/generated/l10n.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/mqtt/mqtt_manager.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/platform/utils/dialog_utils.dart';
import 'package:flutter_basic/platform/utils/event_bus_manager.dart';
import 'package:flutter_basic/platform/utils/local_mode_utils.dart';
import 'package:flutter_basic/repositories/device_repository/model/device_detail_response.dart';
import 'package:flutter_basic/router/diy_feature_routes/diy_feature_routes.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../../components/cus_InkWell/CusInkWell.dart';
import '../../../../platform/utils/sp_utils.dart';
import '../../../../router/devices_feature_routes/devices_routes.dart';
import '../../../../router/devices_feature_routes/devices_routes_type.dart';
import '../../../../router/system_feature_routes/system_routes.dart';
import '../../../../router/system_feature_routes/system_routes_type.dart';
import '../bloc/bloc.dart';

class IntergratedPage extends StatefulWidget {
  final String deviceNo;
  final String productKey;
  final String? pageName;

  const IntergratedPage({
    super.key,
    required this.deviceNo,
    required this.productKey,
    this.pageName,
  });

  @override
  State<StatefulWidget> createState() {
    return IntergratedPageState();
  }
}

class IntergratedPageState extends State<IntergratedPage> {
  int messageCounts = 0;
  DeviceInfoResponse? deviceModel;
  String updateTime = '';
  DateFormat dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');

  late String pageName =
      S.current.text(widget.pageName ?? 'devices.ems_gw_machine_title');

  bool _localModeLoadingIsShow = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        elevation: 0,
        titleText: pageName,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CusInkWell(
                  onTap: () {
                    Navigator.of(context).push(
                      IntergratedRoutes.onGenerateRoute(
                        RouteSettings(
                          name: IntergratedRoutesType.deviceAlarmAndFault,
                          // deviceNo集合
                          arguments: {
                            'devicesNoList': [widget.deviceNo]
                          },
                        ),
                      ),
                    );
                  },
                  child: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      CustomImageAsset(
                        'icon_notifications',
                        width: 24.w,
                        height: 24.h,
                      ),
                      _buildNotifications(messageCounts)
                    ],
                  ),
                ),
                SizedBox(width: 20.w),
                CusInkWell(
                  onTap: () {
                    String? systemId =
                        SpUtil.getString(currentSystemIdKey, defValue: null);
                    if (systemId != null) {}
                    Navigator.of(context).push(
                      SystemRoutes.onGenerateRoute(
                        RouteSettings(
                            name: SystemRoutesType.operateSettingRoute,
                            arguments: {
                              'systemId': systemId,
                            }),
                      ),
                    );
                  },
                  child: CustomImageAsset(
                    'icon_preference_black',
                    width: 24.w,
                    height: 24.h,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
      backgroundColor: ColorsUtil.backgroundColor,
      body: BlocConsumer<IntergratedFetchBloc, IntergratedFetchState>(
        listener: (context, state) {
          if (state is IntergratedFetchInProgress) {
            CustomLoading.showLoading(null);
          } else if (state is BluetoothConnecting) {
            CustomLoading.showLoading(
                S.current.text('moreSetting.wifi_settings_loading'));
          } else if (state is IntergratedFetchSuccess) {
            deviceModel = state.deviceModel;
            updateTime = dateFormat.format(DateTime.now());

            setState(() {});
          } else if (state is IntergratedFetchComplete) {
            CustomLoading.dismissLoading();
          } else if (state is IntergratedFetchMessageSuccess) {
            messageCounts = state.count;
            setState(() {});
          } else if (state is ConnectedSuccess) {
            CustomLoading.dismissLoading();
            _navToWifiSetting(state.sn);
          } else if (state is ConnectedFailure) {
            CustomLoading.dismissLoading();
            DialogUtils.showAppBlufiConnectFailure(context);
          } else if (state is IntergratedFetchFailure) {
            if (state.msg != null) {
              CustomToast.showToast(context, state.msg!);
            }
          } else if (state is BluetoothEnableFailure) {
            CustomLoading.dismissLoading();
            DialogUtils.showAppBluetoothEnable(context);
          } else if (state is LocalModeStart) {
            _localModeLoadingIsShow = true;
            LocalModeUtils.showLocalLoadingDialog(context).then((value) {
              _localModeLoadingIsShow = false;
            });
          } else if (state is LocalModeSuccess) {
            if (_localModeLoadingIsShow) Navigator.of(context).pop();
            LocalModeUtils.showLocalSuccessDialog(context, () {
              Navigator.popUntil(context, (route) => route.isFirst);
              // 跳转到monitor tab
              EventBusManager.eventBus.fire(MainPageTabSwitchEvent(0));
            });
          } else if (state is LocalModeConnectTimeout) {
            // 本地模式连接超时
            if (_localModeLoadingIsShow) Navigator.of(context).pop();
            LocalModeUtils.showLocalTimeoutDialog(context, _navToLocalModel);
          } else if (state is LocalModeFailure) {
            if (!_localModeLoadingIsShow) return;
            CustomLoading.dismissLoading();
            CustomToast.showToast(
                context, S.current.text('common.connection_failed'));
          }
        },
        buildWhen: (p, current) {
          return current is! IntergratedFetchDialogSuccess;
        },
        builder: (context, state) {
          return SafeArea(
              child: Container(
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            child: Column(
              children: [
                Expanded(
                  child: deviceModel != null
                      ? SingleChildScrollView(
                          child: Column(
                            children: [
                              SizedBox(height: 10.h),
                              _buildBaseInfo(),
                              ..._buildProductGroup(),
                              SizedBox(height: 29.h),
                              SizedBox(
                                height: 36.h,
                                child: Text(
                                  '${S.current.text('trend.intergrated_updated')} $updateTime',
                                  style: TextStyle(
                                    fontSize: 13.sp,
                                    color: ColorsUtil.contentColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      : Container(),
                ),
                BottomButtonWidget(
                    text: S.current.text('device.localMode'),
                    buttonType: BottomButtonType.normal,
                    padding: const EdgeInsets.all(0),
                    onPressed: () {
                      _navToLocalModel();
                      // CustomToast.showToast(
                      //     context, S.current.text('moreSetting.local_mode_not_enabled'));
                      // Navigator.of(context).pushAndRemoveUntil(
                      //     MainRoutes.onGenerateRoute(
                      //       const RouteSettings(
                      //         name: MainRoutesType.rootRoute,
                      //       ),
                      //     ),
                      //         (route) => false);
                      // Navigator.of(context).popUntil((route) => route.isFirst);
                    }),
              ],
            ),
          ));
        },
      ),
    );
  }

  _navToLocalModel() async {
    if (MqttManager.instance.isOnlineNow) {
      DialogUtils.showNewDialog(
        context: context,
        content: S.current.text('moreSetting.local_mode_device_online'),
        cancelText: S.current.text('common.ok'),
      );
      return;
    }

    // CustomToast.showToast(
    //     context, S.current.text('moreSetting.local_mode_not_enabled'));
    // return;
    //本地模式连接入口增加是否有网检测
    var systemId = SpUtil.getString(currentSystemIdKey, defValue: null);
    if (!await LocalModeManger.instance
        .checkBluetoothStatus(systemId: systemId)) {
      return;
    }
    context
        .read<IntergratedFetchBloc>()
        .add(IntergratedLocalModeFetch(systemId: systemId!));
  }

  _buildBaseInfoList() {
    return [
      CardItemData(
          title: S.current.text('device.Device_Name'),
          content: deviceModel?.baseVo?.deviceName,
          showIcon: true,
          onTap: () async {
            final deviceName = await Navigator.of(context).push(
              IntergratedRoutes.onGenerateRoute(
                RouteSettings(
                  name: IntergratedRoutesType.modifyDeviceName,
                  arguments: ModifyDeviceNameRoute.pageArgs(
                    pageTitle: pageName,
                    title: S.current.text('device.Device_Name'),
                    hint: S.current.text('device.enter_Device_Name_hint'),
                    text: deviceModel?.baseVo?.deviceName ?? '',
                    deviceNo: deviceModel?.baseVo?.deviceNo ?? '',
                  ),
                ),
              ),
            );
            if (deviceName != null) {
              setState(() {
                deviceModel?.baseVo?.deviceName = deviceName;
              });
            }
          }),
      CardItemData(
        title: S.current.text('device.Device_No'),
        content: deviceModel?.baseVo?.deviceNo,
        showIcon: false,
      ),
    ];
  }

  Widget _buildBaseInfo() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              S.current.text('device.One_Machine_base_info'),
              style: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.contentColor,
              ),
            ),
            CusInkWell(
              onTap: _bluetoothConnectDevice,
              child: Row(
                children: [
                  Text(
                    S.current.text('device.WI-FI_Settings'),
                    style: TextStyle(
                        color: ColorsUtil.highlightTextColor, fontSize: 13.sp),
                  ),
                  CustomImageAsset(
                    'arrow_right_theme_color',
                    width: 20.w,
                    height: 20.w,
                  )
                ],
              ),
            )
          ],
        ),
        SizedBox(height: 6.h),
        CustomCardList(cardList: _buildBaseInfoList())
      ],
    );
  }

  _buildProductList(Device device) {
    return device.propertyVoList
            ?.map((propertyVo) => CardItemData(
                  title: S.current.text(propertyVo.name ?? ''),
                  titleFlex: 2,
                  contentFlex: 3,
                  contentFontSize: 12.sp,
                  content:
                      '${propertyVo.enumType == true ? S.current.text(propertyVo.value ?? '') : (propertyVo.value ?? "-")}${propertyVo.unit ?? ''}',
                  showIcon: false,
                ))
            .toList() ??
        [];
  }

  List<Column> _buildProductGroup() {
    return deviceModel?.devices
            ?.map((device) => Column(
                  children: [
                    SizedBox(height: 11.h),
                    CustomCardList(
                      icon: device.icon,
                      headerTitle: device.productKey != null
                          ? S.current.text(
                              'device.system_all_in_one_machine_detail_${device.productKey}')
                          : null,
                      cardList: _buildProductList(device),
                    ),
                  ],
                ))
            .toList() ??
        [];
  }

  Widget _buildNotifications(int messageCount) {
    double countWidth = messageCounts > 0 && messageCounts <= 9
        ? 20
        : messageCounts > 99
            ? 32
            : 26;
    double countHeight = 20;
    return messageCounts > 0
        ? Positioned(
            left: 10,
            top: -7,
            child: Container(
              height: countHeight.h,
              width: countWidth.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: ColorsUtil.themeColor,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: ColorsUtil.themeColor, width: 1)),
              child: Text(
                messageCount > 99 ? '99+' : messageCounts.toString(),
                style: TextStyle(
                    fontSize: 11.sp, color: ColorsUtil.buttonTextColor),
              ),
            ))
        : const SizedBox();
  }

  ///跳转WIFI-Setting
  void _navToWifiSetting(String sn) {
    Navigator.of(context).push(DiyRoutes.onGenerateRoute(
        RouteSettings(name: DiyRouteType.diyRoute, arguments: {
      'sn': sn,
      'modelKey': '',
      'systemCount': 0,
      'gridStandard': false,
      'wifiConfig': true,
    })));
  }

  void _bluetoothConnectDevice() {
    PermissionUtils.checkBluetoothPermission(onSuccess: () {
      if (MqttManager.instance.gw_sn.isNotEmpty) {
        BlocProvider.of<IntergratedFetchBloc>(context)
            .add(ConnectDeviceEvent(sn: MqttManager.instance.gw_sn));
      }
    }, onFailed: () {
      DialogUtils.connectBleDeviceFailureDialog(context);
    });
  }
}
