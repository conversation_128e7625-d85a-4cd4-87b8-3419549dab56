// ignore_for_file: use_build_context_synchronously

import 'dart:math';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter_basic/components/empty_view.dart';
import 'package:flutter_basic/components/simple_easy_refresher.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/router/devices_feature_routes/devices_feature_routes.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../components/cus_InkWell/CusInkWell.dart';
import '../../../../../generated/l10n.dart';
import '../../../../../platform/platform.dart';
import '../../../../../repositories/device_repository/model/alarm_fault_extra_request.dart';
import '../../../../../repositories/device_repository/model/device_alarm_fault_response.dart';
import '../../utils/alarm_utils.dart';
import '../bloc/device_alarm_bloc.dart';
import '../bloc/device_alarm_event.dart';
import '../bloc/device_alarm_state.dart';

class DeviceAlarmPage extends StatefulWidget {
  final List<AlarmRequestSearch>? devices;

  const DeviceAlarmPage({this.devices, super.key});

  @override
  State<StatefulWidget> createState() => _DeviceAlarmPageState();
}

class _DeviceAlarmPageState extends State<DeviceAlarmPage>
    with AutomaticKeepAliveClientMixin {
  late EasyRefreshController _controller;

  DeviceAlarmFaultResponse? preData;

  /// 是否已经加载完成
  bool isLoaded = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _controller = EasyRefreshController(
        controlFinishRefresh: true, controlFinishLoad: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  final List<DeviceAlarmFaultItemResponse> _dataList = [];
  AlarmRequestExtra extra = AlarmRequestExtra(alarmType: 1);

  Future<void> _refresh() async {
    BlocProvider.of<DeviceAlarmBloc>(context).add(
      DeviceAlarmRequestRefreshEvent(
        pageNo: 1,
        search: widget.devices,
        nextToken: preData?.nextToken,
        extra: extra,
      ),
    );
  }

  Future<void> _loadMore() async {
    BlocProvider.of<DeviceAlarmBloc>(context).add(
      DeviceAlarmRequestLoadMoreEvent(
        pageNo: preData!.pageNo! + 1,
        search: widget.devices,
        nextToken: preData?.nextToken,
        extra: extra,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocListener<DeviceAlarmBloc, DeviceAlarmState>(
      listener: (context, state) {
        if (state is DeviceAlarmFilterInProgressState) {
          _controller.callRefresh();
        } else if (state is DeviceAlarmSussState) {
          isLoaded = true;
          _handleResponse(state);
        } else if (state is DeviceAlarmRefreshFailState) {
          // 刷新失败
          _controller.finishRefresh(IndicatorResult.fail);
        } else if (state is DeviceAlarmLoadMoreFailState) {
          _controller.finishLoad(IndicatorResult.fail);
        }
      },
      child: SimpleEasyRefresher(
        easyRefreshController: _controller,
        onRefresh: _refresh,
        onLoad: _loadMore,
        header: const MaterialHeader(color: Colors.grey),
        childBuilder: (context, physics) {
          return ListView.separated(
            physics: physics,
            itemCount: max(1, _dataList.length),
            separatorBuilder: (context, index) {
              return Container(
                color: ColorsUtil.dividerColor,
                height: 1.h,
              );
            },
            itemBuilder: (context, index) {
              if (!isLoaded) return const SizedBox.shrink();
              if (_dataList.isEmpty) {
                return Padding(
                  padding:
                      const EdgeInsets.only(top: 150, left: 20, right: 20).h,
                  child: EmptyView(label: $t('notice.alarm_is_empty')),
                );
              }
              return CusInkWell(
                onTap: () => {
                  Navigator.of(context).push(
                    IntergratedRoutes.onGenerateRoute(
                      RouteSettings(
                        name: IntergratedRoutesType.deviceAlarmAndFaultDetail,
                        arguments: {'id': _dataList[index].id},
                      ),
                    ),
                  )
                },
                child: _getItem(_dataList[index]),
              );
            },
          );
        },
      ),
    );
  }

  Widget _getItem(DeviceAlarmFaultItemResponse item) {
    return Container(
      padding:
          EdgeInsets.only(left: 20.w, right: 20.w, top: 15.w, bottom: 19.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                  child: Text(
                '${item.alarmName} ${getLevel(item)}',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: ColorsUtil.textColor,
                  fontWeight: FontWeight.w500,
                ),
              )),
              Text(
                S.current.text(
                  item.alarmStatus == DEVICE_ALARM_STATUE_2
                      ? 'device.alarm_fault_status_Recovered'
                      : 'device.alarm_fault_status_InProgress',
                ),
                style: TextStyle(
                  fontSize: 12.sp,
                  color: item.alarmStatus == DEVICE_ALARM_STATUE_2
                      ? ColorsUtil.alarmRecoveredColor
                      : ColorsUtil.alarmInProgressColor,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 3.h,
          ),
          Text(
            item.alarmOccurTime ?? '',
            style: TextStyle(
              fontSize: 10.sp,
              color: ColorsUtil.hintColor,
            ),
          ),
          SizedBox(
            height: 5.h,
          ),
          Text(
            item.alarmDescription ?? '',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 12.sp,
              color: ColorsUtil.hintColor,
            ),
          ),
        ],
      ),
    );
  }

  void _handleResponse(DeviceAlarmSussState state) {
    int pageNo = state.result.pageNo ?? 1;
    preData = state.result;

    /// 刷新
    if (pageNo == 1) {
      _dataList.clear();
      _controller.finishRefresh(IndicatorResult.success);
    }
    if (pageNo >= (state.result.totalPage ?? 0)) {
      _controller.finishLoad(IndicatorResult.noMore);
    } else {
      _controller.finishLoad(IndicatorResult.success);
    }
    _dataList.addAll(state.result.data ?? []);
    setState(() {});
  }

  String getLevel(DeviceAlarmFaultItemResponse item) {
    return '(${item.alarmLevel})';
  }
}
