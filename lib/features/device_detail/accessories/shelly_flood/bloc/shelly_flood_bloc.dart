import 'package:equatable/equatable.dart';
import 'package:flutter_basic/event/update_device_name_event.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/config/system_type.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/event_bus_manager.dart';
import 'package:flutter_basic/repositories/device_repository/device_repository.dart';
import 'package:flutter_basic/repositories/device_repository/model/device_shelly_flood_response.dart';
import 'package:flutter_basic/repositories/system_repository/system_client_repository.dart';
import 'package:intl/intl.dart';

part 'shelly_flood_event.dart';
part 'shelly_flood_state.dart';

class ShellyFloodBloc extends Bloc<ShellyFloodEvent, ShellyFloodState> {
  final String deviceSn;

  ShellyFloodBloc(this.deviceSn)
      : super(ShellyFloodState(
          rainSensorEnabled: true, // Default rain sensor to On
          autoPvShutdownEnabled: false, // Default auto PV shutdown to Off
        )) {
    on<GetShellyFloodDetailsEvent>(
        (event, emit) => _getShellyFloodDetails(emit));
    on<GetWarningCountEvent>((event, emit) => _getWarningCount(emit));
    on<RemoveDeviceEvent>((event, emit) => _removeDevice(emit));
    on<SetDeviceNameEvent>(_setDeviceName);
    on<UpdateRefreshTimeEvent>(_updateRefreshTime);
    on<SetMuteEvent>(_setMute);
    on<SetRainSensorEvent>(_setRainSensor);
    on<SetAutoPvShutdownEvent>(_setAutoPvShutdown);
    add(UpdateRefreshTimeEvent(
        DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())));
  }

  /// 设置静音
  _setMute(SetMuteEvent event, Emitter<ShellyFloodState> emit) async {
    await _setParams(
      'silentSet',
      // 1：设置静音 其他：无效指令
      "1",
      emit,
    );
  }

  /// 设置雨量传感器
  _setRainSensor(
      SetRainSensorEvent event, Emitter<ShellyFloodState> emit) async {
    emit(state.copyWith(rainSensorEnabled: event.enabled));
    // TODO: 实现雨量传感器控制逻辑
    // await _setParams('rainSensorSet', event.enabled ? "1" : "0", emit);
  }

  /// 设置自动关闭光伏系统
  _setAutoPvShutdown(
      SetAutoPvShutdownEvent event, Emitter<ShellyFloodState> emit) async {
    emit(state.copyWith(autoPvShutdownEnabled: event.enabled));
    // TODO: 实现自动关闭光伏系统控制逻辑
    // await _setParams('autoPvShutdownSet', event.enabled ? "1" : "0", emit);
  }

  /// 更新刷新时间
  _updateRefreshTime(
      UpdateRefreshTimeEvent event, Emitter<ShellyFloodState> emit) {
    emit(state.copyWith(refreshTime: event.refreshTime));
  }

  /// 设置设备名称
  _setDeviceName(
      SetDeviceNameEvent event, Emitter<ShellyFloodState> emit) async {
    emit(state.copyWith(nameStatus: ActionStatus.loading));
    try {
      await DeviceClientRepository.instance.updateDevice(deviceSn, event.name);
      emit(state.copyWith(
        nameStatus: ActionStatus.success,
        isDeviceNameUpdated: true,
      ));
      EventBusManager.eventBus
          .fire(UpdateDeviceNameEvent(deviceSn, event.name));
      await _getShellyFloodDetails(emit, false);
    } catch (e) {
      logger.e('设置设备名称失败 $e');
      emit(state.copyWith(
        nameStatus: ActionStatus.failure,
        errorMessage: 'common.operateFail',
      ));
    } finally {
      emit(state.copyWith(nameStatus: ActionStatus.initial));
    }
  }

  /// 获取设备详情
  _getShellyFloodDetails(Emitter<ShellyFloodState> emit,
      [bool showLoading = true]) async {
    if (showLoading) emit(state.copyWith(status: ActionStatus.loading));
    try {
      ShellyFloodResponse details = await DeviceClientRepository.instance
          .getShellyFloodDeviceDetails(deviceSn);
      // 如果是本地模式，需要通过蓝牙获取设备的状态
      if (LocalModeManger.instance.isLocalModeNow) {
        details = await _getDetailsFromLocalMode(details);
      }
      emit(state.copyWith(
          isDetailsLoaded: true,
          status: ActionStatus.success,
          details: details));
    } catch (e) {
      logger.e('获取设备详情失败 $e');
      emit(state.copyWith(status: ActionStatus.failure));
    } finally {
      emit(state.copyWith(status: ActionStatus.initial));
    }
  }

  /// 获取告警数量
  _getWarningCount(Emitter<ShellyFloodState> emit) async {
    try {
      int count = await DeviceClientRepository.instance
          .getDeviceAlarmNumBySystemId(deviceSn);
      emit(state.copyWith(warningCount: count));
    } catch (e) {
      logger.e('获取告警数量失败 $e');
    }
  }

  /// 删除设备
  _removeDevice(Emitter<ShellyFloodState> emit) async {
    emit(state.copyWith(deleteStatus: ActionStatus.loading));
    try {
      await DeviceClientRepository.instance
          .removeDevice(deviceSn, DeviceComponent.Flood);
      emit(state.copyWith(
        deleteStatus: ActionStatus.success,
        isDeviceRemoved: true,
      ));
    } catch (e) {
      logger.e('删除设备失败 $e');
      emit(state.copyWith(
        deleteStatus: ActionStatus.failure,
        errorMessage: 'common.operateFail',
      ));
    } finally {
      emit(state.copyWith(deleteStatus: ActionStatus.initial));
    }
  }

  /// 设置参数
  _setParams(String key, String value, Emitter<ShellyFloodState> emit) async {
    emit(state.copyWith(controlStatus: ActionStatus.loading));
    try {
      await SystemClientRepository.instance.invoke(
        deviceSn,
        'HB-FLOOD-MODEL',
        key,
        'HB-FLOOD',
        value,
      );
      emit(state.copyWith(controlStatus: ActionStatus.success));
      await _getShellyFloodDetails(emit, false);
    } catch (e) {
      logger.e('设置参数失败 $e');
      emit(state.copyWith(
        controlStatus: ActionStatus.failure,
        errorMessage: 'common.operateFail',
      ));
    } finally {
      emit(state.copyWith(controlStatus: ActionStatus.initial));
    }
  }

  /// 本地模式下获取设备详情
  Future<ShellyFloodResponse> _getDetailsFromLocalMode(
      ShellyFloodResponse details) async {
    // 如果是本地模式设置父设备状态为在线
    details.parentDeviceStatus = OnlineStatus.online;
    return details;
  }
}
