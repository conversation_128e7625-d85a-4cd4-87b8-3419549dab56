part of 'shelly_flood_bloc.dart';

sealed class ShellyFloodEvent extends Equatable {
  const ShellyFloodEvent();

  @override
  List<Object> get props => [];
}

class GetShellyFloodDetailsEvent extends ShellyFloodEvent {}

class GetWarningCountEvent extends ShellyFloodEvent {}

class RemoveDeviceEvent extends ShellyFloodEvent {}

/// 修改设备名称
class SetDeviceNameEvent extends ShellyFloodEvent {
  final String name;

  const SetDeviceNameEvent(this.name);

  @override
  List<Object> get props => [name];
}

class UpdateRefreshTimeEvent extends ShellyFloodEvent {
  final String refreshTime;

  const UpdateRefreshTimeEvent(this.refreshTime);

  @override
  List<Object> get props => [refreshTime];
}

class SetMuteEvent extends ShellyFloodEvent {}

/// 设置雨量传感器开关
class SetRainSensorEvent extends ShellyFloodEvent {
  final bool enabled;

  const SetRainSensorEvent(this.enabled);

  @override
  List<Object> get props => [enabled];
}

/// 设置自动关闭光伏系统开关
class SetAutoPvShutdownEvent extends ShellyFloodEvent {
  final bool enabled;

  const SetAutoPvShutdownEvent(this.enabled);

  @override
  List<Object> get props => [enabled];
}
