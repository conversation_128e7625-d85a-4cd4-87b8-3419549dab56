part of 'shelly_flood_bloc.dart';

enum ActionStatus {
  initial,
  loading,
  success,
  failure,
}

class ShellyFloodState extends Equatable {
  final ActionStatus status;
  final String? errorMessage;
  final bool isDeviceRemoved;
  final bool isDeviceNameUpdated;
  final bool isDetailsLoaded;
  final ActionStatus nameStatus;
  final ActionStatus deleteStatus;
  final ActionStatus controlStatus;
  final ShellyFloodResponse? details;
  final String refreshTime;
  final int warningCount;
  final bool? rainSensorEnabled;
  final bool? autoPvShutdownEnabled;

  ShellyFloodState({
    this.status = ActionStatus.initial,
    this.errorMessage,
    this.isDeviceRemoved = false,
    this.isDeviceNameUpdated = false,
    this.isDetailsLoaded = false,
    this.nameStatus = ActionStatus.initial,
    this.deleteStatus = ActionStatus.initial,
    this.controlStatus = ActionStatus.initial,
    this.refreshTime = '',
    this.details,
    this.warningCount = 0,
    this.rainSensorEnabled,
    this.autoPvShutdownEnabled,
  });

  ShellyFloodState copyWith({
    ActionStatus? status,
    String? errorMessage,
    bool? isDeviceRemoved,
    bool? isDeviceNameUpdated,
    ActionStatus? nameStatus,
    bool? isDetailsLoaded,
    ActionStatus? deleteStatus,
    ActionStatus? controlStatus,
    String? refreshTime,
    ShellyFloodResponse? details,
    int? warningCount,
    bool? rainSensorEnabled,
    bool? autoPvShutdownEnabled,
  }) {
    return ShellyFloodState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      isDeviceRemoved: isDeviceRemoved ?? this.isDeviceRemoved,
      isDeviceNameUpdated: isDeviceNameUpdated ?? this.isDeviceNameUpdated,
      isDetailsLoaded: isDetailsLoaded ?? this.isDetailsLoaded,
      nameStatus: nameStatus ?? this.nameStatus,
      deleteStatus: deleteStatus ?? this.deleteStatus,
      controlStatus: controlStatus ?? this.controlStatus,
      refreshTime: refreshTime ?? this.refreshTime,
      details: details ?? this.details,
      warningCount: warningCount ?? this.warningCount,
      rainSensorEnabled: rainSensorEnabled ?? this.rainSensorEnabled,
      autoPvShutdownEnabled:
          autoPvShutdownEnabled ?? this.autoPvShutdownEnabled,
    );
  }

  @override
  List<Object?> get props => [
        status,
        errorMessage,
        isDeviceRemoved,
        isDeviceNameUpdated,
        isDetailsLoaded,
        nameStatus,
        deleteStatus,
        controlStatus,
        refreshTime,
        details,
        warningCount,
        rainSensorEnabled,
        autoPvShutdownEnabled,
      ];
}
