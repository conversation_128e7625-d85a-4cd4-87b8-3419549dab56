import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/components/custom_card_list.dart';
import 'package:flutter_basic/components/custom_dialog_box.dart';
import 'package:flutter_basic/components/custom_switch.dart';
import 'package:flutter_basic/components/delete_button.dart';
import 'package:flutter_basic/components/offline_widget.dart';
import 'package:flutter_basic/components/page_loading.dart';
import 'package:flutter_basic/features/device_detail/accessories/smart_socket/page/modify_name_page.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/repositories/device_repository/model/device_shelly_flood_response.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../bloc/shelly_flood_bloc.dart';

class ShellyFlood extends StatefulWidget {
  const ShellyFlood({super.key});

  @override
  State<StatefulWidget> createState() => _ShellyFloodState();
}

class _ShellyFloodState extends State<ShellyFlood> {
  late final bloc = BlocProvider.of<ShellyFloodBloc>(context);
  bool _isLoadingShown = false;

  @override
  void initState() {
    super.initState();
    context.read<ShellyFloodBloc>().add(GetShellyFloodDetailsEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ShellyFloodBloc, ShellyFloodState>(
      listener: (context, state) {
        _handleLoadingState(context, state);
        _handleOperationResult(context, state);
        _handleDeleteResult(context, state);
      },
      child: Scaffold(
        backgroundColor: ColorsUtil.backgroundColor,
        appBar: CustomAppBar(
          title: AppBarCenterText(
            title: S.current.text('device.shelly_flood_details_page_title'),
          ),
          actions: const [],
        ),
        body: BlocBuilder<ShellyFloodBloc, ShellyFloodState>(
          builder: (context, state) {
            if (state.status == ActionStatus.loading) {
              return const PageLoading();
            }
            return SafeArea(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                          left: 20.w, right: 20.w, top: 10.w, bottom: 10.w),
                      child: Column(children: [
                        if (bloc.state.details?.isOffline == true)
                          const OfflineWidget(),
                        SizedBox(height: 10.w),
                        CustomCardList(
                          sectionTitle:
                              S.current.text('device.ct_details_basic_info'),
                          cardList: [
                            CardItemData(
                              title: S.current.text('device.Device_Name'),
                              onTap: _showEditName,
                              content: context.select((ShellyFloodBloc bloc) =>
                                  bloc.state.details?.deviceName ?? '-'),
                            ),
                            // 雨量传感器
                            CardItemData(
                              showIcon: false,
                              title: S.current.text('device.rain_sensor'),
                              child: Padding(
                                padding: EdgeInsets.only(right: 15.w),
                                child: CustomSwitch(
                                  disabled:
                                      bloc.state.details?.isOffline == true,
                                  onToggle: (value) {
                                    bloc.add(SetRainSensorEvent(value));
                                  },
                                  value: context.select(
                                      (ShellyFloodBloc bloc) =>
                                          bloc.state.rainSensorEnabled ?? true),
                                ),
                              ),
                            ),
                            // 浸水检测 (Flood detection status)
                            CardItemData(
                              showIcon: false,
                              title: S.current.text('device.flood_detection'),
                              content: context.select((ShellyFloodBloc bloc) {
                                var status = bloc.state.details?.alarmStatus;
                                if (status == null) return '-';
                                return S.current.text(
                                    'device.flood_alarm_status_${status.name}');
                              }),
                              contentColor:
                                  context.select((ShellyFloodBloc bloc) {
                                var status = bloc.state.details?.alarmStatus;
                                if (status == null) return null;
                                return status == FloodAlarmStatus.alarm
                                    ? ColorsUtil.redTextColor
                                    : null;
                              }),
                            ),
                            // 监测浸水后，自动关闭阳台光伏系统 (Auto close PV system after detecting flood)
                            CardItemData(
                              showIcon: false,
                              title: S.current.text(
                                  'device.auto_close_pv_system_after_flood'),
                              child: Padding(
                                padding: EdgeInsets.only(right: 15.w),
                                child: CustomSwitch(
                                  disabled:
                                      bloc.state.details?.isOffline == true,
                                  onToggle: (value) {
                                    bloc.add(SetAutoPvShutdownEvent(value));
                                  },
                                  value: context.select(
                                      (ShellyFloodBloc bloc) =>
                                          bloc.state.autoPvShutdownEnabled ??
                                          false),
                                ),
                              ),
                            ),
                            // 电池电量 (Battery level)
                            CardItemData(
                                showIcon: false,
                                title: S.current.text('device.battery_level'),
                                content: context.select((ShellyFloodBloc bloc) {
                                  final battery = bloc.state.details?.battery;
                                  return battery != null ? '${battery}%' : '-';
                                }),
                                hint: context.select((ShellyFloodBloc bloc) {
                                  final battery = bloc.state.details?.battery;
                                  if (battery == null) return null;
                                  final batteryLevel =
                                      int.tryParse(battery) ?? 100;
                                  return batteryLevel <= 15
                                      ? S.current
                                          .text('device.smoke_battery_low')
                                      : null;
                                }),
                                hintColor: ColorsUtil.redTextColor),
                            // 电池电压 (Battery voltage)
                            CardItemData(
                              showIcon: false,
                              title: S.current.text('device.battery_voltage'),
                              content: context.select((ShellyFloodBloc bloc) {
                                final voltage = bloc.state.details?.voltage;
                                return voltage != null ? '${voltage}V' : '-';
                              }),
                            )
                          ],
                        ),
                        SizedBox(height: 10.w),
                        DeleteButton(
                          margin: EdgeInsets.only(top: 20.w, bottom: 20.w),
                          text:
                              S.current.text('device.ct_details_remove_device'),
                          onTap: _clickRemove,
                        ),
                        BlocBuilder<ShellyFloodBloc, ShellyFloodState>(
                          builder: (context, state) {
                            return Text(
                              '${S.current.text('trend.integrated_updated')} ${state.refreshTime}',
                              style: TextStyle(
                                  color: ColorsUtil.assistTextColor,
                                  fontSize: 12.sp),
                            );
                          },
                        ),
                        SizedBox(height: 10.w),
                      ]),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 修改名称
  void _showEditName() => Navigator.of(context).push(
        MaterialPageRoute(builder: (context) {
          return ModifyNamePage(
            title: 'device.plug_device_name',
            value: bloc.state.details?.deviceName ?? "",
            onSubmitted: (v) => bloc.add(SetDeviceNameEvent(v)),
          );
        }),
      );

  /// 点击删除
  void _clickRemove() {
    showDialog(
      context: context,
      builder: (context) {
        return CustomInputDialogBox(
          title: S.current.text('device.ct_details_remove_device'),
          content: S.current.text('device.ct_details_remove_device_confirm'),
          actions: [
            CustomInputButton(
              text: S.current.text('common.cancel'),
              onTap: () => Navigator.of(context).pop(),
            ),
            CustomInputButton(
              text: S.current.text('common.confirm'),
              textColor: ColorsUtil.highlightTextColor,
              onTap: () {
                Navigator.of(context).pop();
                bloc.add(RemoveDeviceEvent());
              },
            ),
          ],
        );
      },
    );
  }

  /// 处理加载状态
  void _handleLoadingState(BuildContext context, ShellyFloodState state) {
    bool shouldShowLoading = state.nameStatus == ActionStatus.loading ||
        state.deleteStatus == ActionStatus.loading ||
        state.controlStatus == ActionStatus.loading;

    if (shouldShowLoading && !_isLoadingShown) {
      _isLoadingShown = true;
      CustomLoading.showLoading(null);
    } else if (!shouldShowLoading && _isLoadingShown) {
      _isLoadingShown = false;
      CustomLoading.dismissLoading();
    }
  }

  /// 处理操作结果
  void _handleOperationResult(BuildContext context, ShellyFloodState state) {
    // 确保在显示Toast前先关闭loading
    if (state.nameStatus == ActionStatus.success ||
        state.nameStatus == ActionStatus.failure ||
        state.controlStatus == ActionStatus.success ||
        state.controlStatus == ActionStatus.failure) {
      if (_isLoadingShown) {
        _isLoadingShown = false;
        CustomLoading.dismissLoading();
      }
    }

    // 延迟一下再显示Toast，确保loading已经关闭
    if (state.nameStatus == ActionStatus.success) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          CustomToast.showToast(
              context, S.current.text('device.ct_details_update_name_success'));
        }
      });
    } else if (state.nameStatus == ActionStatus.failure) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          CustomToast.showToast(context,
              S.current.text(state.errorMessage ?? 'common.operateFail'));
        }
      });
    }

    if (state.controlStatus == ActionStatus.success) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          CustomToast.showToast(
              context, S.current.text('device.ct_details_operation_success'));
        }
      });
    } else if (state.controlStatus == ActionStatus.failure) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          CustomToast.showToast(context,
              S.current.text(state.errorMessage ?? 'common.operateFail'));
        }
      });
    }
  }

  /// 处理删除结果
  void _handleDeleteResult(BuildContext context, ShellyFloodState state) {
    if (state.isDeviceRemoved) {
      // 确保loading已关闭再显示toast
      if (_isLoadingShown) {
        _isLoadingShown = false;
        CustomLoading.dismissLoading();
      }
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          CustomToast.showToast(context,
              S.current.text('device.ct_details_remove_device_success'));
          Navigator.of(context).pop();
        }
      });
    } else if (state.deleteStatus == ActionStatus.failure) {
      if (_isLoadingShown) {
        _isLoadingShown = false;
        CustomLoading.dismissLoading();
      }
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          CustomToast.showToast(context,
              S.current.text(state.errorMessage ?? 'common.operateFail'));
        }
      });
    }
  }
}
