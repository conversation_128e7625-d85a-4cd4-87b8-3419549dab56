import 'package:flutter/material.dart';
import 'package:flutter_basic/components/bottom_button.dart';
import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/components/cus_InkWell/CusInkWell.dart';
import 'package:flutter_basic/components/custom_card.dart';
import 'package:flutter_basic/features/diy_new/accessories/model/accessorise_device_filter.dart';
import 'package:flutter_basic/features/monitor/bloc/monitor_fetch_bloc.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 相位反转设置页面
class PhaseReversePage extends StatefulWidget {
  final List<int> value;
  final AccessoriesDeviceType type;
  const PhaseReversePage({
    super.key,
    required this.value,
    required this.type,
  });

  @override
  State<PhaseReversePage> createState() => _PhaseReversePageState();
}

class _PhaseReversePageState extends State<PhaseReversePage> {
  late List<int> value = widget.value;

  List get list {
    // 获取设备工厂型号以判断是否为二代DIY设备
    final factoryStr = context
            .read<MonitorFetchBloc>()
            .state
            .monitorModel
            ?.systemVO
            ?.factoryModel ??
        "";
    switch (widget.type) {
      case AccessoriesDeviceType.ct:
        return [ShellyCtChannelType.ab];
      case AccessoriesDeviceType.ct3:
        return ShellyCtPhaseType.valuesFor(AccessoriesDeviceType.ct3,
            factoryStr: factoryStr);
      default:
        throw Exception('未知的设备类型');
    }
  }

  @override
  Widget build(BuildContext context) {
    var tipsStyle = TextStyle(color: ColorsUtil.contentColor, fontSize: 13.sp);
    var tips = Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            SizedBox(width: 20.w, child: Text('i. ', style: tipsStyle)),
            Expanded(
                child: Text(
              S.current.text('device.ct_details_phase_reverse_select_tips_1'),
              style: tipsStyle,
            ))
          ]),
          SizedBox(height: 5.w),
          Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            SizedBox(width: 20.w, child: Text('ii. ', style: tipsStyle)),
            Expanded(
                child: Text(
              S.current.text('device.ct_details_phase_reverse_select_tips_2'),
              style: tipsStyle,
            ))
          ]),
        ],
      ),
    );

    return Scaffold(
      backgroundColor: ColorsUtil.backgroundColor,
      appBar: CustomAppBar(
        title: AppBarCenterText(
          title: S.current.text('device.ct_details_phase_reverse_select_title'),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomCard(
                  margin: EdgeInsets.fromLTRB(20.w, 10.w, 20.w, 10.w),
                  child: Column(mainAxisSize: MainAxisSize.min, children: [
                    for (var type in list) ...[
                      _buildItem(type),
                      if (type.index != list.length - 1)
                        Divider(
                          height: 1,
                          color: ColorsUtil.dividerColor,
                          indent: 20.w,
                        )
                    ]
                  ]),
                ),
                tips,
              ],
            ),
          ),
          SafeArea(
            child: BottomButtonWidget(
              buttonType: BottomButtonType.save,
              text: S.current.text('common.done'),
              onPressed: () {
                Navigator.of(context).pop(value);
              },
            ),
          )
        ],
      ),
    );
  }

  Widget _buildItem(type) {
    bool isSelected = value[type.value - 1] == 1;
    return CusInkWell(
      onTap: () => setState(() => value[type.value - 1] = isSelected ? 0 : 1),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.w),
        child: Row(
          children: [
            Expanded(child: Text(S.current.text(type.name))),
            CustomImageAsset(
              isSelected ? 'icon_checked_theme' : 'icon_un_checked',
              size: 18.w,
            )
          ],
        ),
      ),
    );
  }
}
