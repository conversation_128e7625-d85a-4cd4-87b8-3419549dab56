// ignore_for_file: constant_identifier_names

import 'package:flutter_basic/platform/utils/version.dart';
import 'package:flutter_basic/router/diy_feature_routes/diy_route_type.dart';

import '../../../../generated/l10n.dart';

class AccessoriseDeviceFilter {
  /// 插座
  static const SHELLY = 'ShellyPlusPlugS';

  static const SHELLYSG3 = 'ShellyPlugSG3';

  /// 温湿度传感器
  static const SHELLYHTG3 = 'ShellyHTG3';

  /// CT 三路
  static const ShellyCt3 = 'ShellyPro3EM';

  /// CT 双向
  static const ShellyCt = 'ShellyProEM50';

  /// 烟感
  static const ShellySmoke = 'ShellyPlusSmoke';

  /// 水浸
  static const ShellyFlood = 'ShellyFlood';

  static List<String> values() {
    return [
      SHELLY,
      SHELLYSG3,
      SHELLYHTG3,
      ShellyCt3,
      ShellyCt,
      ShellySmoke,
      ShellyFlood,
    ];
  }
}

/// 辅件设备类型
enum AccessoriesDeviceType {
  /// 插座
  plug(
    AccessoriseDeviceFilter.SHELLY,
    'icon_shelly_plug',
    DiyRouteType.diyShellyRoute,
  ),
  plug3(
    AccessoriseDeviceFilter.SHELLYSG3,
    'icon_shelly_plug',
    DiyRouteType.diyShellyRoute,
  ),

  /// 温湿度传感器
  htg3(
    AccessoriseDeviceFilter.SHELLYHTG3,
    'icon_shelly_htg3',
    DiyRouteType.diyShellyRoute,
  ),

  /// CT 三路
  ct3(
    AccessoriseDeviceFilter.ShellyCt3,
    'icon_shelly_ct3',
    DiyRouteType.diyShellyCtRoute,
  ),

  /// CT 双向
  ct(
    AccessoriseDeviceFilter.ShellyCt,
    'icon_shelly_ct',
    DiyRouteType.diyShellyCtRoute,
  ),

  /// 烟感
  smoke(
    AccessoriseDeviceFilter.ShellySmoke,
    'icon_shelly_smoke',
    DiyRouteType.diyShellySmokeRoute,
  ),

  /// 水浸
  flood(
    AccessoriseDeviceFilter.ShellyFlood,
    'icon_shelly_flood',
    DiyRouteType.diyShellyFloodRoute,
  );

  final String key;
  final String photo;
  final String routeName;

  const AccessoriesDeviceType(this.key, this.photo, this.routeName);

  static parse(String key) {
    switch (key) {
      case AccessoriseDeviceFilter.SHELLY:
        return plug;
      case AccessoriseDeviceFilter.SHELLYSG3:
        return plug3;
      case AccessoriseDeviceFilter.ShellyCt3:
        return ct3;
      case AccessoriseDeviceFilter.ShellyCt:
        return ct;
      case AccessoriseDeviceFilter.ShellySmoke:
        return smoke;
      case AccessoriseDeviceFilter.SHELLYHTG3:
        return htg3;
      case AccessoriseDeviceFilter.ShellyFlood:
        return flood;
      default:
        return plug;
    }
  }
}

/// 相位类型
enum ShellyCtPhaseType {
  a(1, 'A'),
  b(2, 'B'),
  c(3, 'C'),
  t(4, 'T');

  final int value;
  final String name;

  const ShellyCtPhaseType(this.value, this.name);

  // 加上多语言翻译
  String get translatedName => S.current.text("diyNew.shelly_ct_phase_$name");

  // 获取可用的相位类型列表，二代CT设备才显示T相
  static List<ShellyCtPhaseType> valuesFor(AccessoriesDeviceType deviceType,
      {String? factoryStr}) {
    // 使用现有的isDIY2方法检查是否是二代DIY设备
    bool isDiy2Device = factoryStr != null ? isDIY2(factoryStr) : false;
    if (isDiy2Device) {
      // 二代CT设备，显示所有相位类型，包括T相
      return ShellyCtPhaseType.values.toList();
    } else {
      // 其他CT设备，不显示T相
      return [ShellyCtPhaseType.a, ShellyCtPhaseType.b, ShellyCtPhaseType.c];
    }
  }
}

enum ShellyCtChannelType {
  a(1, 'A'),
  b(2, 'B'),
  ab(3, 'A+B');

  final int value;
  final String name;

  const ShellyCtChannelType(this.value, this.name);

  // 加上多语言翻译
  String get translatedName => name;
}

/// 配置完成后 蓝牙操作类型
enum ShellyCtAfterBluetoothType {
  close(1, 'diyNew.shelly_dc_after_bluetooth_close'),
  open(2, 'diyNew.shelly_dc_after_bluetooth_open');

  final int value;
  final String name;

  const ShellyCtAfterBluetoothType(this.value, this.name);
}
