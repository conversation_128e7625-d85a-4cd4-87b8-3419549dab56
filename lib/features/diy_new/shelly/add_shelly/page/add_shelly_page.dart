import 'dart:async';

import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/components/custom_card.dart';
import 'package:flutter_basic/components/custom_dialog_box.dart';
import 'package:flutter_basic/features/diy_new/accessories/model/accessorise_device_filter.dart';
import 'package:flutter_basic/features/diy_new/components/diy_new_system_sn.dart';
import 'package:flutter_basic/features/diy_new/components/water_ripples.dart';
import 'package:flutter_basic/features/diy_new/shelly/model/accessorise_device_info_model.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/platform/utils/device_status.dart';
import 'package:flutter_basic/router/diy_feature_routes/diy_route_type.dart';
import 'package:flutter_basic/router/diy_feature_routes/diy_routes.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../bloc/add_shelly_bloc.dart';
import '../bloc/add_shelly_event.dart';
import '../bloc/add_shelly_state.dart';

class AddShellyPage extends StatefulWidget {
  final String systemNo;
  final String systemId;
  final int status;
  final bool clustered;

  const AddShellyPage(
      {super.key,
      required this.systemNo,
      required this.systemId,
      required this.clustered,
      required this.status});

  @override
  State<StatefulWidget> createState() => _AddShellyPageState();
}

class _AddShellyPageState extends State<AddShellyPage> {
  Timer? _timer;
  static const num count = 60;
  num _timeCount = count;
  bool isScan = false;

  // List<BluetoothDevice> _systemDevices = [];
  List<ScanResult> _scanResults = [];
  List<AccessoriseDeviceInfoModel> _accessoriseList = [];
  bool _isScanning = false;
  late StreamSubscription<List<ScanResult>> _scanResultsSubscription;
  late StreamSubscription<bool> _isScanningSubscription;

  ///状态添加
  Timer? deviceStatusTimer;
  int deviceStatus = 0;

  ///检查设备是否在云端在线
  void _startGetCloudDeviceStatus() {
    if (deviceStatusTimer != null) {
      deviceStatusTimer?.cancel();
      deviceStatusTimer = null;
    }
    deviceStatusTimer =
        Timer.periodic(const Duration(milliseconds: 15000), (timer) {
      context
          .read<AddShellyBloc>()
          .add(SystemStatusEvent(systemNo: widget.systemNo));
    });
  }

  @override
  void initState() {
    super.initState();
    // 禁止熄屏
    WakelockPlus.enable();
    deviceStatus = widget.status;
    context
        .read<AddShellyBloc>()
        .add(GetMqttConfigEvent(systemNo: widget.systemNo));
    _startGetCloudDeviceStatus();
    startTimer();
    _scanResultsSubscription = FlutterBluePlus.scanResults.listen((results) {
      _scanResults = results.where((element) {
        // 安全地访问 platformName
        final platformName = element.device.platformName;
        if (platformName.isEmpty) return false;

        final parts = platformName.split('-');
        if (parts.isEmpty) return false;

        return AccessoriseDeviceFilter.values().contains(parts[0]);
      }).toList();
      context.read<AddShellyBloc>().add(CheckDeviceEvent(
            scanResults: _scanResults.map((e) => e.device).toList(),
          ));
    }, onError: (e) {});

    _isScanningSubscription = FlutterBluePlus.isScanning.listen((state) {
      _isScanning = state;
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    deviceStatusTimer?.cancel();
    _timer?.cancel();
    _scanResultsSubscription.cancel();
    _isScanningSubscription.cancel();
    isScan = false;
    _stopScan();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          _popPage();
          return false;
        },
        child: Scaffold(
            backgroundColor: ColorsUtil.backgroundColor,
            appBar: CustomAppBar(
              title: AppBarCenterText(
                title: S.current.text('diyNew.accessory_add_button'),
              ),
              defaultLeadingClicked: () {
                _popPage();
              },
              bottom: PreferredSize(
                preferredSize: Size.fromHeight(50.h),
                child: DiyNewSystemSn(
                  text: '',
                  deviceStatus: DeviceStatus.fromInt(deviceStatus),
                  systemNo: widget.systemNo,
                ),
              ),
            ),
            body: Column(
              children: [
                Expanded(
                  child: BlocConsumer<AddShellyBloc, AddShellyState>(
                    builder: (BuildContext context, AddShellyState state) {
                      return Column(
                        children: [
                          Container(
                            padding: EdgeInsets.only(top: 20.w, left: 20.w),
                            alignment: Alignment.centerLeft,
                            child: Text(
                              _accessoriseList.isEmpty
                                  ? S.current.text(
                                      'diyNew.add_accessorise_scan_empty_tips')
                                  : '${_accessoriseList.length} ${S.current.text('diyNew.add_accessory_scan_count')}',
                              style: TextStyle(
                                  fontSize: 14.sp, color: ColorsUtil.hintColor),
                            ),
                          ),
                          _buildScanView(),
                          Expanded(
                              child: Container(
                            child: _accessoriseList.isEmpty
                                ? _emptyView()
                                : Padding(
                                    padding: EdgeInsets.only(
                                        left: 20.w, right: 20.w),
                                    child: _buildDeviceList(),
                                  ),
                          ))
                        ],
                      );
                    },
                    listenWhen: (previous, current) {
                      return current is StartConnectBleState ||
                          current is MqttConfigSuccessState ||
                          current is MqttConfigFailureState ||
                          current is RequestStartState ||
                          current is RequestEndState ||
                          current is ShellyDeviceSuccessState ||
                          current is SystemStatusState ||
                          current is ConnectSuccessBleState ||
                          current is ConnectFailureBleState;
                    },
                    listener: (BuildContext context, AddShellyState state) {
                      if (state is StartConnectBleState) {
                        CustomLoading.showLoading('');
                      } else if (state is ConnectSuccessBleState) {
                        _navToShellyConfigPage(state);
                      } else if (state is ConnectFailureBleState) {
                        CustomLoading.dismissLoading();
                      } else if (state is RequestStartState) {
                        CustomLoading.showLoading('');
                      } else if (state is RequestEndState) {
                        CustomLoading.dismissLoading();
                      } else if (state is ShellyDeviceSuccessState) {
                        if (mounted) {
                          setState(() {
                            _accessoriseList = state.deviceList;
                          });
                        }
                      } else if (state is SystemStatusState) {
                        if (mounted) {
                          setState(() {
                            deviceStatus = state.status;
                          });
                        }
                      }
                    },
                  ),
                ),
                // SafeArea(
                //   child: Container(
                //     height: 60.h,
                //     padding: EdgeInsets.symmetric(horizontal: 20.w),
                //     child: BlocBuilder<AddShellyBloc, AddShellyState>(
                //       buildWhen: (previous, current) =>
                //           current is RequestStartState ||
                //           current is RequestEndState,
                //       builder: (context, state) {
                //         return BottomButtonWidget(
                //           buttonType: BottomButtonType.normal,
                //           text: S.current.text('diyNew.add_flood_button'),
                //           child: Row(
                //             mainAxisSize: MainAxisSize.min,
                //             children: [
                //               Text(S.current.text('diyNew.add_flood_button')),
                //             ],
                //           ),
                //           onPressed: () {
                //             _navigateToFloodTipPage();
                //           },
                //         );
                //       },
                //     ),
                //   ),
                // ),
              ],
            )));
  }

  _popPage() {
    var needShowDialog = false;
    for (AccessoriseDeviceInfoModel model in _accessoriseList) {
      if (!model.config) {
        needShowDialog = true;
      }
    }
    if (needShowDialog) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return CustomInputDialogBox(
              title: S.current.text('diyNew.delete_shelly_title'),
              content: S.current.text('diyNew.delete_shelly_content'),
              actions: [
                CustomInputButton(
                    text: S.current.text('common.cancel'),
                    onTap: () {
                      Navigator.of(context).pop();
                    }),
                CustomInputButton(
                    text: S.current.text('common.back'),
                    textColor: ColorsUtil.highlightTextColor,
                    onTap: () {
                      Navigator.of(context).pop(); // Close the dialog first
                      Navigator.of(context)
                          .pop(); // Then navigate back from the page
                    })
              ],
            );
          });
    } else {}
  }

  Widget _buildScanView() {
    return CustomCard(
        margin:
            EdgeInsets.only(left: 20.w, top: 10.w, bottom: 20.w, right: 20.w),
        child: SizedBox(
          height: 44.w,
          child: Row(
            children: [
              const WaterRipples(),
              SizedBox(
                width: 10.w,
              ),
              isScan
                  ? Text(S.current.text('diyNew.scanning'),
                      style: TextStyle(
                          fontSize: 13.sp, color: ColorsUtil.themeColor))
                  : Text(S.current.text('diyNew.scan'),
                      style: TextStyle(
                          fontSize: 13.sp, color: ColorsUtil.themeColor)),
              Expanded(child: Container()),
              isScan
                  ? Padding(
                      padding: EdgeInsets.only(right: 20.w),
                      child: Text('$_timeCount s',
                          style: TextStyle(
                              fontSize: 13.sp, color: ColorsUtil.hintColor)),
                    )
                  : Container(
                      // width: 90.w,
                      height: 24.w,
                      margin: EdgeInsets.only(right: 10.w),
                      child: ElevatedButton(
                          style: ButtonStyle(
                            backgroundColor: MaterialStateProperty.all(
                                ColorsUtil.themeColor),
                            shape: MaterialStateProperty.all(
                                RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(22.w))),
                            shadowColor: MaterialStateProperty.all(
                              ColorsUtil.transparentColor,
                            ),
                          ),
                          onPressed: () {
                            startTimer(needClean: true);
                          },
                          child: Text(
                            S.current.text('diyNew.rescan'),
                            style: TextStyle(
                                fontSize: 10.sp,
                                color: ColorsUtil.buttonTextColor),
                          )),
                    )
            ],
          ),
        ));
  }

  _emptyView() {
    return Column(
      children: [
        SizedBox(
          height: 55.h,
        ),
        CustomImageAsset(
          'icon_diy_new_empty',
          width: 65.w,
          height: 102.w,
        ),
        Text(
          S.current.text('diyNew.add_accessorise_scan_empty'),
          textAlign: TextAlign.center,
          style: TextStyle(
              fontSize: 13.sp, color: ColorsUtil.hintColor, height: 1.5),
        )
      ],
    );
  }

  ///辅件列表
  _buildDeviceList() {
    return GridView.builder(
        itemCount: _accessoriseList.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 10.w,
            mainAxisSpacing: 10.h,
            childAspectRatio: 1.3),
        itemBuilder: (context, index) {
          var item = _accessoriseList[index];
          return InkWell(
            onTap: () {
              // Check if this is a CT device and show reset dialog first
              if (item.type == AccessoriesDeviceType.ct ||
                  item.type == AccessoriesDeviceType.ct3) {
                _showResetConfirmationDialog(item);
              } else {
                // For non-CT devices, proceed as normal
                context
                    .read<AddShellyBloc>()
                    .add(ConnectBleDeviceEvent(item: item));
              }
            },
            child: CustomCard(
              child: Container(
                padding: EdgeInsets.only(
                    left: 10.w, right: 5.w, top: 8.w, bottom: 8.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomImageAsset(
                          item.type.photo,
                          width: 60.w,
                          height: 60.w,
                        ),
                        Expanded(child: Container()),
                        Container(
                            padding: EdgeInsets.only(
                                left: 8.w, right: 8.w, top: 2.w, bottom: 2.w),
                            decoration: BoxDecoration(
                                color: Colors.grey.withOpacity(0.1),
                                borderRadius: BorderRadius.all(
                                  Radius.circular(9.w),
                                )),
                            child: item.config
                                ? Text(
                                    S.current
                                        .text('diyNew.accessory_configured'),
                                    style: TextStyle(
                                        fontSize: 10.sp,
                                        color: ColorsUtil.systemAlarmColor),
                                  )
                                : Text(
                                    S.current.text(
                                        'diyNew.accessory_not_configured'),
                                    style: TextStyle(
                                        fontSize: 10.sp,
                                        color: ColorsUtil.hintColor),
                                  )),
                      ],
                    ),
                    Text(
                      item.name,
                      style: TextStyle(
                          fontSize: 13.sp,
                          color: ColorsUtil.textColor,
                          fontWeight: FontWeight.w600),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(
                      height: 5.w,
                    ),
                    Text(
                      item.deviceSn,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: ColorsUtil.hintColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    )
                  ],
                ),
              ),
            ),
          );
        });
  }

  //ShellyPlusPlugS
  Future startTimer({bool needClean = false}) async {
    _timeCount = count;
    _timer?.cancel();
    if (needClean) {
      _accessoriseList.clear();
      context.read<AddShellyBloc>().add(CleanDeviceEvent());
    }
    if (_isScanning == false) {
      await FlutterBluePlus.startScan(
        removeIfGone: const Duration(milliseconds: 10000),
        continuousUpdates: true,
      );
    }
    _timer = Timer.periodic(const Duration(milliseconds: 1000), (timer) {
      if (mounted) {
        _timeCount--;
        if (_timeCount <= 0) {
          timer.cancel();
          isScan = false;
          _stopScan();
        }
        setState(() {});
      }
    });
    setState(() {
      isScan = true;
    });
  }

  void _stopScan() {
    FlutterBluePlus.stopScan();
  }

  void _navToShellyConfigPage(ConnectSuccessBleState state) async {
    var params = await Navigator.of(context).push(
      DiyRoutes.onGenerateRoute(
        RouteSettings(name: state.deviceInfo.type.routeName, arguments: {
          'wifiConfig': false,
          'deviceInfo': state.deviceInfo,
          'systemNo': widget.systemNo,
          'config': state.config.toJson(),
          'clustered': widget.clustered
        }),
      ),
    );
    if (!mounted) return;
    if (params != null) {
      var remoteId = params['remoteId'] as String;
      var isAdd = params['isAdd'] as bool;
      if (isAdd) {
        BlocProvider.of<AddShellyBloc>(context)
            .add(UpdateConfigShellyRemoteIdEvent(remoteId: remoteId));
      } else {
        BlocProvider.of<AddShellyBloc>(context)
            .add(RemoveConfigShellyRemoteIdEvent(remoteId: remoteId));
      }
    }
  }

  /// Shows a reset confirmation dialog for CT devices
  void _showResetConfirmationDialog(AccessoriseDeviceInfoModel device) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return CustomInputDialogBox(
          title: S.current.text('diyNew.reset_shelly_dialog_title'),
          content: S.current.text('diyNew.manual_reset_shelly__dialog_content'),
          actions: [
            CustomInputButton(
              text: S.current.text('common.cancel'),
              onTap: () => Navigator.of(dialogContext).pop(),
            ),
            CustomInputButton(
              text: S.current.text('diyNew.reset_ready'),
              textColor: ColorsUtil.highlightTextColor,
              onTap: () {
                Navigator.of(dialogContext).pop();
                // Proceed with connecting to the device after reset confirmation
                context
                    .read<AddShellyBloc>()
                    .add(ConnectBleDeviceEvent(item: device));
              },
            )
          ],
        );
      },
    );
  }

  /// 跳转到水浸提示页面
  void _navigateToFloodTipPage() async {
    await Navigator.of(context).push(
      DiyRoutes.onGenerateRoute(
        RouteSettings(name: DiyRouteType.diyShellyFloodTipRoute, arguments: {
          'systemNo': widget.systemNo,
          'systemId': widget.systemId,
          'status': widget.status,
        }),
      ),
    );
  }
}
