import 'dart:async';

import 'package:flutter_basic/components/bottom_button.dart';
import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/components/cus_InkWell/CusInkWell.dart';
import 'package:flutter_basic/components/custom_card.dart';
import 'package:flutter_basic/components/custom_card_list.dart';
import 'package:flutter_basic/components/custom_dialog_box.dart';
import 'package:flutter_basic/components/custom_loading_icon.dart';
import 'package:flutter_basic/components/custom_wifi_white_signal.dart';
import 'package:flutter_basic/event/refresh_devices_page_event.dart';
import 'package:flutter_basic/features/diy_new/accessories/model/accessorise_device_filter.dart';
import 'package:flutter_basic/features/diy_new/shelly/page/shelly_page.dart';
import 'package:flutter_basic/features/diy_new/shelly/shelly_ct/bloc/shelly_ct_bloc.dart';
import 'package:flutter_basic/features/diy_new/shelly/shelly_ct/page/shelly_ct_select_page.dart';
import 'package:flutter_basic/features/monitor/bloc/monitor_fetch_bloc.dart';
import 'package:flutter_basic/platform/bluetooth/bluetooth_manager.dart';
import 'package:flutter_basic/platform/bluetooth/blufi_manager.dart';
import 'package:flutter_basic/platform/bluetooth/model/ble_wifi_account.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/platform/utils/event_bus_manager.dart';
import 'package:flutter_basic/platform/utils/version.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../monitor/bloc/mqtt/mqtt_bloc.dart';

class ShellyCtPage extends StatefulWidget {
  final bool clustered;
  const ShellyCtPage({super.key, this.clustered = false});

  @override
  State<ShellyCtPage> createState() => _ShellyCtPageState();
}

class _ShellyCtPageState extends State<ShellyCtPage> {
  late ShellyCtBloc bloc = context.read<ShellyCtBloc>();
  WifiType wifiType = WifiType.home;
  WifiAccountList wifiAccountList = WifiAccountList(account: [
    WifiAccount(password: "", name: ''),
  ]);

  /// 蓝牙状态监听
  StreamSubscription<BluetoothConnectionState>?
      onConnectionStateChangedStreamSubscription;
  @override
  void initState() {
    context.read<ShellyCtBloc>().wifiType = wifiType;
    super.initState();

    // Check if MonitorFetchBloc is available in the context
    try {
      // 获取设备工厂型号以判断是否为二代DIY设备
      final factoryStr = context
              .read<MonitorFetchBloc>()
              .state
              .monitorModel
              ?.systemVO
              ?.factoryModel ??
          "";

      // 获取设备工厂型号以判断是否为二代DIY设备
      final softVar = context
              .read<MqttBloc>()
              .deviceList
              .firstWhere((element) => element.productKey == 'HB-EMS')
              .softVer ??
          "";
      if (isDIY2(factoryStr)) {
        bloc.phaseType = ShellyCtPhaseType.t;
      } else {
        bloc.phaseType = ShellyCtPhaseType.a;
      }
      bloc.softVer = softVar;
      bloc.factoryStr = factoryStr;
    } catch (e) {
      // MonitorFetchBloc might not be available, just continue
      logger.e("Failed to get factoryModel: $e");
    }

    BlufiManager.instance.getDeviceWifiAccount().then((value) {
      wifiAccountList = value;
      context.read<ShellyCtBloc>().wifiAccountList = wifiAccountList;
    });
    context.read<ShellyCtBloc>().add(GetShellyWifiEvent());
  }

  @override
  void dispose() {
    onConnectionStateChangedStreamSubscription?.cancel();
    super.dispose();
  }

  /// 设备名称
  Widget get deviceTitle => Container(
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.fromLTRB(20.w, 15.w, 20.w, 15.w),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 1.w, color: ColorsUtil.dividerColor),
        ),
      ),
      child: Text(
        '${bloc.deviceInfo.name} - ${bloc.deviceInfo.deviceSn}',
        style: TextStyle(
          fontSize: 14.sp,
          color: ColorsUtil.textColor,
          fontWeight: FontWeight.w500,
        ),
      ));

  /// 未连接时显示的wifi图标
  Widget get noConfigWifiIcon => CircleAvatar(
      radius: 12.w,
      backgroundColor: Colors.grey.withOpacity(0.1),
      child: CustomImageAsset(
        'icon_wifi_signal_3',
        width: 22.w,
        height: 22.w,
      ));

  /// 生成 CardItemData
  CardItemData _buildCardItem({
    required Widget child,
    VoidCallback? onTap,
  }) {
    return CardItemData(
      title: '',
      item: CusInkWell(
        onTap: onTap,
        child: child,
      ),
    );
  }

  /// 创建WiFi设置项
  CardItemData _buildWifiSettingItem({
    required String title,
    required String wifiName,
    required bool isSelected,
    required Widget icon,
    required VoidCallback onTap,
  }) {
    return _buildCardItem(
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: 6.w, top: 15.w),
                child: Text(
                  S.current.text(title),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color.fromRGBO(0, 0, 0, 0.6),
                  ),
                ),
              ),
              CustomCard(
                child: Container(
                  width: 250.w,
                  color: Color.fromRGBO(247, 247, 247, 1),
                  padding:
                      EdgeInsets.symmetric(vertical: 10.w, horizontal: 16.2),
                  child: Row(
                    children: [
                      icon,
                      SizedBox(width: 10.w),
                      Text(
                        wifiName,
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: Color.fromRGBO(0, 0, 0, 1),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 15.w),
            ],
          ),
          if (isSelected)
            Padding(
              padding: EdgeInsets.only(top: 30.h, right: 10.w),
              child: CustomImageAsset('icon_list_checked', width: 16.w),
            )
        ],
      ),
    );
  }

  getWifiSSID() {
    if (wifiType == WifiType.home) {
      if (wifiAccountList.account.isNotEmpty) {
        return wifiAccountList.account.first.name.trim();
      }
      return '';
    } else {
      return bloc.shellyWifiInfo.ssid;
    }
  }

  /// wifi设置
  Widget get wifiSetting => BlocConsumer<ShellyCtBloc, ShellyCtState>(
        listener: (context, state) {
          String msg = "";
          if (state is ShellyCtGetWifiInfoFailure) {
            // wifi信息获取失败
            msg = 'diyNew.shelly_wifi_list_failure';
          } else if (state is ShellyCtGetWifiIsNullState) {
            // wifi信息为空
            msg = 'diyNew.shelly_wifi_is_null';
          } else if (state is ShellyCtSetWifiChanged) {
            if (state.status == ShellyCtSetWifiState.failure) {
              msg = 'diyNew.shelly_wifi_connect_failure';
            } else if (state.status == ShellyCtSetWifiState.connected) {
              msg = 'diyNew.shelly_wifi_connect_success';
            }
          }
          if (msg.isNotEmpty) {
            CustomToast.showToast(context, S.current.text(msg));
          }
        },
        builder: (context, state) {
          return BlocBuilder<ShellyCtBloc, ShellyCtState>(
            builder: (context, state) {
              var bloc = context.read<ShellyCtBloc>();
              Widget icon = noConfigWifiIcon;
              if (state is ShellyCtSetWifiChanged) {
                if (state.status == ShellyCtSetWifiState.connecting) {
                  icon = _buildStatusIcon(ExecutionStatus.loading);
                } else if (state.status == ShellyCtSetWifiState.connected) {
                  icon = CustomWifiWhiteSignal(bloc.shellyWifiInfo.rssi);
                } else if (state.status == ShellyCtSetWifiState.failure) {
                  icon = noConfigWifiIcon;
                }
              } else if (bloc.deviceInfo.config) {
                icon = CustomWifiWhiteSignal(bloc.shellyWifiInfo.rssi);
              }

              return CustomCardList(
                padding: EdgeInsets.only(left: 15.w),
                separatorBuilder: (context, index) => Container(),
                cardList: [
                  _buildWifiSettingItem(
                    title: 'diyNew.home_wifi',
                    icon: wifiType == WifiType.home ? icon : noConfigWifiIcon,
                    wifiName: wifiAccountList.account.first.name,
                    isSelected: wifiType == WifiType.home,
                    onTap: () => setState(() {
                      wifiType = WifiType.home;
                      context.read<ShellyCtBloc>().wifiType = wifiType;
                      context.read<ShellyCtBloc>().wifiAccountList =
                          wifiAccountList;
                    }),
                  ),
                  _buildWifiSettingItem(
                    title: 'diyNew.device_wifi',
                    icon: wifiType == WifiType.device ? icon : noConfigWifiIcon,
                    wifiName: bloc.shellyWifiInfo.ssid,
                    isSelected: wifiType == WifiType.device,
                    onTap: () => setState(() {
                      wifiType = WifiType.device;
                      context.read<ShellyCtBloc>().wifiType = wifiType;
                      context.read<ShellyCtBloc>().wifiAccountList =
                          wifiAccountList;
                    }),
                  ),
                ],
              );
            },
            buildWhen: (previous, current) =>
                current is ShellyCtGetWifiInfoSuccess ||
                current is ShellyCtSetWifiChanged,
          );
        },
      );

  /// 配置后设置蓝牙
  Widget get settingAfter => BlocConsumer<ShellyCtBloc, ShellyCtState>(
        listener: (context, state) {
          if (state is! ShellyCtSetBluetoochStatusChanged) return;
          if (state.status == ExecutionStatus.failure) {
            CustomToast.showToast(context,
                S.current.text('diyNew.shelly_bluetooth_setting_failure'));
          } else if (state.status == ExecutionStatus.success) {
            CustomToast.showToast(context,
                S.current.text('diyNew.shelly_bluetooth_setting_success'));
          }
        },
        builder: (context, state) {
          Widget icon =
              CustomImageAsset('icon_accessories_setting', size: 22.w);
          var configing = state is ShellyCtConfigStart;
          if (state is ShellyCtSetBluetoochStatusChanged) {
            icon = _buildStatusIcon(state.status);
            configing = state.status == ExecutionStatus.loading;
          }
          return _buildSettingItem(
            onTap: configing ? null : _setBluetoothOperation,
            icon: icon,
            title: 'diyNew.shelly_dc_after_select_title',
            value: bloc.bluetoothType.name,
          );
        },
        buildWhen: (previous, current) =>
            current is ShellyCtSetBluetoothChanged ||
            current is ShellyCtConfigStart ||
            current is ShellyCtConfigFailureState ||
            current is ShellyCtBlueDisContentState ||
            current is ShellyCtSetBluetoochStatusChanged,
      );

  /// 设置相位
  Widget get settingPhase => BlocBuilder<ShellyCtBloc, ShellyCtState>(
        builder: (context, state) {
          Widget icon = CustomImageAsset('icon_accessories_phase', size: 22.w);
          var configing = state is ShellyCtConfigStart;
          if (state is ShellyCtSetPhaseChannelStatusChanged) {
            icon = _buildStatusIcon(state.status);
            configing = state.status == ExecutionStatus.loading;
          }
          return _buildSettingItem(
            onTap: configing ? null : _setPhase,
            icon: icon,
            title: 'diyNew.shelly_dc_phase_select_title',
            value: bloc.phaseType?.translatedName,
          );
        },
        buildWhen: (previous, current) =>
            current is ShellyCtSetPhaseChanged ||
            current is ShellyCtConfigStart ||
            current is ShellyCtConfigFailureState ||
            current is ShellyCtBlueDisContentState ||
            current is ShellyCtSetPhaseChannelStatusChanged,
      );

  /// 设置通道
  Widget get settingChannel => BlocBuilder<ShellyCtBloc, ShellyCtState>(
        builder: (context, state) {
          Widget icon = CustomImageAsset('icon_accessories_phase', size: 22.w);
          var configing = state is ShellyCtConfigStart;
          if (state is ShellyCtSetPhaseChannelStatusChanged) {
            icon = _buildStatusIcon(state.status);
            configing = state.status == ExecutionStatus.loading;
          }
          return _buildSettingItem(
            onTap: configing ? null : _setChannel,
            icon: icon,
            title: 'diyNew.shelly_dc_channel_select_title',
            value: bloc.channelType.name,
          );
        },
        buildWhen: (previous, current) =>
            current is ShellyCtSetChannelChanged ||
            current is ShellyCtConfigStart ||
            current is ShellyCtConfigFailureState ||
            current is ShellyCtBlueDisContentState ||
            current is ShellyCtSetPhaseChannelStatusChanged,
      );

  /// 监听一些全局触发事件
  Widget get listen => BlocListener<ShellyCtBloc, ShellyCtState>(
        listener: (context, state) {
          if (state is ShellyCtShowLoading) {
            CustomLoading.showLoading(null);
          } else if (state is ShellyCtHideLoading) {
            CustomLoading.dismissLoading();
          } else if (state is ShellyCtStartListenBluetooth) {
            _listenBluetoothState();
          } else if (state is ShellyCtCancelListenBluetooth) {
            onConnectionStateChangedStreamSubscription?.cancel();
          } else if (state is ShellyCtSetPhaseChannelStatusChanged) {
            if (state.status == ExecutionStatus.failure) {
              var msg =
                  state.message ?? 'diyNew.shelly_distribute_setting_failure';
              CustomToast.showToast(context, S.current.text(msg));
            } else if (state.status == ExecutionStatus.success) {
              CustomToast.showToast(context,
                  S.current.text('diyNew.shelly_distribute_setting_success'));
            }
          } else if (state is ShellyCtConfigSuccessState) {
            logger.i('配置成功');
            EventBusManager.eventBus.fire(RefreshDevicesPageEvent());
            Timer(const Duration(milliseconds: 500), () {
              Navigator.of(context)
                  .pop({'remoteId': bloc.deviceInfo.remoteId, 'isAdd': true});
            });
          } else if (state is ShellyCtBlueDisContentState) {
            // 蓝牙断开状态由 _listenBluetoothState 处理，这里不再显示 toast
            // 会尝试重连 3 次后再显示 toast
          } else if (state is ShellyCtConfigFailureState) {
            if (state.message.isEmpty) return;
            var msg = S.current.text(state.message);
            CustomToast.showToast(context, msg);
          }
        },
        child: const SizedBox.shrink(),
      );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorsUtil.backgroundColor,
      appBar: CustomAppBar(
        title: AppBarCenterText(
          title: S.current.text('diyNew.accessory_title'),
        ),
        actions: [
          BlocConsumer<ShellyCtBloc, ShellyCtState>(
            builder: (context, state) {
              if (!bloc.deviceInfo.config) return const SizedBox.shrink();
              return Padding(
                padding: EdgeInsets.only(right: 20.w),
                child: CusInkWell(
                  onTap: _showDeleteDeviceDialog,
                  child: Text(
                    S.current.text('common.reset'),
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: ColorsUtil.requiredTextColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              );
            },
            buildWhen: (previous, current) =>
                current is ShellyCtDeleteDeviceStatusChanged,
            listener: (BuildContext context, ShellyCtState state) {
              if (state is ShellyCtDeleteDeviceStatusChanged) {
                if (state.status == ExecutionStatus.failure) {
                  var msg =
                      S.current.text(state.message ?? 'common.operateFail');
                  CustomToast.showToast(context, msg);
                } else if (state.status == ExecutionStatus.success) {
                  CustomToast.showToast(
                      context, S.current.text('common.operateSuccess'));
                }
              }
            },
          )
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(50.w),
          child: deviceTitle,
        ),
      ),
      body: Column(children: [
        listen,
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.fromLTRB(15.w, 20.w, 15.w, 10.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                wifiSetting,
                SizedBox(height: 20.w),
                CustomCardList(
                  padding: EdgeInsets.only(left: 15.w, right: 10.w),
                  sectionTitle:
                      S.current.text('diyNew.shelly_ct_other_setting'),
                  separatorBuilder: (context, index) => Divider(
                    height: 1,
                    indent: 50.w,
                    endIndent: 15.w,
                    color: ColorsUtil.dividerColor,
                  ),
                  cardList: [
                    //  Shelly CT 1.6 版本 关闭蓝牙,蓝牙立马断开
                    // _buildCardItem(child: settingAfter),
                    if (bloc.deviceInfo.type == AccessoriesDeviceType.ct3)
                      _buildCardItem(child: settingPhase),
                    if (bloc.deviceInfo.type == AccessoriesDeviceType.ct)
                      _buildCardItem(child: settingChannel),
                  ],
                ),
              ],
            ),
          ),
        ),
        SafeArea(
          child: BlocBuilder<ShellyCtBloc, ShellyCtState>(
            buildWhen: (previous, current) =>
                current is ShellyCtConfigStart ||
                current is ShellyCtSetPhaseChanged ||
                current is ShellyCtBlueDisContentState ||
                current is ShellyCtConfigFailureState,
            builder: (context, state) {
              var noPhase = bloc.deviceInfo.type == AccessoriesDeviceType.ct3 &&
                  bloc.phaseType == null;
              return BottomButtonWidget(
                buttonType: BottomButtonType.save,
                enabled: !noPhase && state is! ShellyCtConfigStart,
                text: '',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (state is ShellyCtConfigStart) ...[
                      SizedBox.fromSize(
                        size: Size(13.w, 13.w),
                        child: const CircularProgressIndicator(
                            color: Colors.white, strokeWidth: 2),
                      ),
                      SizedBox(width: 10.w)
                    ],
                    Text(S.current.text('diyNew.shelly_ct_config_btn_title')),
                  ],
                ),
                onPressed: () {
                  if (getWifiSSID().isEmpty) {
                    CustomToast.showToast(
                        context, S.current.text('diyNew.wifi_password_empty'));
                    return;
                  }
                  if (noPhase) {
                    CustomToast.showToast(context,
                        S.current.text('diyNew.shelly_ct_select_phase'));
                    return;
                  }
                  CustomToast.showToast(context,
                      S.current.text('diyNew.shelly_ct_start_setting'));
                  // 获取设备工厂型号以判断是否为二代DIY设备
                  final factoryStr = context
                          .read<MonitorFetchBloc>()
                          .state
                          .monitorModel
                          ?.systemVO
                          ?.factoryModel ??
                      "";
                  final softVar = context
                          .read<MqttBloc>()
                          .deviceList
                          .firstWhere(
                              (element) => element.productKey == 'HB-EMS')
                          .softVer ??
                      "";
                  bloc.add(ShellyCtSetConfigEvent());
                },
              );
            },
          ),
        ),
      ]),
    );
  }

  /// 根据状态显示图标
  _buildStatusIcon(ExecutionStatus status) {
    if (status == ExecutionStatus.loading) {
      return Container(
        width: 24.w,
        height: 24.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(15.w)),
          color: ColorsUtil.iconBackgroundColor,
        ),
        child: Center(
          child: CustomerLoadingIcon(
            size: Size(12.w, 12.w),
            width: 2.w,
          ),
        ),
      );
    }

    String icon = status == ExecutionStatus.success
        ? 'icon_wifi_connect_success'
        : 'icon_wifi_connect_failed';

    return CircleAvatar(
      radius: 12.w,
      backgroundColor: Colors.transparent,
      child: CustomImageAsset(icon, width: 22.w, height: 22.w),
    );
  }

  /// 蓝牙重连计数器
  int _reconnectCount = 0;

  /// 是否正在重连中
  bool _isReconnecting = false;

  /// 监听蓝牙状态
  _listenBluetoothState() {
    onConnectionStateChangedStreamSubscription = BluetoothManager
        .instance.mBluetoothConnectedDevice?.connectionState
        .listen((event) {
      if (event == BluetoothConnectionState.disconnected) {
        _reconnectBluetooth();
      }
    });

    // 监听蓝牙异常事件
    context.read<ShellyCtBloc>().stream.listen((state) {
      if (state is ShellyCtBlueDisContentState) {
        // 当收到蓝牙断开异常时尝试重连
        _reconnectBluetooth();
      }
    });
  }

  /// 尝试重新连接蓝牙
  _reconnectBluetooth() async {
    if (!mounted) return;

    // 防止多次触发重连
    if (_isReconnecting) return;

    _isReconnecting = true;
    _reconnectCount++;
    logger.d('蓝牙断开，尝试第 $_reconnectCount 次重连');

    try {
      await BluetoothManager.instance.restartConnect();
      // 重连成功，重置计数器
      _reconnectCount = 0;
      logger.d('蓝牙重连成功');
    } catch (e) {
      logger.d('蓝牙重连失败: $e');

      // 如果已经尝试了3次，显示toast并重置计数器
      if (_reconnectCount >= 3) {
        if (mounted) {
          CustomToast.showToast(
              context, S.current.text('diyNew.shelly_disconnected_error'));
        }
        _reconnectCount = 0;
      }
    } finally {
      _isReconnecting = false;
    }
  }

  /// 设置相位
  _setPhase() async {
    // 获取设备工厂型号以判断是否为二代DIY设备
    final factoryStr = context
            .read<MonitorFetchBloc>()
            .state
            .monitorModel
            ?.systemVO
            ?.factoryModel ??
        "";

    var value = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ShellyCtPhaseSelectPage<ShellyCtPhaseType>(
          value: bloc.phaseType,
          list: ShellyCtPhaseType.valuesFor(bloc.deviceInfo.type,
              factoryStr: factoryStr),
        ),
      ),
    );
    if (mounted && value is ShellyCtPhaseType) {
      // ignore: use_build_context_synchronously
      context.read<ShellyCtBloc>().add(ShellyCtSetPhaseEvent(phase: value));
    }
  }

  /// 设置蓝牙后续操作
  _setBluetoothOperation() async {
    var value = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            ShellyCtPhaseSelectPage<ShellyCtAfterBluetoothType>(
          value: bloc.bluetoothType,
          list: ShellyCtAfterBluetoothType.values.toList(),
        ),
      ),
    );
    if (mounted && value is ShellyCtAfterBluetoothType) {
      // ignore: use_build_context_synchronously
      context.read<ShellyCtBloc>().add(ShellyCtSetBluetoothEvent(type: value));
    }
  }

  /// 设置通道
  _setChannel() async {
    var value = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ShellyCtPhaseSelectPage<ShellyCtChannelType>(
          value: bloc.channelType,
          list: ShellyCtChannelType.values.toList(),
        ),
      ),
    );
    if (mounted && value is ShellyCtChannelType) {
      // ignore: use_build_context_synchronously
      context.read<ShellyCtBloc>().add(ShellyCtSetChannelEvent(type: value));
    }
  }

  /// 显示删除设备弹窗
  _showDeleteDeviceDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext _) {
        return CustomInputDialogBox(
          title: S.current.text('diyNew.reset_shelly_dialog_title'),
          content: S.current.text('diyNew.reset_shelly_dialog_content'),
          actions: [
            CustomInputButton(
              text: S.current.text('common.cancel'),
              onTap: () => Navigator.of(_).pop(),
            ),
            CustomInputButton(
              text: S.current.text('common.reset'),
              textColor: ColorsUtil.highlightTextColor,
              onTap: () {
                context.read<ShellyCtBloc>().add(ShellyCtDeleteDeviceEvent());
                Navigator.of(_).pop();
              },
            )
          ],
        );
      },
    );
  }

  /// 构建设置项
  _buildSettingItem({
    required void Function()? onTap,
    required Widget icon,
    required String title,
    String? value,
  }) {
    return CusInkWell(
      onTap: onTap,
      child: Row(children: [
        CircleAvatar(
          radius: 12.w,
          backgroundColor: Colors.grey.withOpacity(0.1),
          child: icon,
        ),
        SizedBox(width: 10.w),
        Expanded(child: Text(S.current.text(title))),
        if (value != null) Text(S.current.text(value)),
        if (value == null)
          Text(
            S.current.text('diyNew.PleaseSelect'),
            style: TextStyle(color: ColorsUtil.pickerHintColor),
          ),
        CustomImageAsset(
          'icon_arrow_right_item',
          width: 20.w,
          height: 20.h,
        )
      ]),
    );
  }
}
