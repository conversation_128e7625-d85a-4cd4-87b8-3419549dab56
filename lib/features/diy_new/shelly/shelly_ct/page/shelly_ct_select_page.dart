import 'package:flutter/material.dart';
import 'package:flutter_basic/components/bottom_button.dart';
import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/components/cus_InkWell/CusInkWell.dart';
import 'package:flutter_basic/components/custom_card.dart';
import 'package:flutter_basic/features/diy_new/accessories/model/accessorise_device_filter.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 相位选择页面
class ShellyCtPhaseSelectPage<T> extends StatefulWidget {
  final T? value;
  final List<T> list;

  const ShellyCtPhaseSelectPage({
    super.key,
    required this.value,
    required this.list,
  });

  @override
  State<ShellyCtPhaseSelectPage> createState() =>
      _ShellyCtPhaseSelectPageState<T>();
}

class _ShellyCtPhaseSelectPageState<T> extends State<ShellyCtPhaseSelectPage> {
  late T? value = widget.value;

  String get pageTitle => T == ShellyCtPhaseType
      ? S.current.text('diyNew.shelly_dc_phase_select_title')
      : S.current.text('diyNew.shelly_dc_channel_select_title');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorsUtil.backgroundColor,
      appBar: CustomAppBar(
        title: AppBarCenterText(title: pageTitle),
      ),
      body: Column(
        children: [
          Expanded(
            child: Align(
              alignment: Alignment.topCenter,
              child: CustomCard(
                margin: EdgeInsets.fromLTRB(20.w, 10.w, 20.w, 10.w),
                child: Column(mainAxisSize: MainAxisSize.min, children: [
                  for (var type in widget.list) ...[
                    CusInkWell(
                      onTap: () => setState(() => value = type),
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 20.w, vertical: 15.w),
                        child: Row(
                          children: [
                            Expanded(
                                child:
                                    Text(S.current.text(type.translatedName))),
                            if (value == type)
                              CustomImageAsset('icon_list_checked', size: 18.w)
                          ],
                        ),
                      ),
                    ),
                    if (type.index != widget.list.length - 1)
                      Divider(
                        height: 1,
                        color: ColorsUtil.dividerColor,
                        indent: 20.w,
                      )
                  ]
                ]),
              ),
            ),
          ),
          SafeArea(
            child: BottomButtonWidget(
              buttonType: BottomButtonType.save,
              text: S.current.text('common.done'),
              onPressed: () {
                Navigator.of(context).pop(value);
              },
            ),
          )
        ],
      ),
    );
  }
}
