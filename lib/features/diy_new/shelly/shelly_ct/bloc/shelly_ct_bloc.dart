import 'package:equatable/equatable.dart';
import 'package:flutter/services.dart';
import 'package:flutter_basic/features/diy_new/accessories/model/accessorise_device_filter.dart';
import 'package:flutter_basic/features/diy_new/shelly/model/accessorise_device_info_model.dart';
import 'package:flutter_basic/features/diy_new/shelly/model/shelly_wifi_state_model.dart';
import 'package:flutter_basic/features/diy_new/shelly/page/shelly_page.dart';
import 'package:flutter_basic/platform/bluetooth/ble_exception/ble_reconnected_exception.dart';
import 'package:flutter_basic/platform/bluetooth/ble_exception/exception.dart';
import 'package:flutter_basic/platform/bluetooth/bluetooth_manager.dart';
import 'package:flutter_basic/platform/bluetooth/blufi_manager.dart';
import 'package:flutter_basic/platform/bluetooth/model/ble_wifi_account.dart';
import 'package:flutter_basic/platform/config/system_type.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/version.dart';
import 'package:flutter_basic/repositories/custom_exception/ServiceException.dart';
import 'package:flutter_basic/repositories/diy_new_repository/diy_new_repository.dart';
import 'package:flutter_basic/repositories/download_repository/download_client_repository.dart';
import 'package:flutter_basic/repositories/shelly_repository/model/model.dart';
import 'package:flutter_js/quickjs/ffi.dart';

import '../../../../../platform/mqtt/mqtt_manager.dart';

part 'shelly_ct_event.dart';
part 'shelly_ct_state.dart';

class ShellyCtBloc extends Bloc<ShellyCtEvent, ShellyCtState> {
  final String systemNo;
  final DiyDeviceMqttConfigModel mqttConfigModel;
  final AccessoriseDeviceInfoModel deviceInfo;

  /// 当前设备需要连接的wifi信息
  late ShellyWifiInfo shellyWifiInfo = ShellyWifiInfo.fromSystemSn(systemNo);

  /// wifi是否已配置
  bool isWifiConnected = false;

  /// 当前选择相位，二代CT才有T相位
  ShellyCtPhaseType? phaseType;

  /// 通道类型 默认AB
  ShellyCtChannelType channelType = ShellyCtChannelType.ab;

  /// 当前选择蓝牙后的操作
  WifiType wifiType = WifiType.home;
  WifiAccountList wifiAccountList = WifiAccountList(account: [
    WifiAccount(password: "", name: ''),
  ]);
  ShellyCtAfterBluetoothType bluetoothType = ShellyCtAfterBluetoothType.close;

  /// 存储从MqttBloc获取到的软件版本
  String softVer = "";
  String factoryStr = "";
  getWifiSSID() {
    if (wifiType == WifiType.home) {
      return wifiAccountList.account.first.name;
    } else {
      return shellyWifiInfo.ssid;
    }
  }

  getWifiPassword() {
    if (wifiType == WifiType.home) {
      return wifiAccountList.account.first.password;
    } else {
      return shellyWifiInfo.ssid.split('_').length > 1
          ? shellyWifiInfo.ssid.split('_')[1]
          : '';
    }
  }

  ShellyCtBloc({
    required this.systemNo,
    required this.mqttConfigModel,
    required this.deviceInfo,
  }) : super(ShellyCtInitial()) {
    // 使用现有的isDIY2方法检查是否是二代DIY设备

    on<GetShellyWifiEvent>(_getWifiList);
    on<ShellyCtSetWifiEvent>(
        (event, emit) => _setShellyWifi(event, emit, true));
    on<ShellyCtSetConfigEvent>((event, emit) => _setConfig(emit));

    on<ShellyCtDeleteDeviceEvent>((event, emit) => _deleteDevice(emit));

    /// 设置相位
    on<ShellyCtSetPhaseEvent>((event, emit) {
      phaseType = event.phase;
      emit(ShellyCtSetPhaseChanged(phaseType!));
    });

    /// 设置后操作类型
    on<ShellyCtSetBluetoothEvent>((event, emit) {
      bluetoothType = event.type;
      emit(ShellyCtSetBluetoothChanged(bluetoothType));
    });

    /// 设置通道类型
    on<ShellyCtSetChannelEvent>((event, emit) {
      channelType = event.type;
      emit(ShellyCtSetChannelChanged(channelType));
    });
  }

  _getWifiList(GetShellyWifiEvent event, Emitter<ShellyCtState> emit) async {
    await Future.delayed(const Duration(milliseconds: 500));
    emit(ShellyCtShowLoading());
    try {
      // https://zentao.dev.highlands.ltd/bug-view-29734.html 客户要求这样改的
      // 需要用户手动重置CT设备
      // await BluetoothManager.instance.shellyReset();
      // await Future.delayed(const Duration(milliseconds: 10000));
      // await BluetoothManager.instance.restartConnect();
      emit(ShellyCtStartListenBluetooth());
      emit(ShellyCtGetWifiInfoSuccess(shellyWifiInfo));
    } catch (e, stack) {
      logger.d("writeMsg catch :$e \n stack: $stack");
      emit(ShellyCtGetWifiInfoFailure());
    } finally {
      emit(ShellyCtHideLoading());
    }
  }

  /// 设置wifi
  _setShellyWifi(
    ShellyCtSetWifiEvent event,
    Emitter<ShellyCtState> emit,
    bool showLoading,
  ) async {
    if (showLoading) {
      emit(const ShellyCtSetWifiChanged(ShellyCtSetWifiState.connecting));
    }
    try {
      ///开始连接WIFI
      await BluetoothManager.instance
          .setWifiConfig(getWifiSSID(), getWifiPassword());

      var count = 0;
      while (count < 10) {
        ShellyWifiStatusModel model =
            await BluetoothManager.instance.getWifiStatus();
        // yield ConnectedWifiProgressState(info: model);
        if (model.status == ShellyConnectedWifiStatus.CONNECTED ||
            model.status == ShellyConnectedWifiStatus.GOT_IP) {
          //只能返回GOT_IP
          model.status = ShellyConnectedWifiStatus.CONNECTED;
          // yield ConnectedWifiSuccessState(info: model);
          //连接成功
          isWifiConnected = true;
          if (showLoading) {
            emit(const ShellyCtSetWifiChanged(ShellyCtSetWifiState.connected));
          }
          break;
        }
        count++;
        await Future.delayed(const Duration(milliseconds: 1000));
      }
      if (count == 10) {
        //WIFI连接失败需要重新配置
        logger.d('wifi连接失败:连接次数：$count');
        throw Exception('diyNew.shelly_wifi_connect_failure');
      }
    } catch (e) {
      logger.e('设置wifi失败---$e');
      emit(const ShellyCtSetWifiChanged(ShellyCtSetWifiState.failure));
      rethrow;
    }
  }

  /// 关闭设备蓝牙
  _closeBluetooth(Emitter<ShellyCtState> emit) async {
    try {
      if (bluetoothType == ShellyCtAfterBluetoothType.close) {
        emit(const ShellyCtSetBluetoochStatusChanged(ExecutionStatus.loading));
        await BluetoothManager.instance.closeShellyBle();
      }
      emit(const ShellyCtSetBluetoochStatusChanged(ExecutionStatus.success));
    } catch (e) {
      emit(const ShellyCtSetBluetoochStatusChanged(ExecutionStatus.failure,
          message: 'diyNew.shelly_bluetooth_setting_failure'));
      rethrow;
    }
  }

  /// 判断当前CT设备是否是三相CT
  /// @return true为三相CT，false为单相CT
  bool isThreePhaseDevice() {
    return (deviceInfo.type == AccessoriesDeviceType.ct3);
  }

  /// 添加智能设备，配置完成后调用
  _addSmartDevice(Emitter<ShellyCtState> emit) async {
    // 根据设备类型确定linkParaValue
    int linkParaValue;

    // 判断是三相还是单相CT
    bool isThreePhase = isThreePhaseDevice();

    if (isThreePhase) {
      // 三相CT，使用相位类型（如果是T相则使用T相，否则使用选择的其他相位）
      linkParaValue = phaseType!.value;
    } else {
      // 单相CT，使用通道类型
      linkParaValue = channelType.value;
    }

    try {
      emit(const ShellyCtSetPhaseChannelStatusChanged(ExecutionStatus.loading));
      await BlufiManager.instance.addSmartDevice(
        shelly_sn: deviceInfo.deviceSn,
        linkPara: linkParaValue.toString(),
        deviceType: DeviceComponent.CT,
      );
      emit(const ShellyCtSetPhaseChannelStatusChanged(ExecutionStatus.success));
    } on BleDeviceFailureException catch (e) {
      String msg = e.msg ?? "";
      if (e.result == '16') {
        msg = 'diyNew.shelly_ct_only_one_ct';
      }
      emit(
        ShellyCtSetPhaseChannelStatusChanged(
          ExecutionStatus.failure,
          message: msg,
        ),
      );
      rethrow;
    } catch (e) {
      logger.d('添加智能设备失败---$e');
      emit(const ShellyCtSetPhaseChannelStatusChanged(ExecutionStatus.failure,
          message: 'diyNew.shelly_device_add_failure'));
      rethrow;
    }
  }

  _setConfig(Emitter<ShellyCtState> emit) async {
    emit(ShellyCtConfigStart());
    try {
      // 配置wifi
      if (isWifiConnected == false) {
        await _setShellyWifi(ShellyCtSetWifiEvent(), emit, true);
      }
      // 状态处理加到这里是为了让配置过程loading看起来更平滑
      emit(const ShellyCtSetBluetoochStatusChanged(ExecutionStatus.loading));

      // ************:1883或者8883
      logger.d('开始配置mqtt【---------------------------一体机');
      await BluetoothManager.instance.setMQTTConfig(
        sn: deviceInfo.deviceSn,
        server: '************:1883',
        user: null,
        pass: null,
        sslCa: null,
        isTLS: false,
      );

      ///   二代diy ems 版本大于 19
      if (isDIY2(factoryStr) && isGreaterThanV19(softVer)) {
        logger.d("不下发脚本");
      } else {
        ShellyScriptsModel modelList =
            await BluetoothManager.instance.shellyScriptList();

        ///开始删除脚本
        for (Scripts s in modelList.scripts) {
          logger.d('删除脚本-------------');
          await BluetoothManager.instance.shellyScriptDelete(s.id);
        }
        logger.d('创建脚本-------------');
        int scriptId = await BluetoothManager.instance.shellyScriptCreate();

        ///下发脚本代码
        logger.d('下发脚本代码-------------');
        var scriptPath = deviceInfo.type == AccessoriesDeviceType.ct
            ? await DownloadClientRepository.instance.getOtaDownloadFile(
                mqttConfigModel.ctScriptUrl, 'ShellyProEM50.js')
            : await DownloadClientRepository.instance.getOtaDownloadFile(
                mqttConfigModel.ct3ScriptUrl, 'ShellyPro3EM.js');
        await BluetoothManager.instance.putShellyCode(scriptId, scriptPath);

        ///设置脚本上电运行
        logger.d('设置脚本上电运行-------------');
        await BluetoothManager.instance.scriptSetConfig(scriptId);

        ///脚本启动
        logger.d('脚本启动-------------');
        await BluetoothManager.instance.scriptStart(scriptId);
      }

      ///设置时区服务
      await BluetoothManager.instance.systemConfig();

      /// 设置Cloud
      await BluetoothManager.instance.setCloudConfig();

      ///重启设备
      logger.d('重启设备-------------');
      // 状态处理加到这里是为了让配置过程loading看起来更平滑
      emit(const ShellyCtSetPhaseChannelStatusChanged(ExecutionStatus.loading));

      ///取消监听离线
      emit(ShellyCtCancelListenBluetooth());
      await BluetoothManager.instance.rebootDevice();
      await Future.delayed(const Duration(milliseconds: 6000));
      logger.d('准备开始连接设备-------------');

      await BluetoothManager.instance.restartConnect();

      logger.d('设备连接成功-------------');
      logger.d('关闭设备Ap-------------');
      await BluetoothManager.instance.closeAp();

      // await _closeBluetooth(emit);
      await _addSmartDevice(emit);

      await findNewDevice();

      // TODO:这里有BUG，重启后会抛蓝牙断开的异常  蓝牙在设备重启后才会关闭，这里再重启一次设备
      // if (bluetoothType == ShellyCtAfterBluetoothType.close) {
      //   await BluetoothManager.instance.rebootDevice();
      // }

      logger.i('配置shelly成功-------------');
      // 主动召唤一下CT 相位数据数据，不然无法及时更新
      MqttManager.instance.publishMessage(
        'data_get',
        BlufiManager.instance.systemNo,
        {
          "dev_list": [
            {
              "dev_sn": deviceInfo.deviceSn.toLowerCase(),
              // 相位类型
              "meter_list": ['123939841']
            }
          ]
        },
        '${MqttDevicesConfig.publishClusterTopic}${BlufiManager.instance.systemNo}',
      );
      emit(ShellyCtConfigSuccessState());
    } on BleReconnectedFailureException catch (e) {
      logger.i('配置shelly失败---$e}');
      emit(ShellyCtConfigFailureState(e.msg ?? ''));
    } on BleFailureException catch (e) {
      logger.e(e);
      if (e is BleDeviceFailureException) {
        emit(ShellyCtBlueDisContentState());
      }
    } on PlatformException catch (e) {
      logger.d('配置shelly失败---PlatformException: $e');
      // 如果是设备断开连接的异常，触发蓝牙断开状态
      if (e.message?.contains('device is disconnected') == true ||
          e.code == 'writeCharacteristic') {
        emit(ShellyCtBlueDisContentState());
      } else {
        emit(const ShellyCtConfigFailureState('common.operateFail'));
      }
    } catch (e) {
      logger.d('配置shelly失败---$e');
      emit(const ShellyCtConfigFailureState('common.operateFail'));
    }
  }

  /// 使用device_get 判断新设备是否已经添加完成
  findNewDevice() async {
    // 一分钟超时
    var startTime = DateTime.now();
    var deviceSn = deviceInfo.deviceSn.toUpperCase();
    while (true) {
      var data = await BlufiManager.instance.getDeviceInfo();
      var device = data.dev_list
          ?.firstWhereOrNull((e) => e.dev_sn.toUpperCase() == deviceSn);
      if (device != null && device.status == '0') {
        // 将device_get数据上报到云端，避免设备未上报导致设备列表不显示
        await DiyNewClientRepository.instance.deviceReport(systemNo, data);
        return true;
      }

      if (DateTime.now().difference(startTime).inSeconds >= 60) {
        logger.d('[add_smart] 未找到新设备信息，超时');
        throw BleReconnectedFailureException(
            msg: 'diyNew.add_smart_get_timeout', result: '');
      }

      logger.d('[add_smart] 未找到新设备信息，1秒后重试');
      await Future.delayed(const Duration(seconds: 1));
    }
  }

  /// 删除/重置设备
  _deleteDevice(Emitter<ShellyCtState> emit) async {
    try {
      emit(ShellyCtShowLoading());
      await BluetoothManager.instance.shellyReset();
      await BlufiManager.instance.delSmartDevice(
          shellySn: deviceInfo.deviceSn, deviceType: DeviceComponent.CT);

      deviceInfo.config = false;
      emit(const ShellyCtDeleteDeviceStatusChanged(ExecutionStatus.success));
    } on BleReconnectedFailureException catch (e) {
      emit(ShellyCtDeleteDeviceStatusChanged(ExecutionStatus.failure,
          message: e.msg ?? ''));
    } catch (e) {
      final msg = e is ServiceException ? e.msg : null;
      emit(ShellyCtDeleteDeviceStatusChanged(ExecutionStatus.failure,
          message: msg ?? 'common.operateFail'));
    } finally {
      emit(ShellyCtHideLoading());
    }
  }
}
