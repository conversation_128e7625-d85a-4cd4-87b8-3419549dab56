import 'package:extended_image/extended_image.dart';
import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/components/fl_chart/src/extensions/color_extension.dart';
import 'package:flutter_basic/components/monitor_popup/monitor_dialog_new.dart';
import 'package:flutter_basic/components/page_loading.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/repositories/combine_repository/model/combine_common_model.dart';
import 'package:flutter_basic/repositories/login_repository/login_client_repository.dart';
import 'package:flutter_basic/repositories/notice_repository/model/message_home_invitation_model.dart';
import 'package:flutter_basic/repositories/system_repository/system_repository.dart';
import 'package:flutter_basic/router/combine_feature_routes/combine_routes.dart';
import 'package:flutter_basic/router/combine_feature_routes/combine_routes_type.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../components/bottom_button.dart';
import '../../../repositories/system_repository/model/system_model.dart';

typedef CallBack = void Function(String systemId, SystemModel selectIndexArr);

class SwitchSystem {
  /// 单列
  /// 单列选择器返回选中行对象和index
  static Future<void> showSwitchSystem<T>(
    BuildContext context, {
    required String? systemId,
    CallBack? clickCallBack,
    Function? clickCreate,
    int? monitorModelStatus,
  }) async {
    await showModalBottomSheet(
      isScrollControlled: true,
      useSafeArea: true,
      context: context,
      builder: (BuildContext context) {
        return SystemSwitchView(
          systemId: systemId,
          clickCallBack: clickCallBack,
          clickCreate: clickCreate,
          monitorModelStatus: monitorModelStatus,
        );
      },
    );
  }
}

/// 自定义picker
class SystemSwitchView extends StatefulWidget {
  final String? systemId;
  final CallBack? clickCallBack;
  final Function? clickCreate;
  final int? monitorModelStatus;

  const SystemSwitchView({
    Key? key,
    this.clickCallBack,
    this.systemId,
    this.clickCreate,
    this.monitorModelStatus,
  }) : super(key: key);

  @override
  State<SystemSwitchView> createState() => SystemSwitchViewState();
}

class SystemSwitchViewState extends State<SystemSwitchView> {
  String? systemId;
  bool isLoading = true;
  List<SystemModel>? data = [];

  @override
  void initState() {
    super.initState();
    systemId = widget.systemId;
    _getSystemList();
  }

  @override
  Widget build(BuildContext context) {
    bool showCombine = true;
    return DecoratedBox(
      decoration: BoxDecoration(
        color: ColorsUtil.windowBgColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(8.r)),
      ),
      child: SafeArea(
        bottom: true,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            SizedBox(height: 20.w),
            isLoading ? const PageLoading() : _buildContentWidget(),
            SizedBox(height: 20.w),
            Row(
              children: [
                SizedBox(width: 20.w),
                if (false) ...[
                  Expanded(
                    child: BottomButtonWidget(
                      padding: const EdgeInsets.all(0),
                      buttonPadding: const EdgeInsets.all(0),
                      buttonType: BottomButtonType.cancel,
                      buttonHeight: 36.w,
                      needShadow: false,
                      text: "",
                      onPressed: _onCombine,
                      child: Text(
                        $t('combine.Combine'),
                        style: TextStyle(fontSize: 13.sp),
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                ],
                Expanded(
                  child: BottomButtonWidget(
                    padding: const EdgeInsets.all(0),
                    buttonPadding: const EdgeInsets.all(0),
                    buttonHeight: 36.w,
                    needShadow: false,
                    onPressed: _onConfirm,
                    text: "",
                    child: Text(
                      $t('systemMonitor.Create_New_System'),
                      style: TextStyle(fontSize: 13.sp),
                    ),
                  ),
                ),
                SizedBox(width: 20.w),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 1, color: ColorsUtil.dividerColor),
        ),
      ),
      height: 50.w,
      padding: EdgeInsets.only(left: 20.w, right: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(S.current.text('systemMonitor.My_System'),
              style: TextStyle(
                  color: ColorsUtil.textColor,
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w600)),
          InkResponse(
            onTap: () => {Navigator.pop(context)},
            child: CustomImageAsset(
              'icon_close',
              width: 22.w,
              height: 22.w,
            ),
          )
        ],
      ),
    );
  }

  _onConfirm() {
    Navigator.pop(context);
    widget.clickCreate?.call();
  }

  _onCombine() {
    List<String> systemNoList = [];

    data?.forEach((element) {
      if ((element.clustered != Clustered.yes.toInt()) &&
          element.status != SystemRunStatus.offline.toInt()) {
        systemNoList.add(element.systemNo ?? "");
      }
    });
    logger.i('systemNoList: $systemNoList');
    Navigator.of(context).push(
      CombineRoutes.onGenerateRoute(
        RouteSettings(
          name: CombineRoutesType.combineSystemRoute,
          arguments: {
            'systemNoList': systemNoList,
          },
        ),
      ),
    );
  }

  int selectedIndex = 0;

  Widget _buildContentWidget() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      constraints: BoxConstraints(maxHeight: 0.65.sh),
      child: GridView.builder(
        shrinkWrap: true,
        // itemCount: data!.length,
        itemCount: data!.length,
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 2.w),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            mainAxisExtent: 168.w,
            crossAxisCount: 2,
            crossAxisSpacing: 10.w,
            mainAxisSpacing: 10.w),
        itemBuilder: (context, index) {
          SystemModel model = data![index];
          bool isSelected = systemId == model.id;
          // bool showCombine = isDiy && model.isNetworked == 1;
          /// mock
          bool showCombine = model.clustered == Clustered.yes.toInt();

          final (borderColor, textColor) = isSelected
              ? (ColorsUtil.highlightTextColor, ColorsUtil.highlightTextColor)
              : (Colors.transparent, ColorsUtil.assistTextColor);

          var children = [
            Container(
              width: 162.5.w,
              height: 120.w,
              decoration: BoxDecoration(
                image: DecorationImage(
                    image: ExtendedNetworkImageProvider(model.img ?? '',
                        cache: true),
                    fit: BoxFit.fitWidth),
                shape: BoxShape.rectangle,
                border: Border.all(color: borderColor, width: 1.w),
                borderRadius: BorderRadius.all(Radius.circular(8.r)),
              ),
            ),
            if (model.isNewInviteSystem)
              Positioned(right: 0, top: 10.w, child: _newSystemIcon())
            else
              Positioned(left: 5.w, top: 5.w, child: _getStatusWidget(model)),
            if (showCombine)
              Positioned(right: 5.w, top: 5.w, child: _combineIcon())
          ];

          var systemName = Text(
            textAlign: TextAlign.center,
            model.name ?? '',
            style: TextStyle(color: textColor, fontSize: 12.sp),
          );

          return InkResponse(
            onTap: () {
              if (!model.isNewInviteSystem) {
                return _selectSystem(model, context);
              }
              var messageModel = MessageHomeInvitationModel(
                id: model.appUserNoticeId!,
                systemName: model.name!,
                installerName: model.installerName!,
                systemId: model.id!,
              );

              MonitorNewDialog.showMonitorHomeSavingsInvitationDialog(
                  context, messageModel, (systemId) {
                _selectSystem(model, context);
              });
            },
            child: SizedBox(
              width: 162.5.w,
              height: 200.w,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Stack(children: children),
                  SizedBox(height: 9.w),
                  systemName
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _selectSystem(SystemModel model, BuildContext context) {
    systemId = model.id;
    setState(() {});
    widget.clickCallBack?.call(systemId!, model);
    Navigator.pop(context);
  }

  /// 新系统提示小图标
  Widget _newSystemIcon() {
    final child = Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2).w,
      decoration: BoxDecoration(
        color: ColorsUtil.themeColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(4),
          topRight: Radius.circular(4),
          bottomLeft: Radius.circular(4),
        ).w,
      ),
      child: Text(
        $t('systemMonitor.new_invitation_system'),
        style: TextStyle(
          color: ColorsUtil.buttonTextColor,
          fontSize: 12.sp,
        ),
      ),
    );

    var triangle = Container(
      width: 0,
      height: 0,
      margin: const EdgeInsets.only(top: 5.9, right: 8).w,
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(width: 8.w, color: Colors.transparent),
          bottom: BorderSide(width: 6.w, color: ColorsUtil.themeColor.darken()),
        ),
      ),
    );

    return Transform(
      transform: Matrix4.translationValues(8.w, 0, 0),
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [child, triangle],
      ),
    );
  }

  /// 并机提示小图标
  Container _combineIcon() {
    return Container(
      height: 20.r,
      width: 20.r,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(16.r)),
        color: ColorsUtil.systemStatusBackgroundColor,
      ),
      child: CustomImageAsset('combine_system_list_icon', width: 15.r),
    );
  }

  Widget _getStatusWidget(SystemModel model) {
    /// 如果deviceStatus为2，表示离线
    var currentState = model.deviceStatus == 2 ? -1 : model.status;
    if (widget.monitorModelStatus != null &&
        model.id == widget.systemId &&
        currentState != widget.monitorModelStatus) {
      logger.i('判断状态不一致，调用接口');
      SystemClientRepository.instance
          .updateSystemDeviceDataGet(widget.systemId!);
      currentState = widget.monitorModelStatus;
    }

    SystemRunStatus status = SystemRunStatus.fromInt(currentState);

    return Container(
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(9.r)),
        color: ColorsUtil.systemStatusBackgroundColor,
      ),
      child: Row(
        children: [
          CustomImageAsset(status.statusIcon(), width: 9.w, height: 9.w),
          SizedBox(width: 5.w),
          Text(
            S.current.text(status.statusText() ?? ''),
            style: TextStyle(color: status.statusColorMap(), fontSize: 10.sp),
          )
        ],
      ),
    );
  }

  void _getSystemList() async {
    try {
      final result = await LoginClientRepository.instance.getLoginAfterInfo();
      isLoading = false;
      data = result.systemList;
      setState(() {});
    } on Object catch (_) {
      isLoading = false;
      data = [];
      setState(() {});
    }
  }
}
