import 'dart:async';

import 'package:flutter_basic/event/receive_energy_flow_event.dart';
import 'package:flutter_basic/platform/bluetooth/ble_exception/ble_failure_exception.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/mqtt/mqtt_manager.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/event_bus_manager.dart';
import 'package:flutter_basic/platform/utils/sp_utils.dart';
import 'package:flutter_basic/repositories/custom_exception/ServiceException.dart';
import 'package:flutter_basic/repositories/login_repository/login_client_repository.dart';
import 'package:flutter_basic/repositories/monitor_repository/monitor_client_repository.dart';
import 'package:flutter_basic/repositories/ota_repository/ota_repository.dart';

import '../../../platform/bluetooth/model/bluetooth_info_model.dart';
import '../../../platform/community/receiver_manager.dart';
import '../../../repositories/combine_repository/model/combine_common_model.dart';
import '../../../repositories/dictionary_repository/dictionary_client_repository.dart';
import '../../../repositories/diy_new_repository/diy_new_client_repository.dart';
import '../../../repositories/mqtt_repository/mqtt_client_repository.dart';
import '../../../repositories/notice_repository/notice_client_repository.dart';
import 'bloc.dart';

class MonitorFetchBloc extends Bloc<MonitorFetchEvent, MonitorFetchState> {
  late StreamSubscription subscription;
  MonitorFetchBloc() : super(const MonitorFetchState()) {
    on<MonitorInfoFetched>(_mapMonitorInfoFetchedToState);
    on<MonitorInfoDialogFetched>(_mapMonitorInfoDialogFetchedToState);
    on<SystemListFetched>(_mapSystemListFetchedToState);
    on<SystemRomCheckFetched>(_mapSystemRomCheckFetchedToState);
    on<MonitorDictListFetched>(_mapDictListFetchStartToState);
    on<MonitorLocalModeFetch>(_mapLocalModeToState);
    on<MonitorCurrentNoticeFetched>(_mapCurrentNoticeFetched);
    on<MonitorEnergyFlowFetched>(_mapEnergyFlowFetchedToState);
    on<MonitorRomUpdateStatus>(_mapRomUpdateStatusFetchedToState);
    on<MonitorClearOTAInfo>(_mapOtaInfoFetchedToState);
    on<MonitorInvitationDialogStatus>(_mapInvitationDialogStatusToState);
    on<MonitorClear>((event, emit) => emit(const MonitorFetchState()));
    // 监听能流图数据
    subscription =
        EventBusManager.eventBus.on<ReceiveEnergyFlowEvent>().listen((event) {
      add(MonitorEnergyFlowFetched(event.model));
    });
  }

  _mapInvitationDialogStatusToState(MonitorInvitationDialogStatus event,
      Emitter<MonitorFetchState> emit) async {
    emit(state.copyWith(
        hasShownInvitationDialog: event.hasShownInvitationDialog));
  }

  _mapOtaInfoFetchedToState(
      MonitorClearOTAInfo event, Emitter<MonitorFetchState> emit) async {
    emit(state.copyWith(otaModel: null, hasCheckRomUpdate: false));
  }

  _mapRomUpdateStatusFetchedToState(
      MonitorRomUpdateStatus event, Emitter<MonitorFetchState> emit) async {
    emit(state.copyWith(hasCheckRomUpdate: event.hasCheckRomUpdate));
  }

  _mapEnergyFlowFetchedToState(
      MonitorEnergyFlowFetched event, Emitter<MonitorFetchState> emit) async {
    emit(state.copyWith(monitorModel: event.model));
  }

  _mapDictListFetchStartToState(
      MonitorDictListFetched event, Emitter<MonitorFetchState> emit) async {
    try {
      final gridConnStatusList =
          await DictionaryClientRepository.instance.getGridConnStatusList();
      emit(state.copyWith(gridConnStatusList: gridConnStatusList));
    } on Exception catch (_) {}
  }

  _mapSystemRomCheckFetchedToState(
      SystemRomCheckFetched event, Emitter<MonitorFetchState> emit) async {
    try {
      emit(state.copyWith(otaModel: null));
      if (event.systemId == null || event.systemId!.isEmpty) return;
      var res = await OtaClientRepository.instance.getOtaList(event.systemId!);
      emit(state.copyWith(otaModel: res, hasCheckRomUpdate: false));
    } catch (e) {
      logger.d('SystemUpdateFetchFailure error ${e.toString()}');
    }
  }

  _mapMonitorInfoFetchedToState(
      MonitorInfoFetched event, Emitter<MonitorFetchState> emit) async {
    emit(state.copyWith(isLoading: true, needLoading: event.needLoading));
    try {
      final result =
          await MonitorClientRepository.instance.getMonitor(event.assetId);
      await SpUtil.putInt(currentSystemIsCluster,
          result.systemVO?.clustered ?? Clustered.no.toInt());
      result.isOffline = result.energyFlowChartVO?.emsGwVo?.gwStatus != 1;
      MqttManager.instance.setIsOnline(!result.isOffline);
      if (result.isOffline) {
        result.energyFlowChartVO?.toPvDirection = 0;
        result.energyFlowChartVO?.pvInfo?.pvPower = null;
      }
      emit(state.copyWith(isLoading: false, monitorModel: result));
    } on ServiceException catch (e) {
      emit(state.copyWith(isLoading: false, error: e));
    } on Exception catch (e, s) {
      logger.d('MonitorFetchFailure error ${e.toString()}');
      logger.d('MonitorFetchFailure stack ${s.toString()}');
      emit(state.copyWith(isLoading: false, error: e));
    }
  }

  _mapMonitorInfoDialogFetchedToState(
      MonitorInfoDialogFetched event, Emitter<MonitorFetchState> emit) async {}

  _mapSystemListFetchedToState(
      SystemListFetched event, Emitter<MonitorFetchState> emit) async {
    try {
      final result = await LoginClientRepository.instance.getLoginAfterInfo();
      emit(state.copyWith(loginAfterInfo: result));
    } on Object catch (_) {
      emit(state.copyWith(error: Exception('Failed to fetch system list')));
    }
  }

  _mapLocalModeToState(
      MonitorLocalModeFetch event, Emitter<MonitorFetchState> emit) async {
    emit(state.copyWith(isLocalModeStart: true));
    try {
      if (!LocalModeManger.instance.isLocalModeNow) {
        String? systemId = SpUtil.getString(currentSystemIdKey, defValue: null);
        if (systemId == null) {
          return;
        }
        final devicesResult =
            await MqttClientRepository.instance.getMqttDevices(systemId);
        //添加设备
        ReceiverManager.instance.addDevices(devicesResult);
        if (MqttManager.instance.gw_sn.isEmpty) {
          throw Exception('查询网关失败');
        }
        var gwSn = MqttManager.instance.gw_sn;
        //开始连接本地模式
        BluetoothInfoModel infoModel =
            await LocalModeManger.instance.startLocalMode(gwSn);
        //开始上报数据
        final result = await DiyNewClientRepository.instance
            .deviceReportByLocalMode(gwSn, infoModel);
        unawaited(LocalModeManger.instance.propertyReport(infoModel));
        //添加设备
        ReceiverManager.instance.addDevices(result);
      }
      emit(state.copyWith(isLocalModeSuccess: true, isLocalModeStart: false));
    } on BleFailureException catch (_) {
      emit(state.copyWith(isLocalModeFailure: true, isLocalModeStart: false));
    } catch (_) {
      LocalModeManger.instance.close();
      emit(state.copyWith(isLocalModeFailure: true, isLocalModeStart: false));
    }
  }

  _mapCurrentNoticeFetched(MonitorCurrentNoticeFetched event,
      Emitter<MonitorFetchState> emit) async {
    try {
      var list = await NoticeClientRepository.instance.getCurrentNotice();
      emit(state.copyWith(
          currentNotices: list, hasShownInvitationDialog: false));
    } on Object catch (_) {
      // emit(state.copyWith(error: Exception('Failed to fetch current notices')));
    }
  }

  @override
  Future<void> close() {
    subscription.cancel();
    return super.close();
  }
}
