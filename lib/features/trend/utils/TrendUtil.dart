import 'dart:math';

import 'package:flutter_basic/repositories/trend_repository/model/cluster/trend_cluster_sta_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/trend_common_model.dart';

import '../components/trend_bar_chart.dart';

class TrendUtil {
  static double getBarMax(List<List<BarData>>? datas) {
    if (datas == null) {
      return 0.0;
    }
    var maxValue = 0.0;
    for (var data in datas) {
      for (var bar in data) {
        maxValue = max(maxValue, bar.data ?? 0.0);
      }
    }
    return getMax(maxValue);
  }

  static double getBarMin(List<List<BarData>>? datas) {
    if (datas == null) {
      return 0.0;
    }
    var minValue = 0.0;
    for (var data in datas) {
      for (var bar in data) {
        minValue = min(minValue, bar.data ?? 0.0);
      }
    }
    // 如果是既有正也有负的情况，绝对值后取最大值
    return getMin(minValue);
  }

  static double getLineMax(List<TrendLineData>? datas) {
    if (datas == null) {
      return 0.0;
    }
    var maxValue = 0.0;
    for (var data in datas) {
      for (var line in data.data) {
        maxValue = max(maxValue, line.val ?? 0.0);
      }
    }
    return getMax(maxValue);
  }

  static double getLineMin(List<TrendLineData>? datas) {
    if (datas == null) {
      return 0.0;
    }
    var minValue = 0.0;
    for (var data in datas) {
      for (var line in data.data) {
        minValue = min(minValue, line.val ?? 0.0);
      }
    }
    return getMin(minValue);
  }

  static double getMax(double value) {
    // if (value == 0) {
    //   return 0;
    // } else if (value <= 0.5) {
    //   return 0.5;
    // } else if (value <= 1) {
    //   return 1;
    // }
    return value;
  }

  static double getMin(double value) {
    // if (value == 0) {
    //   return 0;
    // } else if (value >= -0.5) {
    //   return -0.5;
    // } else if (value >= -1) {
    //   return -1;
    // }
    return value;
  }

  /// Calculate appropriate fractionDigits based on data values
  /// Returns a value between 0 and 2 based on the maximum value in the data
  /// - For large values (≥10), returns 0 decimal places
  /// - For medium values (≥1 but <10), returns 1 decimal place
  /// - For small values (<1), returns 2 decimal places
  static int calculateFractionDigits(double maxValue) {
    if (maxValue >= 10) return 0;
    if (maxValue >= 1) return 1;
    return 2;
  }

  /// Calculate appropriate fractionDigits for StaTrendData list
  /// Examines all numeric fields in the data to find the maximum value
  static int calculateFractionDigitsForStaTrendData(List<StaTrendData> data) {
    // Default to 1 if no data
    if (data.isEmpty) return 1;

    // Find the maximum absolute value in the data
    double maxValue = 0;
    for (var item in data) {
      // Check all numeric fields that might be displayed
      if (item.batteryCharge != null) {
        maxValue = max(maxValue, item.batteryCharge!.abs().toDouble());
      }
      if (item.batteryDischarge != null) {
        maxValue = max(maxValue, item.batteryDischarge!.abs().toDouble());
      }
      if (item.pvChargeAmount != null) {
        maxValue = max(maxValue, item.pvChargeAmount!.abs().toDouble());
      }
      if (item.pv1TotalGen != null) {
        maxValue = max(maxValue, item.pv1TotalGen!.abs().toDouble());
      }
      if (item.pv2TotalGen != null) {
        maxValue = max(maxValue, item.pv2TotalGen!.abs().toDouble());
      }
      if (item.onGridInput != null) {
        maxValue = max(maxValue, item.onGridInput!.abs().toDouble());
      }
      if (item.onGridOut != null) {
        maxValue = max(maxValue, item.onGridOut!.abs().toDouble());
      }
      // Add other relevant fields as needed
    }
    return calculateFractionDigits(maxValue);
  }

  /// Calculate appropriate fractionDigits for BarData list
  /// Examines all data values to find the maximum value
  static int calculateFractionDigitsForBarData(List<List<BarData>> data) {
    if (data.isEmpty) return 1;

    double maxValue = 0;
    for (var dataList in data) {
      for (var item in dataList) {
        if (item.data != null) {
          maxValue = max(maxValue, item.data!.abs());
        }
      }
    }

    return calculateFractionDigits(maxValue);
  }

  /// Calculate appropriate fractionDigits for TrendLineData list
  /// Examines all data values to find the maximum value
  static int calculateFractionDigitsForLineData(List<TrendLineData> data) {
    if (data.isEmpty) return 1;

    double maxValue = 0;
    for (var lineData in data) {
      for (var point in lineData.data) {
        if (point.val != null) {
          maxValue = max(maxValue, point.val!.abs());
        }
      }
    }

    return calculateFractionDigits(maxValue);
  }
}
