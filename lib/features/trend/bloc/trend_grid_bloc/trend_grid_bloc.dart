import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_basic/generated/l10n.dart';
import 'package:flutter_basic/repositories/trend_repository/model/cluster/trend_cluster_grid_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/trend_common_model.dart';
import 'package:flutter_basic/repositories/trend_repository/trend_client_repository.dart';

import '../../../../event/system_no_state_event.dart';
import '../../../../platform/utils/color_utils.dart';
import '../../../../platform/utils/event_bus_manager.dart';
import '../../../../repositories/custom_exception/ServiceException.dart';
import '../../page/trend_page.dart';

part 'trend_grid_event.dart';
part 'trend_grid_state.dart';

class TrendGridBloc extends Bloc<TrendGridEvent, TrendGridState> {
  TrendGridBloc() : super(TrendGridInitial()) {
    on<TrendGridFetch>(_onTrendGridFetch);
  }

  Future<void> _onTrendGridFetch(
    TrendGridFetch event,
    Emitter<TrendGridState> emit,
  ) async {
    emit(TrendGridInProgress());
    try {
      final systemId = event.systemId;
      final startTime = event.startTime;
      final endTime = event.endTime;
      final type = event.type;
      final result =
          await TrendClientRepository.instance.getTrendClusterGridPower(
        systemId: systemId,
        startTime: startTime,
        endTime: endTime,
        type: type,
      );

      final List<TrendClusterGridLineData> trendClusterGridLineDataList = [];
      final List<OnGridTrendClusterSystemList> onGridTrendClusterSystemList =
          result.onGridTrendClusterSystemList ?? [];
      for (int i = 0; i < onGridTrendClusterSystemList.length; i++) {
        final element = onGridTrendClusterSystemList[i];
        if (i == 0 && result.cluster == true) {
          trendClusterGridLineDataList.add(TrendClusterGridLineData(
            cluster: true,
            systemName: element.systemName,
            systemId: element.systemId,
            gridData: [
              TrendLineData(
                data: element.acOnGridTrend ?? [],
                name: S.current.text('trend.cluster_ac_on_grid_power'),
                color: ColorsUtil.chartLineColor1,
                unit: 'W',
              ),
              TrendLineData(
                data: element.gridTrend ?? [],
                name: S.current.text('trend.cluster_grid_power'),
                color: ColorsUtil.disChargeLineColor,
                unit: 'W',
              ),
            ],
            plugData: [
              TrendLineData(
                data: element.otherLoadTrend ?? [],
                name: S.current.text('trend.cluster_other_load_power'),
                color: ColorsUtil.chartLineColor1,
                unit: 'W',
              ),
              TrendLineData(
                data: element.plugTrend ?? [],
                name: S.current.text('trend.cluster_smart_socket_power'),
                color: ColorsUtil.chartLineColor2,
                unit: 'W',
              ),
            ],
          ));
        } else {
          trendClusterGridLineDataList.add(TrendClusterGridLineData(
            cluster: false,
            systemName: element.systemName,
            systemId: element.systemId,
            gridData: [
              TrendLineData(
                data: element.acOnGridTrend ?? [],
                name: S.current.text('trend.ac_on_grid_power'),
                color: ColorsUtil.chartLineColor1,
                unit: 'W',
              ),
              TrendLineData(
                data: element.gridTrend ?? [],
                name: S.current.text('trend.grid_power'),
                color: ColorsUtil.disChargeLineColor,
                unit: 'W',
              ),
            ],
            plugData: [
              TrendLineData(
                data: element.otherLoadTrend ?? [],
                name: S.current.text('trend.other_load_power'),
                color: ColorsUtil.chartLineColor1,
                unit: 'W',
              ),
              TrendLineData(
                data: element.plugTrend ?? [],
                name: S.current.text('trend.smart_socket_power'),
                color: ColorsUtil.chartLineColor2,
                unit: 'W',
              ),
            ],
          ));
        }
      }
      emit(TrendGridSuccess(
          trendClusterGridLineDataList: trendClusterGridLineDataList));
      EventBusManager.eventBus.fire(const SystemNoStateEvent(true));
    } on ServiceException catch (_) {
      if (_.code == noSysNoCode) {
        EventBusManager.eventBus.fire(const SystemNoStateEvent(false));
      }
      emit(TrendGridFailure(code: _.code, msg: _.msg));
    } on Object catch (_) {
      emit(const TrendGridFailure());
    }
  }
}
