import 'dart:async';

import 'package:flutter_basic/features/trend/page/solar_page.dart';
import 'package:flutter_basic/generated/l10n.dart';
import 'package:flutter_basic/repositories/trend_repository/model/cluster/trend_cluster_sta_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../components/custom_tab_bar.dart';
import '../../../components/error/custom_common_fail.dart';
import '../../../components/home_page_top_background.dart';
import '../../../components/no_network_bar.dart';
import '../../../components/no_system_widget.dart';
import '../../../components/page_loading.dart';
import '../../../components/trend_local_mode_bar.dart';
import '../../../event/local_mode_event.dart';
import '../../../platform/bluetooth/local_mode_manger.dart';
import '../../../platform/platform.dart';
import '../../../platform/utils/event_bus_manager.dart';
import '../../system_detail/config/SystemConfig.dart';
import '../bloc/trend_bms_bloc/trend_bms_bloc.dart';
import '../bloc/trend_charge_bloc/trend_charge_bloc.dart';
import '../bloc/trend_grid_bloc/trend_grid_bloc.dart';
import '../bloc/trend_grid_power_bloc.dart';
import '../bloc/trend_load_power_bloc.dart';
import '../bloc/trend_out_put_bloc/trend_out_put_bloc.dart';
import '../bloc/trend_pv_power_bloc/trend_pv_power_bloc.dart';
import '../bloc/trend_soc_bloc/trend_soc_temp_bloc.dart';
import '../bloc/trend_sta_bloc/trend_sta_bloc.dart';
import '../service/trend_service.dart';
import 'ac_on_grid_page.dart';
import 'battery_page.dart';
import 'out_put_page.dart';

class TrendDiyPage extends StatefulWidget {
  final TrendService trendService;
  final bool hasSystem;
  final bool isLoading;
  final Function? fetchTrend;

  const TrendDiyPage(
      {super.key,
      required this.trendService,
      this.hasSystem = false,
      this.isLoading = true,
      this.fetchTrend});

  @override
  State<StatefulWidget> createState() {
    return TrendDiyState();
  }
}

class TrendDiyState extends State<TrendDiyPage> {
  final tabsGroup = [
    CustomTab(
      title: S.current.text('trend.pv'),
      selectIcon: 'trend_solar_s',
      unSelectIcon: 'trend_solar',
    ),
    CustomTab(
      title: S.current.text('trend.battery'),
      selectIcon: 'trend_battery_s',
      unSelectIcon: 'trend_battery',
    ),
    CustomTab(
      title: S.current.text('trend.output'),
      selectIcon: 'trend_output_s',
      unSelectIcon: 'trend_output',
    ),
    CustomTab(
      title: S.current.text('trend.ac_on_grid'),
      selectIcon: 'trend_grid_s',
      unSelectIcon: 'trend_grid',
    ),
  ];

  final trendStaBloc = TrendStaBloc();
  final trendSocTempBloc = TrendSocTempBloc();
  final trendChargeBloc = TrendChargeBloc();
  final trendGridPowerBloc = TrendGridPowerBloc();
  final trendLoadPowerBloc = TrendLoadPowerBloc();
  final trendPvPowerBloc = TrendPvPowerBloc();
  final trendOutPutBloc = TrendOutPutBloc();
  final trendGridBloc = TrendGridBloc();
  final trendBmsBloc = TrendBmsBloc();
  String? systemId;
  var tabIndex = 0;
  StreamSubscription? localModelSubscription;

  @override
  void initState() {
    super.initState();
    widget.trendService.trendRequestStream.listen((event) {
      _getTrendSta(event);
    });
    localModelSubscription =
        EventBusManager.eventBus.on<LocalModeEvent>().listen((event) {
      setState(() {});
    });
  }

  _getTrendSta(TrendRequestModel model) {
    final startTime = _getTime(model.startTime, model.type);
    final endTime = _getTime(model.endTime, model.type);
    final systemId = model.systemId;
    final type = model.type.type;
    trendStaBloc.add(TrendStaFetch(
        startTime: startTime,
        endTime: endTime,
        systemId: systemId,
        type: type));
    trendBmsBloc.add(TrendBmsFetch(
        startTime: startTime,
        endTime: endTime,
        systemId: systemId,
        type: type));
    if (model.type == TrendType.day) {
      trendSocTempBloc.add(TrendSocTempFetch(
          startTime: startTime,
          endTime: endTime,
          systemId: systemId,
          type: type));
      trendChargeBloc.add(TrendChargeFetch(
          startTime: startTime,
          endTime: endTime,
          systemId: systemId,
          type: type));
      trendPvPowerBloc.add(TrendPvPowerFetch(
          startTime: startTime,
          endTime: endTime,
          systemId: systemId,
          type: type));
      trendOutPutBloc.add(TrendOutPutFetch(
          startTime: startTime,
          endTime: endTime,
          systemId: systemId,
          type: type));
      trendGridBloc.add(TrendGridFetch(
          startTime: startTime,
          endTime: endTime,
          systemId: systemId,
          type: type));
    }
  }

  _getTime(String time, TrendType type) {
    switch (type) {
      case TrendType.day:
        return TimeUtils.getTrendToday(time);
      case TrendType.week:
        return TimeUtils.getTrendWeek(time);
      case TrendType.month:
        return TimeUtils.getTrendMonth(time);
      case TrendType.year:
        return TimeUtils.getTrendYear(time);
    }
  }

  @override
  void dispose() {
    super.dispose();
    localModelSubscription?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        const HomePageTopBackground(),
        MultiBlocProvider(
          providers: [
            BlocProvider<TrendStaBloc>(
              create: (BuildContext context) => trendStaBloc,
            ),
            BlocProvider<TrendSocTempBloc>(
              create: (BuildContext context) => trendSocTempBloc,
            ),
            BlocProvider<TrendChargeBloc>(
              create: (BuildContext context) => trendChargeBloc,
            ),
            BlocProvider<TrendGridPowerBloc>(
              create: (BuildContext context) => trendGridPowerBloc,
            ),
            BlocProvider<TrendLoadPowerBloc>(
              create: (BuildContext context) => trendLoadPowerBloc,
            ),
            BlocProvider<TrendPvPowerBloc>(
              create: (BuildContext context) => trendPvPowerBloc,
            ),
            BlocProvider<TrendOutPutBloc>(
              create: (BuildContext context) => trendOutPutBloc,
            ),
            BlocProvider<TrendGridBloc>(
              create: (BuildContext context) => trendGridBloc,
            ),
            BlocProvider<TrendBmsBloc>(
              create: (BuildContext context) => trendBmsBloc,
            ),
          ],
          child: BlocConsumer<TrendStaBloc, TrendStaState>(
            bloc: trendStaBloc,
            builder: (BuildContext context, state) {
              TrendClusterStaModel staModel = TrendClusterStaModel();
              if (state is TrendStaSuccess) {
                staModel = state.model;
                // hasLoad = true;
              }
              return SafeArea(
                  child: Stack(
                children: [
                  widget.isLoading
                      ? const PageLoading()
                      : RefreshIndicator(
                          color: Colors.grey,
                          notificationPredicate: (notification) {
                            return notification.metrics.minScrollExtent == 0;
                          },
                          onRefresh: () async {
                            logger.d('onRefresh');
                            widget.fetchTrend?.call(pullRefresh: true);
                            return Future.value();
                          },
                          child: !SystemConfig.singleton.hasNet
                              ? Center(
                                  child: CustomCommonFail(
                                    onTryAgainTap: () => widget.fetchTrend
                                        ?.call(pullRefresh: true),
                                  ),
                                )
                              : widget.hasSystem
                                  ? Flex(
                                      direction: Axis.vertical,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const NoNetworkBar(),
                                        const SizedBox(
                                          height: 10,
                                        ),
                                        CustomTabBar(
                                          tabList: tabsGroup,
                                          onTabChange: (index) {
                                            setState(() {
                                              tabIndex = index;
                                            });
                                          },
                                        ),
                                        SizedBox(
                                          height: 10.h,
                                        ),
                                        Expanded(
                                          flex: 1,
                                          child: _getTrendPage(
                                              tabIndex,
                                              staModel,
                                              widget.trendService
                                                  .trendRequestModel),
                                        ),
                                      ],
                                    )
                                  : const NoSystemWidget(),
                        ),
                  LocalModeManger.instance.isLocalModeNow
                      ? const Positioned(child: TrendLocalModeBar())
                      : Container(),
                ],
              ));
            },
            listener: (BuildContext context, TrendStaState? state) {
              // if (state is TrendStaInProgress) {
              //   CustomLoading.showLoading('');
              // } else if (state is TrendStaFailure) {
              //   CustomLoading.dismissLoading();
              //   // TODO 需要确定是否弹出toast
              //   // if (state.msg != null) {
              //   //   CustomToast.showToast(context,
              //   //       state.msg ?? S.current.text('common.try_again'));
              //   // }
              // } else if (state is TrendStaSuccess) {
              //   CustomLoading.dismissLoading();
              // }
            },
          ),
        )
      ],
    );
  }

  _getTrendPage(int index, TrendClusterStaModel staModel,
      TrendRequestModel requestModel) {
    switch (index) {
      case 0:
        return _getSolarPage(staModel, requestModel);
      case 1:
        return _getBatteryPage(staModel, requestModel);
      case 2:
        return _getLoadPage(staModel, requestModel);
      case 3:
        return _getPowerGridPage(staModel, requestModel);
    }
  }

  _getSolarPage(TrendClusterStaModel staModel, TrendRequestModel requestModel) {
    return SolarPage(staModel: staModel, requestModel: requestModel);
  }

  _getBatteryPage(
      TrendClusterStaModel staModel, TrendRequestModel requestModel) {
    return BatteryPage(staModel: staModel, requestModel: requestModel);
  }

  _getLoadPage(TrendClusterStaModel staModel, TrendRequestModel requestModel) {
    return OutPutPage(staModel: staModel, requestModel: requestModel);
  }

  _getPowerGridPage(
      TrendClusterStaModel staModel, TrendRequestModel requestModel) {
    return AcOnGridPage(staModel: staModel, requestModel: requestModel);
  }
}
