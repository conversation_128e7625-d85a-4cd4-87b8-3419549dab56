import 'package:flutter_basic/components/custom_card_list.dart';
import 'package:flutter_basic/components/custom_dialog_box.dart';
import 'package:flutter_basic/components/custom_image_asset.dart';
import 'package:flutter_basic/components/custom_switch.dart';
import 'package:flutter_basic/features/monitor/bloc/mqtt/mqtt_bloc.dart';
import 'package:flutter_basic/features/select_map/model/address_model.dart';
import 'package:flutter_basic/features/system_detail/bloc/system_detail_fetch_event.dart';
import 'package:flutter_basic/features/system_detail/config/SystemConfig.dart';
import 'package:flutter_basic/features/system_detail/operate_setting/const/index.dart';
import 'package:flutter_basic/generated/l10n.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/config/system_type.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/repositories/select_map_repository/select_map_client_repository.dart';
import 'package:flutter_basic/repositories/system_repository/model/system_detail/system_detail_response.dart';
import 'package:flutter_basic/router/router.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../../components/cus_InkWell/CusInkWell.dart';
import '../../../../platform/community/monitor/constants.dart';
import '../../../../platform/utils/version.dart';
import '../../../../router/system_feature_routes/system_routes.dart';
import '../../../../router/system_feature_routes/system_routes_type.dart';
import '../bloc/bloc.dart';
import '../../widgets/grid_feeding_power_dialog.dart';

class OperateSettingPage extends StatefulWidget {
  const OperateSettingPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return OperateSettingPageState();
  }
}

class OperateSettingPageState extends State<OperateSettingPage> {
  bool extremeWeatherSwitch = false;
  SystemDetailResponse? systemModel;
  AddressModel? addressModel;
  String? formatAddress;

  /// 当前设备是否有定位信息
  bool get hasLocation =>
      systemModel?.systemDetailCommonRunSetUp?.lat != null &&
      systemModel?.systemDetailCommonRunSetUp?.lng != null;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        defaultLeadingClicked: () {
          var noAddress = addressModel == null &&
              systemModel?.systemDetailCommonRunSetUp?.lat == null;
          if (extremeWeatherSwitch && noAddress) {
            _showNoAddressDialog();
          } else {
            Navigator.pop(context);
          }
        },
        elevation: 0,
        titleText: S.current.text('systemDetail.Operation_Settings'),
      ),
      backgroundColor: ColorsUtil.backgroundColor,
      body: BlocConsumer<OperateSettingBloc, OperateSettingState>(
        listener: _blocListener,
        builder: _blocBuilder,
      ),
    );
  }

  Widget _buildOperationModes() {
    String defaultValue = '-';
    List<CardItemData> operationModes = [
      CardItemData(
          title: S.current.text('systemDetail.Operation_Mode'),
          content: systemModel?.systemDetailCommonRunSetUp?.runModel != null
              ? S.current.text(systemModel
                      ?.systemDetailCommonRunSetUp?.runModelLanguageKey ??
                  '')
              : defaultValue,
          titleFlex: 2,
          onTap: () async {
            dynamic mode = await Navigator.of(context).push(
              SystemRoutes.onGenerateRoute(
                RouteSettings(
                    name: SystemRoutesType.operateModeRoute,
                    arguments: {
                      'systemId': systemModel?.id,
                      'list': systemModel
                              ?.systemDetailCommonRunSetUp?.runModeList ??
                          [],
                      'runMode':
                          systemModel?.systemDetailCommonRunSetUp?.runModel ??
                              -1,
                      'modelKey':
                          systemModel!.systemDetailCommonRunSetUp!.emsModelKey!,
                      'productKey': systemModel!
                          .systemDetailCommonRunSetUp!.emsProductKey!,
                      'deviceNo':
                          systemModel!.systemDetailCommonRunSetUp!.emsDeviceNo!,
                      'workModeIdentify': systemModel!
                          .systemDetailCommonRunSetUp!
                          .runModelFunctionIdentifier!
                    }),
              ),
            );
            if (mode != null) {
              _operateSettingRefresh();
            }
            logger.d(mode);
          }),
      CardItemData(
          onTap: _goChargeSetting,
          titleFlex: 4,
          title: S.current.text('systemDetail.Backup_Power_SOC'),
          content: SystemConfig.doubleToPercentage(
              systemModel?.systemDetailCommonRunSetUp?.dischargeLimit)),
      CardItemData(
          onTap: _goChargeSetting,
          titleFlex: 4,
          title: S.current.text('systemDetail.Maximum_Rechargeable'),
          content: SystemConfig.doubleToPercentage(
              systemModel?.systemDetailCommonRunSetUp?.chargingLimit))
    ];
    return CustomCardList(
      sectionTitle: S.current.text('systemDetail.Operation_Mode'),
      cardList: operationModes,
    );
  }

  Widget _buildGridFeedingSettings() {
    return CustomCardList(
      sectionTitle: S.current.text('systemDetail.Grid_Feeding_Settings'),
      cardList: [
        // 先隐藏，当前版本不支持设置防逆流开关
        // CardItemData(
        //   title: S.current.text('systemDetail.Allow_Grid_Feeding'),
        //   showIcon: false,
        //   child: Padding(
        //     padding: EdgeInsets.only(right: 15.w),
        //     child: CustomSwitch(
        //       value:
        //           systemModel?.systemDetailCommonRunSetUp?.antiBackflowEnable ==
        //               BackFlowEnable.enable.toInt(),
        //       onToggle: _onToggleGridFeeding,
        //     ),
        //   ),
        // ),
        CardItemData(
          onTap: () => _showGridFeedingPowerDialog(),
          titleFlex: 4,
          title: S.current.text('systemDetail.Grid_Feeding_Power'),
          content: systemModel?.systemDetailCommonRunSetUp?.maxFeedPower != null
              ? '${systemModel?.systemDetailCommonRunSetUp?.maxFeedPower}W'
              : '-',
        ),
      ],
    );
  }

  void _onToggleGridFeeding(bool val) {
    // 蓝牙模式
    if (LocalModeManger.instance.isLocalModeNow) {
      var values = [
        [
          diy_point_allow_grid_connect,
          val
              ? BackFlowEnable.enable.toBleString()
              : BackFlowEnable.disable.toBleString()
        ]
      ];
      _operateSettingInvokeWithValues("", values);
    } else {
      _operateSettingInvoke('antiBackflowEnable',
          val ? BackFlowEnable.enable.toInt() : BackFlowEnable.disable.toInt());
    }
  }

  bool _validateGridFeedingPower(String value) {
    if (int.parse(value) >= 0 &&
        int.parse(value) <= SystemConfig.gridPowerDefault) {
      return true;
    }
    CustomToast.showToast(
        context, S.current.text('systemDetail.Grid_Feeding_Power_Limit_Error'));
    return false;
  }

  void _onGridFeedingPowerSelected(dynamic value, dynamic index) {
    var v = int.parse(value.replaceAll('W', ''));
    // 设置馈网功率
    if (LocalModeManger.instance.isLocalModeNow) {
      var values = [
        [diy_point_power_limit, v.toString()]
      ];
      _operateSettingInvokeWithValues("", values);
    } else {
      _operateSettingInvoke("maxFeedPower", v);
    }
  }

  Widget _buildOtherSettings() {
    return CustomCardList(
      sectionTitle: S.current.text('systemDetail.Others'),
      cardList: [
        if (systemModel?.ctDeviceExist == false)
          CardItemData(
              title: S.current.text('device.otherPowerLimit'),
              titleFlex: 4,
              onTap: () async {
                dynamic result = await Navigator.of(context).push(
                  SystemRoutes.onGenerateRoute(
                    RouteSettings(
                        name: SystemRoutesType.powerLimitRoute,
                        arguments: {
                          'data': systemModel?.systemDetailCommonRunSetUp
                                  ?.otherLoadsPowerLimitString ??
                              ''
                        }),
                  ),
                );
                logger.d('result by pop: ${result.toString()}');
                if (result != null) {
                  _operateSettingInvoke(
                      systemModel!.systemDetailCommonRunSetUp!
                          .otherLoadsPowerLimitFunctionIdentifier!,
                      result);
                }
              }),
        CardItemData(
          title: S.current.text('systemDetail.Extreme_Weather_Protection'),
          showIcon: false,
          child: Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: CustomSwitch(
              value: extremeWeatherSwitch,
              onToggle: (val) {
                if (hasLocation) {
                  _operateSetting({'extremeWeatherGuardian': val}, false);
                }
                setState(() => extremeWeatherSwitch = val);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAddressInfo() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 6.0),
          child: Row(
            children: [
              Text(
                '* ',
                style: TextStyle(color: ColorsUtil.systemFaultColor),
              ),
              Text(S.current.text('systemDetail.Address_Info'),
                  style: TextStyle(color: ColorsUtil.assistTextColor)),
            ],
          ),
        ),
        Column(
          children: [
            InkResponse(
              onTap: () => _selectMap(false),
              child: Container(
                padding: EdgeInsets.only(left: 20.w, right: 10.w),
                decoration: BoxDecoration(
                  color: ColorsUtil.aboutCardLineColor,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(8.w).r),
                ),
                height: 44.w,
                alignment: Alignment.center,
                child: Row(
                  children: [
                    CustomImageAsset(
                      'icon_map_selector',
                      width: 20.w,
                      height: 20.w,
                    ),
                    SizedBox(width: 7.w),
                    Expanded(
                      child: Text(
                        S.current.text('systemDetail.Select_on_the_map'),
                        style: TextStyle(
                            color: ColorsUtil.highlightTextColor,
                            fontSize: 13.sp),
                      ),
                    ),
                    CustomImageAsset(
                      'arrow_right_theme_color',
                      width: 20,
                      height: 20,
                    ),
                  ],
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                  color: ColorsUtil.aboutCardColor,
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8.w).r,
                      bottomRight: Radius.circular(8.w).r)),
              alignment: Alignment.center,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CusInkWell(
                    onTap: () => _selectMap(false),
                    child: Padding(
                      padding: EdgeInsets.only(
                          left: 20.w, right: 10.w, top: 13.w, bottom: 15.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(S.current.text('systemDetail.Metro_City'),
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: ColorsUtil.textColor,
                              )),
                          SizedBox(height: 8.w),
                          SizedBox(
                            width: double.infinity,
                            child: Text(
                              addressModel?.formattedAddress ??
                                  formatAddress ??
                                  '-',
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: ColorsUtil.itemValueTextColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 20.w),
                    color: ColorsUtil.aboutCardLineColor,
                    height: 1.h,
                  ),
                  CusInkWell(
                    onTap: () async {
                      dynamic address = await Navigator.of(context).push(
                        PlatformRoutes.onGenerateRoute(
                          RouteSettings(
                              name: PlatformRoutesType.inputPage,
                              arguments: {
                                'value': systemModel
                                        ?.systemDetailCommonRunSetUp?.address ??
                                    '',
                                'emptyTips': S.current
                                    .text('diyNew.address_supplement_is_empty'),
                                'hint': S.current
                                    .text('systemDetail.supplementHint'),
                                'title': S.current
                                    .text('systemDetail.supplementTitle')
                              }),
                        ),
                      );
                      if (address != null) {
                        logger.d('address : ${address as String}');
                        _operateSetting({
                          'address': address,
                        }, false);
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                          left: 20.w, right: 10.w, top: 13.h, bottom: 15.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                              S.current.text('systemDetail.Address_Supplement'),
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: ColorsUtil.textColor,
                              )),
                          SizedBox(height: 8.h),
                          Row(
                            children: [
                              Expanded(
                                  child: Text(
                                      systemModel?.systemDetailCommonRunSetUp
                                              ?.address ??
                                          '-',
                                      style: TextStyle(
                                        fontSize: 13.sp,
                                        color: ColorsUtil.assistTextColor,
                                      ))),
                              CustomImageAsset(
                                'icon_arrow_right_item',
                                width: 20.w,
                                height: 20.h,
                              )
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 20.w)
      ],
    );
  }

  void _goChargeSetting() async {
    var result = await Navigator.of(context).push(
      SystemRoutes.onGenerateRoute(
        RouteSettings(name: SystemRoutesType.chargeSettingRoute, arguments: {
          'leftValue': systemModel?.systemDetailCommonRunSetUp?.dischargeLimit,
          'rightValue': systemModel?.systemDetailCommonRunSetUp?.chargingLimit
        }),
      ),
    );
    logger.d('charglimit result : ${result.toString()}');
    if (result != null) {
      int discharge = (result['discharge'] as double).toInt();
      int charge = (result['charge'] as double).toInt();
      if (LocalModeManger.instance.isLocalModeNow) {
        var values = [
          [diy_point_self_use_charge_limit, "${charge * 10}"],
          [diy_point_self_use_discharge_limit, "${discharge * 10}"]
        ];

        _operateSettingInvokeWithValues("", values);
      } else {
        _operateSettingInvokeWithValues(
            systemModel!.systemDetailCommonRunSetUp!
                .selfChargeDisChargeFunctionIdentifier!,
            [charge, discharge]);
      }
    }
  }

  void _operateSetting(Map<String, dynamic> params, bool needPop) {
    BlocProvider.of<OperateSettingBloc>(context)
        .add(OperateSetting(systemModel!.id!, params, needPop));
  }

  void _operateSettingInvoke(String identify, dynamic value) {
    BlocProvider.of<OperateSettingBloc>(context).add(OperateSettingInvoke(
        value,
        systemModel!.id!,
        systemModel!.systemDetailCommonRunSetUp!.emsModelKey!,
        systemModel!.systemDetailCommonRunSetUp!.emsProductKey!,
        systemModel!.systemDetailCommonRunSetUp!.emsDeviceNo!,
        identify));
  }

  void _operateSettingInvokeWithValues(String identify, List<dynamic> values) {
    BlocProvider.of<OperateSettingBloc>(context).add(OperateSettingInvoke(
        values,
        systemModel!.id!,
        systemModel!.systemDetailCommonRunSetUp!.emsModelKey!,
        systemModel!.systemDetailCommonRunSetUp!.emsProductKey!,
        systemModel!.systemDetailCommonRunSetUp!.emsDeviceNo!,
        identify));
  }

  _getReGeoCode(double lat, double lng) async {
    dynamic result = await SelectMapRepository.instance.reGeocode(lat, lng);
    logger.d('re geo result: ${result.toString()}');
    formatAddress = result;
    setState(() {});
  }

  _showNoAddressDialog() async {
    var res = await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext _) {
          return CustomInputDialogBox(
            title: S.current.text('systemDetail.no_address_title'),
            content: S.current.text('systemDetail.no_address_content'),
            actions: [
              CustomInputButton(
                text: S.current.text('systemDetail.no_address_cancle'),
                onTap: () => Navigator.of(_).pop(false),
              ),
              CustomInputButton(
                text: S.current.text('systemDetail.no_address_ensure'),
                textColor: ColorsUtil.themeColor,
                onTap: () {
                  Navigator.of(_).pop(true);
                  _selectMap(true);
                },
              )
            ],
          );
        });

    if (mounted && res == false) {
      Navigator.of(context).pop(true);
    }
  }

  void _selectMap(bool needPop) async {
    var address = await Navigator.of(context).push(
      SystemRoutes.onGenerateRoute(
        RouteSettings(name: SystemRoutesType.mapSelectRoute, arguments: {
          'latlng': hasLocation
              ? LatLng(
                  systemModel!.systemDetailCommonRunSetUp!.lat!,
                  systemModel!.systemDetailCommonRunSetUp!.lng!,
                )
              : null,
        }),
      ),
    );
    if (address != null) {
      addressModel = address;
      setState(() {});
      _operateSetting({
        'extremeWeatherGuardian': extremeWeatherSwitch,
        'lat': addressModel?.location?.latitude,
        'lng': addressModel?.location?.longitude,
      }, needPop);

      BlocProvider.of<OperateSettingBloc>(context)
          .stream
          .firstWhere((element) => element is OperateSettingSuccess)
          .then((value) {
        if (addressModel?.country != S.current.text('district.DE')) {
          CustomToast.showToast(context,
              S.current.text('systemDetail.select_map_mark_country_not_de'));
        }
      });
    }

    logger.d(address.toString());
    logger.d('needPop : $needPop');
  }

  ///刷新页面
  void _operateSettingRefresh() {
    BlocProvider.of<OperateSettingBloc>(context)
        .add(OperateSettingFetched(systemModel!.id!));
  }

  /// 馈网功能显示 ● 仅当有CT 且 DIY的模式为自发自用时，需要设置馈网内容。APP 版本大于等 V0.0.22
  bool showGridFeeding() {
    // 仅当有CT
    //  第二代DIY
    if (isDIY2(systemModel?.thirdPartyModelKey ?? "")) {
      if (systemModel?.ctDeviceExist == true) {
        return true;
      }
      return false;
    } else {
      // 仅当有CT
      return systemModel?.ctDeviceExist == true &&
          // DIY的模式为自发自用
          (systemModel?.systemDetailCommonRunSetUp?.runModel ==
              OperationMode.spontaneousUse.toInt()) &&
          // APP 版本大于等 V0.0.22
          isGreaterThanV0022(context
                  .read<MqttBloc>()
                  .deviceList
                  .firstWhere((element) => element.productKey == 'HB-EMS')
                  .softVer ??
              "V0.0.0");
    }
  }

  void _blocListener(BuildContext context, OperateSettingState state) {
    if (state is OperateSettingFetchInProgress ||
        state is OperateSettingInProgress) {
      CustomLoading.showLoading(null);
    } else if (state is OperateSettingFetchSuccess) {
      systemModel = state.model;
      extremeWeatherSwitch =
          systemModel?.systemDetailCommonRunSetUp?.extremeWeatherGuardian ??
              false;

      if (extremeWeatherSwitch && hasLocation) {
        _getReGeoCode(systemModel!.systemDetailCommonRunSetUp!.lat!,
            systemModel!.systemDetailCommonRunSetUp!.lng!);
      }

      setState(() {});
      CustomLoading.dismissLoading();
    } else if (state is OperateSettingFetchFailure) {
      CustomLoading.dismissLoading();
    } else if (state is OperateSettingSuccess) {
      CustomLoading.dismissLoading();
      SystemConfig.singleton.systemDetailFetchBloc
          ?.add(SystemDetailInfoFetched(systemModel!.id!, needLoading: false));
    } else if (state is OperateSettingFailure) {
      var msg = state.msg ?? 'common.operateFail';
      CustomToast.showToast(context, S.current.text(msg));
      CustomLoading.dismissLoading();
    }
  }

  Widget _blocBuilder(BuildContext context, OperateSettingState state) {
    return SafeArea(
        bottom: true,
        child: Container(
          padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 10.h),
          child: SingleChildScrollView(
            child: Column(
              children: [
                _buildOperationModes(),
                SizedBox(height: 20.h),
                showGridFeeding() ? _buildGridFeedingSettings() : Container(),
                showGridFeeding() ? SizedBox(height: 20.h) : Container(),
                _buildOtherSettings(),
                SizedBox(height: 20.h),
                extremeWeatherSwitch ? _buildAddressInfo() : Container(),
              ],
            ),
          ),
        ));
  }

  void _showGridFeedingPowerDialog() {
    GridFeedingPowerDialogHelper.showDialog(
      context: context,
      currentValue: systemModel?.systemDetailCommonRunSetUp?.maxFeedPower ?? 0,
      onValueChanged: (value) {
        _onGridFeedingPowerSelected(value.toString(), 0);
      },
    );
  }
}
