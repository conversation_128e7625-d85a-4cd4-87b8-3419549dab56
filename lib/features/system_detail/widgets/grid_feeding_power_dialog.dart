import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/components/cus_InkWell/CusInkWell.dart';
import 'package:flutter_basic/generated/l10n.dart';

class GridFeedingPowerDialogHelper {
  static void showDialog({
    required BuildContext context,
    required int currentValue,
    required ValueChanged<int> onValueChanged,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => GridFeedingPowerDialog(
        currentValue: currentValue,
        onValueChanged: onValueChanged,
      ),
    );
  }
}

class GridFeedingPowerDialog extends StatefulWidget {
  final int currentValue;
  final ValueChanged<int> onValueChanged;

  const GridFeedingPowerDialog({
    Key? key,
    required this.currentValue,
    required this.onValueChanged,
  }) : super(key: key);

  @override
  State<GridFeedingPowerDialog> createState() => _GridFeedingPowerDialogState();
}

class _GridFeedingPowerDialogState extends State<GridFeedingPowerDialog> {
  late int _currentValue;
  final int _minValue = 0;
  final int _maxValue = 800;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.currentValue.clamp(_minValue, _maxValue);
  }

  void _updateValue(int value) {
    if (value >= _minValue && value <= _maxValue) {
      setState(() {
        _currentValue = value;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title bar with close button
          Container(
            height: 56.h,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
              border: Border(
                bottom: BorderSide(
                  color: ColorsUtil.aboutCardLineColor,
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.current.text('systemDetail.Grid_Feeding_Power_Setting'),
                  style: TextStyle(
                    fontSize: 17.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    width: 24.w,
                    height: 24.w,
                    decoration: BoxDecoration(
                      color: ColorsUtil.aboutCardLineColor,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.close,
                      size: 16.sp,
                      color: ColorsUtil.assistTextColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content area
          Padding(
            padding: EdgeInsets.all(20.w),
            child: Column(
              children: [
                // Power control section - all in one card
                Container(
                  padding:
                      EdgeInsets.symmetric(vertical: 24.h, horizontal: 24.w),
                  decoration: BoxDecoration(
                    color: ColorsUtil.backgroundColor,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Column(
                    children: [
                      // Button and power display row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Minus button
                          Opacity(
                            opacity: _currentValue > _minValue ? 1.0 : 0.4,
                            child: GestureDetector(
                              onTap: _currentValue > _minValue
                                  ? () {
                                      _updateValue(_currentValue - 10);
                                    }
                                  : null,
                              child: Container(
                                width: 32.w,
                                height: 32.w,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: ColorsUtil.themeColor,
                                    width: 2,
                                  ),
                                  boxShadow: _currentValue > _minValue
                                      ? [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.05),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ]
                                      : [],
                                ),
                                child: Icon(
                                  Icons.remove,
                                  color: ColorsUtil.themeColor,
                                  size: 18.sp,
                                ),
                              ),
                            ),
                          ),

                          // Power display (centered)
                          Text(
                            '${_currentValue}W',
                            style: TextStyle(
                              fontSize: 32.sp,
                              fontWeight: FontWeight.w600,
                              color: ColorsUtil.themeColor,
                            ),
                          ),

                          // Plus button
                          Opacity(
                            opacity: _currentValue < _maxValue ? 1.0 : 0.4,
                            child: GestureDetector(
                              onTap: _currentValue < _maxValue
                                  ? () {
                                      _updateValue(_currentValue + 10);
                                    }
                                  : null,
                              child: Container(
                                width: 32.w,
                                height: 32.w,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: ColorsUtil.themeColor,
                                    width: 2,
                                  ),
                                  boxShadow: _currentValue < _maxValue
                                      ? [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.05),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ]
                                      : [],
                                ),
                                child: Icon(
                                  Icons.add,
                                  color: ColorsUtil.themeColor,
                                  size: 18.sp,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 32.h),

                      // Slider section
                      Row(
                        children: [
                          Text(
                            '$_minValue',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: ColorsUtil.assistTextColor,
                            ),
                          ),
                          Expanded(
                            child: SliderTheme(
                              data: SliderTheme.of(context).copyWith(
                                activeTrackColor: ColorsUtil.themeColor,
                                inactiveTrackColor:
                                    ColorsUtil.aboutCardLineColor,
                                thumbColor: ColorsUtil.themeColor,
                                overlayColor:
                                    ColorsUtil.themeColor.withOpacity(0.2),
                                thumbShape: RoundSliderThumbShape(
                                  enabledThumbRadius: 12.r,
                                ),
                                trackHeight: 4.h,
                              ),
                              child: Slider(
                                value: _currentValue.toDouble(),
                                min: _minValue.toDouble(),
                                max: _maxValue.toDouble(),
                                divisions: 80,
                                // 80 steps (0, 10, 20, ..., 800)
                                onChanged: (value) {
                                  _updateValue(value.round());
                                },
                              ),
                            ),
                          ),
                          Text(
                            '$_maxValue',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: ColorsUtil.assistTextColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 24.h),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                        flex: 1,
                        child: CusInkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 36.h,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(22.h),
                                color: ColorsUtil.backgroundColor,
                                border: Border.all(
                                    color: ColorsUtil.backgroundColor,
                                    width: 1)),
                            child: Text(
                              S.current.text('common.cancel'),
                              style: TextStyle(
                                  fontSize: 13.sp, color: ColorsUtil.textColor),
                            ),
                          ),
                        )),
                    SizedBox(width: 10.w),
                    Expanded(
                        flex: 1,
                        child: CusInkWell(
                          onTap: () => {
                            widget.onValueChanged(_currentValue),
                            Navigator.of(context).pop(),
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 36.h,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(22.h),
                              color: ColorsUtil.themeColor,
                            ),
                            child: Text(
                              S.current.text('common.confirm'),
                              style: TextStyle(
                                  fontSize: 13.sp, color: Colors.white),
                            ),
                          ),
                        )),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
