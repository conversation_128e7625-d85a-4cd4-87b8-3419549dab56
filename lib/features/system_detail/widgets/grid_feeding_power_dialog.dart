import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/components/cus_InkWell/CusInkWell.dart';
import 'package:flutter_basic/generated/l10n.dart';

class GridFeedingPowerDialogHelper {
  static void showDialog({
    required BuildContext context,
    required int currentValue,
    required ValueChanged<int> onValueChanged,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => GridFeedingPowerDialog(
        currentValue: currentValue,
        onValueChanged: onValueChanged,
      ),
    );
  }
}

class GridFeedingPowerDialog extends StatefulWidget {
  final int currentValue;
  final ValueChanged<int> onValueChanged;

  const GridFeedingPowerDialog({
    Key? key,
    required this.currentValue,
    required this.onValueChanged,
  }) : super(key: key);

  @override
  State<GridFeedingPowerDialog> createState() => _GridFeedingPowerDialogState();
}

class _GridFeedingPowerDialogState extends State<GridFeedingPowerDialog> {
  late int _currentValue;
  final int _minValue = 0;
  final int _maxValue = 800;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.currentValue.clamp(_minValue, _maxValue);
  }

  void _updateValue(int value) {
    if (value >= _minValue && value <= _maxValue) {
      setState(() {
        _currentValue = value;
      });
    }
  }

  Widget _buildControlButton({
    required IconData icon,
    required bool enabled,
    required VoidCallback onTap,
  }) {
    return Opacity(
      opacity: enabled ? 1.0 : 0.4,
      child: GestureDetector(
        onTap: enabled ? onTap : null,
        child: Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            border: Border.all(
              color: ColorsUtil.themeColor,
              width: 2,
            ),
            boxShadow: enabled
                ? [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ]
                : [],
          ),
          child: Icon(
            icon,
            color: ColorsUtil.themeColor,
            size: 20.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildQuickButton(int value, String label) {
    final isSelected = _currentValue == value;
    return GestureDetector(
      onTap: () => _updateValue(value),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: isSelected ? ColorsUtil.themeColor : Colors.transparent,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: ColorsUtil.themeColor,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: isSelected ? Colors.white : ColorsUtil.themeColor,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title bar with close button
          Container(
            height: 56.h,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
              border: Border(
                bottom: BorderSide(
                  color: ColorsUtil.aboutCardLineColor,
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.current.text('systemDetail.Grid_Feeding_Power_Setting'),
                  style: TextStyle(
                    fontSize: 17.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    width: 24.w,
                    height: 24.w,
                    decoration: BoxDecoration(
                      color: ColorsUtil.aboutCardLineColor,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.close,
                      size: 16.sp,
                      color: ColorsUtil.assistTextColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content area
          Padding(
            padding: EdgeInsets.all(20.w),
            child: Column(
              children: [
                // Power control section - all in one card
                Container(
                  padding:
                      EdgeInsets.symmetric(vertical: 24.h, horizontal: 24.w),
                  decoration: BoxDecoration(
                    color: ColorsUtil.backgroundColor,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Column(
                    children: [
                      // Power display with enhanced UI
                      Column(
                        children: [
                          // Main power display
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Minus button
                              _buildControlButton(
                                icon: Icons.remove,
                                enabled: _currentValue > _minValue,
                                onTap: () => _updateValue(_currentValue - 10),
                              ),

                              // Power display (centered)
                              Column(
                                children: [
                                  Text(
                                    '${_currentValue}W',
                                    style: TextStyle(
                                      fontSize: 36.sp,
                                      fontWeight: FontWeight.w700,
                                      color: ColorsUtil.themeColor,
                                    ),
                                  ),
                                  Text(
                                    '当前设置功率',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: ColorsUtil.assistTextColor,
                                    ),
                                  ),
                                ],
                              ),

                              // Plus button
                              _buildControlButton(
                                icon: Icons.add,
                                enabled: _currentValue < _maxValue,
                                onTap: () => _updateValue(_currentValue + 10),
                              ),
                            ],
                          ),

                          SizedBox(height: 20.h),

                          // Quick selection buttons
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildQuickButton(0, '关闭'),
                              _buildQuickButton(200, '200W'),
                              _buildQuickButton(400, '400W'),
                              _buildQuickButton(800, '最大'),
                            ],
                          ),
                        ],
                      ),

                      SizedBox(height: 32.h),

                      // Slider section
                      Row(
                        children: [
                          Text(
                            '$_minValue',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: ColorsUtil.assistTextColor,
                            ),
                          ),
                          Expanded(
                            child: SliderTheme(
                              data: SliderTheme.of(context).copyWith(
                                activeTrackColor: ColorsUtil.themeColor,
                                inactiveTrackColor:
                                    ColorsUtil.aboutCardLineColor,
                                thumbColor: ColorsUtil.themeColor,
                                overlayColor:
                                    ColorsUtil.themeColor.withOpacity(0.2),
                                thumbShape: RoundSliderThumbShape(
                                  enabledThumbRadius: 12.r,
                                ),
                                trackHeight: 4.h,
                              ),
                              child: Slider(
                                value: _currentValue.toDouble(),
                                min: _minValue.toDouble(),
                                max: _maxValue.toDouble(),
                                divisions: 80,
                                // 80 steps (0, 10, 20, ..., 800)
                                onChanged: (value) {
                                  _updateValue(value.round());
                                },
                              ),
                            ),
                          ),
                          Text(
                            '$_maxValue',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: ColorsUtil.assistTextColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 20.h),

                // Information tip
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: ColorsUtil.themeColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16.sp,
                        color: ColorsUtil.themeColor,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          '设置范围：0-800W，建议根据实际用电需求调整',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: ColorsUtil.textColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 24.h),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                        flex: 1,
                        child: CusInkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 36.h,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(22.h),
                                color: ColorsUtil.backgroundColor,
                                border: Border.all(
                                    color: ColorsUtil.backgroundColor,
                                    width: 1)),
                            child: Text(
                              S.current.text('common.cancel'),
                              style: TextStyle(
                                  fontSize: 13.sp, color: ColorsUtil.textColor),
                            ),
                          ),
                        )),
                    SizedBox(width: 10.w),
                    Expanded(
                        flex: 1,
                        child: CusInkWell(
                          onTap: () => {
                            widget.onValueChanged(_currentValue),
                            Navigator.of(context).pop(),
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 36.h,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(22.h),
                              color: ColorsUtil.themeColor,
                            ),
                            child: Text(
                              S.current.text('common.confirm'),
                              style: TextStyle(
                                  fontSize: 13.sp, color: Colors.white),
                            ),
                          ),
                        )),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
