import 'dart:async';

import 'package:flutter_basic/components/cus_InkWell/CusInkWell.dart';
import 'package:flutter_basic/components/custom_card_list.dart';
import 'package:flutter_basic/components/custom_dialog_box.dart';
import 'package:flutter_basic/components/custom_image_asset.dart';
import 'package:flutter_basic/components/custom_switch.dart';
import 'package:flutter_basic/components/delete_button.dart';
import 'package:flutter_basic/event/modify_system_name_event.dart';
import 'package:flutter_basic/features/monitor/bloc/mqtt/mqtt_bloc.dart';
import 'package:flutter_basic/features/select_map/model/address_model.dart';
import 'package:flutter_basic/features/system_detail/config/SystemConfig.dart';
import 'package:flutter_basic/generated/l10n.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/community/monitor/constants.dart';
import 'package:flutter_basic/platform/config/system_type.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/platform/utils/combine.dart';
import 'package:flutter_basic/platform/utils/event_bus_manager.dart';
import 'package:flutter_basic/platform/utils/version.dart';
import 'package:flutter_basic/repositories/combine_repository/model/combine_common_model.dart';
import 'package:flutter_basic/repositories/dictionary_repository/model/time_zone_model.dart';
import 'package:flutter_basic/repositories/select_map_repository/select_map_client_repository.dart';
import 'package:flutter_basic/router/main_feature_routes/main_routes.dart';
import 'package:flutter_basic/router/main_feature_routes/main_routes_type.dart';
import 'package:flutter_basic/router/router.dart';
import 'package:flutter_basic/router/system_feature_routes/system_feature_routes.dart';
import 'package:flutter_page_lifecycle/flutter_page_lifecycle.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../repositories/system_repository/model/system_detail/system_detail_response.dart';
import '../bloc/bloc.dart';

class SystemDetailPage extends StatefulWidget {
  final String systemId;

  const SystemDetailPage({super.key, required this.systemId});

  @override
  State<StatefulWidget> createState() {
    return SystemDetailPageState();
  }
}

class SystemDetailPageState extends State<SystemDetailPage> {
  List<CardItemData> _List = [];
  final _list = [];
  SystemDetailResponse? systemModel;
  List<CardItemData> _basicInfo = [];
  List<CardItemData> _rateList = [];
  List<CardItemData> _operateSettingList = [];
  List<CardItemData> _notifyList = [];
  List<TimeZoneModel> currencyList = [];
  String defaultValue = '-';
  StreamSubscription? subscription;
  bool extremeWeatherSwitch = false;
  AddressModel? addressModel;
  String? formatAddress;

  /// 当前设备是否有定位信息
  bool get hasLocation =>
      systemModel?.systemDetailCommonRunSetUp?.lat != null &&
      systemModel?.systemDetailCommonRunSetUp?.lng != null;
  @override
  void initState() {
    super.initState();
    SystemConfig.singleton.systemDetailFetchBloc =
        BlocProvider.of<SystemDetailFetchBloc>(context);
    subscription =
        EventBusManager.eventBus.on<ModifySystemName>().listen((event) {
      systemModel?.basicInfoVo?.systemName = event.systemName;
      setState(() {});
    });
  }

  @override
  void dispose() {
    subscription?.cancel();
    super.dispose();
  } // void _refreshSystemDetail() {
  //   var bloc = SystemConfig.singleton.systemDetailFetchBloc;
  //   bloc?.add(SystemDetailInfoFetched(systemModel!.id!, needLoading: false));
  // }

  @override
  Widget build(BuildContext context) {
    _List = [];
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: ColorsUtil.backgroundColor,
      appBar: CustomAppBar(
        elevation: 0,
        actions: [
          // CusInkWell(
          //   onTap: () {
          //     Navigator.of(context).push(
          //       SystemRoutes.onGenerateRoute(
          //         RouteSettings(
          //           name: SystemRoutesType.powerRecordRoute,
          //           arguments: {
          //             'systemId': widget.systemId,
          //           },
          //         ),
          //       ),
          //     );
          //   },
          //   child: Container(
          //     margin: EdgeInsets.only(right: 20.w).r,
          //     child: CustomImageAsset(
          //       'icon_system_battery_record',
          //       width: 24.w,
          //       height: 24.w,
          //     ),
          //   ),
          // )
        ],
        titleText: S.current.text('systemDetail.System_Detail'),
      ),
      body: BlocConsumer<SystemDetailFetchBloc, SystemDetailFetchState>(
        listener: (context, state) {
          if (state is SystemDetailFetchInProgress) {
            if (state.needLoading == true) {
              CustomLoading.showLoading(null);
            }
          } else if (state is DeleteSystemInProgress) {
            CustomLoading.showLoading(null);
          } else if (state is SystemDetailFetchSuccess) {
            systemModel = state.model;
            logger.d('SystemDetailFetchSuccess : ${systemModel!.id}');
            extremeWeatherSwitch = systemModel
                    ?.systemDetailCommonRunSetUp?.extremeWeatherGuardian ??
                false;

            if (extremeWeatherSwitch && hasLocation) {
              _getReGeoCode(systemModel!.systemDetailCommonRunSetUp!.lat!,
                  systemModel!.systemDetailCommonRunSetUp!.lng!);
            }
            if (state.isFirstLoad) {
              currencyList = state.currencyModels!;
            }
            buildList();
            setState(() {});
            CustomLoading.dismissLoading();
          } else if (state is SystemDetailFetchFailure ||
              state is DeleteSystemFailure ||
              state is SystemDetailFetchComplete) {
            if (state is DeleteSystemFailure) {
              CustomToast.showToast(context, S.current.text(state.msg));
            }
            CustomLoading.dismissLoading();
          } else if (state is DeleteSystemSuccess) {
            CustomLoading.dismissLoading();
            // 离线提示
            if (state.msg == 'delete.system.gw.device.offline.confirm') {
              return _showInputDialog(true);
            }
            SystemConfig.cleanData();
            Navigator.of(context).pushAndRemoveUntil(
                MainRoutes.onGenerateRoute(
                  const RouteSettings(
                    name: MainRoutesType.rootRoute,
                  ),
                ),
                (route) => false);
          } else if (state is SystemDetailOperateSettingSuccess) {
            BlocProvider.of<SystemDetailFetchBloc>(context).add(
                SystemDetailInfoFetched(systemModel!.id!, needLoading: false));
          } else if (state is SystemDetailOperateSettingFailure) {
            CustomToast.showToast(context, S.current.text(state.msg));
          } else if (state is SystemDetailOperateSettingInProgress) {
            CustomLoading.showLoading(null);
          } else if (state is SystemDetailOperateSettingComplete) {
            CustomLoading.dismissLoading();
          }
        },
        builder: (context, state) {
          final size = MediaQuery.of(context).size;

          return PageLifecycle(
            stateChanged: (appear) async {
              // todo 每次 appear 刷新界面
              logger.d(
                  "SystemDetailPage is ${appear ? "appeared" : "disappeared"}");
            },
            child: SafeArea(
              bottom: true,
              child: Container(
                  color: ColorsUtil.backgroundColor,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Container(
                          color: ColorsUtil.backgroundColor,
                          padding: const EdgeInsets.only(
                              left: 20, right: 20, top: 10),
                          child: Column(
                            children: [
                              CustomCardList(
                                sectionTitle:
                                    S.current.text('systemDetail.Basic_Info'),
                                sectionRightWidget: InkResponse(
                                    onTap: () {
                                      if (systemModel == null) {
                                        return;
                                      }
                                      Navigator.of(context).push(
                                        SystemRoutes.onGenerateRoute(
                                          RouteSettings(
                                              name: SystemRoutesType
                                                  .basicInfoRoute,
                                              arguments: {
                                                'systemModel': systemModel,
                                                'systemId': widget.systemId
                                              }),
                                        ),
                                      );
                                    },
                                    child: SizedBox()),
                                cardList: _basicInfo,
                                itemTap: (index) {},
                              ),
                              SizedBox(height: 20.h),
                              CustomCardList(
                                sectionTitle: S.current
                                    .text('systemDetail.Operation_Settings'),
                                cardList: [
                                  CardItemData(
                                    titleFlex: 2,
                                    title: S.current
                                        .text('systemDetail.Operation_Mode'),
                                    content: systemModel
                                                ?.systemDetailCommonRunSetUp
                                                ?.runModel !=
                                            null
                                        ? S.current.text(systemModel
                                                ?.systemDetailCommonRunSetUp
                                                ?.runModelLanguageKey ??
                                            '')
                                        : '-',
                                    onTap: () async {
                                      dynamic mode =
                                          await Navigator.of(context).push(
                                        SystemRoutes.onGenerateRoute(
                                          RouteSettings(
                                              name: SystemRoutesType
                                                  .operateModeRoute,
                                              arguments: {
                                                'systemId': widget.systemId,
                                                'list': systemModel
                                                        ?.systemDetailCommonRunSetUp
                                                        ?.runModeList ??
                                                    [],
                                                'runMode': systemModel
                                                        ?.systemDetailCommonRunSetUp
                                                        ?.runModel ??
                                                    -1,
                                                'modelKey': systemModel!
                                                    .systemDetailCommonRunSetUp!
                                                    .emsModelKey!,
                                                'productKey': systemModel!
                                                    .systemDetailCommonRunSetUp!
                                                    .emsProductKey!,
                                                'deviceNo': systemModel!
                                                    .systemDetailCommonRunSetUp!
                                                    .emsDeviceNo!,
                                                'workModeIdentify': systemModel!
                                                    .systemDetailCommonRunSetUp!
                                                    .runModelFunctionIdentifier!
                                              }),
                                        ),
                                      );
                                      if (mode != null) {
                                        // 刷新页面
                                        BlocProvider.of<SystemDetailFetchBloc>(
                                                context)
                                            .add(SystemDetailInfoFetched(
                                                systemModel!.id!,
                                                needLoading: false));
                                      }
                                      logger.d(mode);
                                    },
                                  )
                                ],
                              ),
                              SizedBox(height: 20.h),
                              CustomCardList(
                                sectionTitle: S.current
                                    .text('systemDetail.Battery_Settings'),
                                cardList: [
                                  CardItemData(
                                      onTap: _goChargeSetting,
                                      titleFlex: 4,
                                      title: S.current.text(
                                          'systemDetail.Backup_Power_SOC'),
                                      content: SystemConfig.doubleToPercentage(
                                          systemModel
                                              ?.systemDetailCommonRunSetUp
                                              ?.dischargeLimit)),
                                  CardItemData(
                                      onTap: _goChargeSetting,
                                      titleFlex: 4,
                                      title: S.current.text(
                                          'systemDetail.Maximum_Rechargeable'),
                                      content: SystemConfig.doubleToPercentage(
                                          systemModel
                                              ?.systemDetailCommonRunSetUp
                                              ?.chargingLimit))
                                ],
                              ),
                              showGridFeeding()
                                  ? SizedBox(height: 20.h)
                                  : Container(),
                              showGridFeeding()
                                  ? _buildGridFeedingSettings()
                                  : Container(),

                              SizedBox(height: 20.h),
                              _buildOtherSettings(),
                              SizedBox(height: 20.h),
                              extremeWeatherSwitch
                                  ? _buildAddressInfo()
                                  : Container(),
                              CustomCardList(
                                sectionTitle: S.current
                                    .text('systemDetail.Rate_Settings'),
                                sectionRightWidget: InkResponse(
                                  onTap: () {},
                                  child: SizedBox(),
                                ),
                                cardList: _rateList,
                                itemTap: (index) {
                                  if (systemModel == null) {
                                    return;
                                  }

                                  Navigator.of(context).push(
                                    SystemRoutes.onGenerateRoute(
                                      RouteSettings(
                                          name:
                                              SystemRoutesType.rateSettingRoute,
                                          arguments: {
                                            'systemModel': systemModel,
                                            'systemId': widget.systemId,
                                            'currencyList': currencyList
                                          }),
                                    ),
                                  );
                                },
                              ),
                              // SizedBox(height: 20.h),
                              // CustomCardList(
                              //   sectionTitle: S.current
                              //       .text('systemDetail.Notification_Settings'),
                              //   sectionRightWidget: InkResponse(
                              //     onTap: () {
                              //       if (systemModel == null) {
                              //         return;
                              //       }
                              //       Navigator.of(context).push(
                              //         SystemRoutes.onGenerateRoute(
                              //           RouteSettings(
                              //               name: SystemRoutesType
                              //                   .noticeSettingRoute,
                              //               arguments: {
                              //                 'systemModel': systemModel,
                              //                 'systemId': widget.systemId,
                              //               }),
                              //         ),
                              //       );
                              //     },
                              //     child: Row(
                              //       children: [
                              //         Text(
                              //           S.current.text('common.Setting'),
                              //           style: TextStyle(
                              //               color:
                              //                   ColorsUtil.highlightTextColor),
                              //         ),
                              //         CustomImageAsset(
                              //           'arrow_right_theme_color',
                              //           width: 20,
                              //           height: 20,
                              //         )
                              //       ],
                              //     ),
                              //   ),
                              //   cardList: _notifyList,
                              //   itemTap: (index) {},
                              // ),
                              const Padding(padding: EdgeInsets.only(top: 27))
                            ],
                          ),
                        ),
                        if (systemModel != null)
                          DeleteButton(
                              text: isCombine()
                                  ? S.current.text(
                                      'systemDetail.Cancel_System_Combination')
                                  : S.current
                                      .text('systemDetail.Delete_System'),
                              onTap: () {
                                _showInputDialog(false);
                              })
                      ],
                    ),
                  )),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAddressInfo() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 6.0),
          child: Row(
            children: [
              Text(
                '* ',
                style: TextStyle(color: ColorsUtil.systemFaultColor),
              ),
              Text(S.current.text('systemDetail.Address_Info'),
                  style: TextStyle(color: ColorsUtil.assistTextColor)),
            ],
          ),
        ),
        Column(
          children: [
            InkResponse(
              onTap: () => _selectMap(false),
              child: Container(
                padding: EdgeInsets.only(left: 20.w, right: 10.w),
                decoration: BoxDecoration(
                  color: ColorsUtil.aboutCardLineColor,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(8.w).r),
                ),
                height: 44.w,
                alignment: Alignment.center,
                child: Row(
                  children: [
                    CustomImageAsset(
                      'icon_map_selector',
                      width: 20.w,
                      height: 20.w,
                    ),
                    SizedBox(width: 7.w),
                    Expanded(
                      child: Text(
                        S.current.text('systemDetail.Select_on_the_map'),
                        style: TextStyle(
                            color: ColorsUtil.highlightTextColor,
                            fontSize: 13.sp),
                      ),
                    ),
                    CustomImageAsset(
                      'arrow_right_theme_color',
                      width: 20,
                      height: 20,
                    ),
                  ],
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                  color: ColorsUtil.aboutCardColor,
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8.w).r,
                      bottomRight: Radius.circular(8.w).r)),
              alignment: Alignment.center,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CusInkWell(
                    onTap: () => _selectMap(false),
                    child: Padding(
                      padding: EdgeInsets.only(
                          left: 20.w, right: 10.w, top: 13.w, bottom: 15.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(S.current.text('systemDetail.Metro_City'),
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: ColorsUtil.textColor,
                              )),
                          SizedBox(height: 8.w),
                          SizedBox(
                            width: double.infinity,
                            child: Text(
                              addressModel?.formattedAddress ??
                                  formatAddress ??
                                  '-',
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: ColorsUtil.itemValueTextColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 20.w),
                    color: ColorsUtil.aboutCardLineColor,
                    height: 1.h,
                  ),
                  CusInkWell(
                    onTap: () async {
                      dynamic address = await Navigator.of(context).push(
                        PlatformRoutes.onGenerateRoute(
                          RouteSettings(
                              name: PlatformRoutesType.inputPage,
                              arguments: {
                                'value': systemModel
                                        ?.systemDetailCommonRunSetUp?.address ??
                                    '',
                                'emptyTips': S.current
                                    .text('diyNew.address_supplement_is_empty'),
                                'hint': S.current
                                    .text('systemDetail.supplementHint'),
                                'title': S.current
                                    .text('systemDetail.supplementTitle')
                              }),
                        ),
                      );
                      if (address != null) {
                        logger.d('address : ${address as String}');
                        _operateSetting({
                          'address': address,
                        }, false);
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                          left: 20.w, right: 10.w, top: 13.h, bottom: 15.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                              S.current.text('systemDetail.Address_Supplement'),
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: ColorsUtil.textColor,
                              )),
                          SizedBox(height: 8.h),
                          Row(
                            children: [
                              Expanded(
                                  child: Text(
                                      systemModel?.systemDetailCommonRunSetUp
                                              ?.address ??
                                          '-',
                                      style: TextStyle(
                                        fontSize: 13.sp,
                                        color: ColorsUtil.assistTextColor,
                                      ))),
                              CustomImageAsset(
                                'icon_arrow_right_item',
                                width: 20.w,
                                height: 20.h,
                              )
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 20.w)
      ],
    );
  }

  Widget _buildOtherSettings() {
    return CustomCardList(
      sectionTitle: S.current.text('systemDetail.Others'),
      cardList: [
        if (systemModel?.ctDeviceExist == false)
          CardItemData(
              title: S.current.text('device.otherPowerLimit'),
              titleFlex: 4,
              onTap: () async {
                dynamic result = await Navigator.of(context).push(
                  SystemRoutes.onGenerateRoute(
                    RouteSettings(
                        name: SystemRoutesType.powerLimitRoute,
                        arguments: {
                          'data': systemModel?.systemDetailCommonRunSetUp
                                  ?.otherLoadsPowerLimitString ??
                              ''
                        }),
                  ),
                );
                logger.d('result by pop: ${result.toString()}');
                if (result != null) {
                  _operateSettingInvoke(
                      systemModel!.systemDetailCommonRunSetUp!
                          .otherLoadsPowerLimitFunctionIdentifier!,
                      result);
                }
              }),
        CardItemData(
          title: S.current.text('systemDetail.Extreme_Weather_Protection'),
          showIcon: false,
          child: Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: CustomSwitch(
              value: extremeWeatherSwitch,
              onToggle: (val) {
                if (hasLocation) {
                  _operateSetting({'extremeWeatherGuardian': val}, false);
                }
                setState(() => extremeWeatherSwitch = val);
              },
            ),
          ),
        ),
      ],
    );
  }

  void _operateSetting(Map<String, dynamic> params, bool needPop) {
    BlocProvider.of<SystemDetailFetchBloc>(context).add(
        SystemDetailOperateSetting(systemId: systemModel!.id!, params: params));
  }

  void _goChargeSetting() async {
    var result = await Navigator.of(context).push(
      SystemRoutes.onGenerateRoute(
        RouteSettings(name: SystemRoutesType.chargeSettingRoute, arguments: {
          'leftValue': systemModel?.systemDetailCommonRunSetUp?.dischargeLimit,
          'rightValue': systemModel?.systemDetailCommonRunSetUp?.chargingLimit
        }),
      ),
    );
    logger.d('charglimit result : ${result.toString()}');
    if (result != null) {
      int discharge = (result['discharge'] as double).toInt();
      int charge = (result['charge'] as double).toInt();
      if (LocalModeManger.instance.isLocalModeNow) {
        var values = [
          [diy_point_self_use_charge_limit, "${charge * 10}"],
          [diy_point_self_use_discharge_limit, "${discharge * 10}"]
        ];

        _operateSettingInvokeWithValues("", values);
      } else {
        _operateSettingInvokeWithValues(
            systemModel!.systemDetailCommonRunSetUp!
                .selfChargeDisChargeFunctionIdentifier!,
            [charge, discharge]);
      }
    }
  }

  void _operateSettingInvokeWithValues(String identify, List<dynamic> values) {
    BlocProvider.of<SystemDetailFetchBloc>(context).add(
        SystemDetailOperateSettingInvoke(
            systemId: systemModel!.id!,
            deviceNo: systemModel!.systemDetailCommonRunSetUp!.emsDeviceNo!,
            modelKey: systemModel!.systemDetailCommonRunSetUp!.emsModelKey!,
            identify: identify,
            productKey: systemModel!.systemDetailCommonRunSetUp!.emsProductKey!,
            value: values));
  }

  _getReGeoCode(double lat, double lng) async {
    dynamic result = await SelectMapRepository.instance.reGeocode(lat, lng);
    logger.d('re geo result: ${result.toString()}');
    formatAddress = result;
    setState(() {});
  }

  _showNoAddressDialog() async {
    var res = await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext _) {
          return CustomInputDialogBox(
            title: S.current.text('systemDetail.no_address_title'),
            content: S.current.text('systemDetail.no_address_content'),
            actions: [
              CustomInputButton(
                text: S.current.text('systemDetail.no_address_cancle'),
                onTap: () => Navigator.of(_).pop(false),
              ),
              CustomInputButton(
                text: S.current.text('systemDetail.no_address_ensure'),
                textColor: ColorsUtil.themeColor,
                onTap: () {
                  Navigator.of(_).pop(true);
                  _selectMap(true);
                },
              )
            ],
          );
        });

    if (mounted && res == false) {
      Navigator.of(context).pop(true);
    }
  }

  void _selectMap(bool needPop) async {
    var address = await Navigator.of(context).push(
      SystemRoutes.onGenerateRoute(
        RouteSettings(name: SystemRoutesType.mapSelectRoute, arguments: {
          'latlng': hasLocation
              ? LatLng(
                  systemModel!.systemDetailCommonRunSetUp!.lat!,
                  systemModel!.systemDetailCommonRunSetUp!.lng!,
                )
              : null,
        }),
      ),
    );
    if (address != null) {
      addressModel = address;
      setState(() {});
      _operateSetting({
        'extremeWeatherGuardian': extremeWeatherSwitch,
        'lat': addressModel?.location?.latitude,
        'lng': addressModel?.location?.longitude,
      }, needPop);

      BlocProvider.of<SystemDetailFetchBloc>(context)
          .stream
          .firstWhere((element) => element is SystemDetailOperateSettingSuccess)
          .then((value) {
        if (addressModel?.countryCode == 'DE') {
        } else {
          CustomToast.showToast(context,
              S.current.text('systemDetail.select_map_mark_country_not_de'));
        }
      });
    }

    logger.d(address.toString());
    logger.d('needPop : $needPop');
  }

  /// 馈网功能显示 ● 仅当有CT 且 DIY的模式为自发自用时，需要设置馈网内容。APP 版本大于等 V0.0.22
  bool showGridFeeding() {
    //  第二代DIY
    if (isDIY2(systemModel?.thirdPartyModelKey ?? "")) {
      if (systemModel?.ctDeviceExist == true) {
        return true;
      }
      return false;
    } else {
      // 仅当有CT
      return systemModel?.ctDeviceExist == true &&
          // DIY的模式为自发自用
          (systemModel?.systemDetailCommonRunSetUp?.runModel ==
              OperationMode.spontaneousUse.toInt()) &&
          // APP 版本大于等 V0.0.22
          isGreaterThanV0022(context
                  .read<MqttBloc>()
                  .deviceList
                  .firstWhere((element) => element.productKey == 'HB-EMS')
                  .softVer ??
              "V0.0.0");
    }
  }

  Widget _buildGridFeedingSettings() {
    return CustomCardList(
      sectionTitle: S.current.text('systemDetail.Grid_Feeding_Settings'),
      cardList: [
        // 先隐藏，当前版本不支持设置防逆流开关
        // CardItemData(
        //   title: S.current.text('systemDetail.Allow_Grid_Feeding'),
        //   showIcon: false,
        //   child: Padding(
        //     padding: EdgeInsets.only(right: 15.w),
        //     child: CustomSwitch(
        //       value:
        //           systemModel?.systemDetailCommonRunSetUp?.antiBackflowEnable ==
        //               BackFlowEnable.enable.toInt(),
        //       onToggle: _onToggleGridFeeding,
        //     ),
        //   ),
        // ),
        CardItemData(
          onTap: () => _showGridFeedingPowerDialog(),
          titleFlex: 4,
          title: S.current.text('systemDetail.Grid_Feeding_Power'),
          content: systemModel?.systemDetailCommonRunSetUp?.maxFeedPower != null
              ? '${systemModel?.systemDetailCommonRunSetUp?.maxFeedPower}W'
              : '-',
        ),
      ],
    );
  }

  bool _validateGridFeedingPower(String value) {
    if (int.parse(value) >= 0 &&
        int.parse(value) <= SystemConfig.gridPowerDefault) {
      return true;
    }
    CustomToast.showToast(
        context, S.current.text('systemDetail.Grid_Feeding_Power_Limit_Error'));
    return false;
  }

  void _onGridFeedingPowerSelected(dynamic value, dynamic index) {
    var v = int.parse(value.replaceAll('W', ''));
    // 设置馈网功率
    if (LocalModeManger.instance.isLocalModeNow) {
      var values = [
        [diy_point_power_limit, v.toString()]
      ];
      _operateSettingInvokeWithValues("", values);
    } else {
      _operateSettingInvoke("maxFeedPower", v);
    }
  }

  void _operateSettingInvoke(String identify, dynamic value) {
    BlocProvider.of<SystemDetailFetchBloc>(context).add(
        SystemDetailOperateSettingInvoke(
            systemId: systemModel!.id!,
            deviceNo: systemModel!.systemDetailCommonRunSetUp!.emsDeviceNo!,
            modelKey: systemModel!.systemDetailCommonRunSetUp!.emsModelKey!,
            identify: identify,
            productKey: systemModel!.systemDetailCommonRunSetUp!.emsProductKey!,
            value: value));
  }

  void buildList() {
    _basicInfo = [];
    _operateSettingList = [];
    _notifyList = [];
    _rateList = [];
    String defaultValue = '-';
    _basicInfo.add(CardItemData(
        title: '',
        item: InkResponse(
          onTap: () async {
            logger
                .d('system detail model: ${systemModel?.toJson().toString()}');
            dynamic systemName = await Navigator.of(context).push(
              SystemRoutes.onGenerateRoute(
                RouteSettings(
                    name: SystemRoutesType.epcSystemModifyNameRoute,
                    arguments: {
                      'systemName': systemModel?.basicInfoVo?.systemName ?? '',
                      'systemId': widget.systemId
                    }),
              ),
            );
            // if (systemName != null) {
            //   systemModel!.basicInfoVo!.systemName = systemName as String;
            //   setState(() {});
            // }
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(S.current.text('systemDetail.System_Name'),
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: ColorsUtil.textColor,
                  )),
              SizedBox(height: 8.h),
              Row(
                children: [
                  Expanded(
                      child: Text(
                          systemModel?.basicInfoVo?.systemName ?? defaultValue,
                          style: TextStyle(
                            fontSize: 13.sp,
                            color: ColorsUtil.itemValueTextColor,
                          ))),
                  CustomImageAsset(
                    'icon_arrow_right_item',
                    width: 20.w,
                    height: 20.h,
                  ),
                  SizedBox(
                    width: 10.w,
                  )
                ],
              )
            ],
          ),
        )));
    _basicInfo.add(CardItemData(
        title: S.current.text('systemDetail.System_SN'),
        content: systemModel?.basicInfoVo?.systemNo ?? defaultValue,
        showIcon: false));
    // _basicInfo.add(CardItemData(
    //     title: S.current.text('systemDetail.Installer'),
    //     content: systemModel?.basicInfoVo?.installerUserName ?? defaultValue,
    //     showIcon: false));
    // _basicInfo.add(CardItemData(
    //     title: S.current.text('systemDetail.Telephone'),
    //     content: systemModel?.basicInfoVo?.installerUserPhone ?? defaultValue,
    //     showIcon: false));
    // DateFormat dateFormat = DateFormat('yyyy-MM-dd');
    // String installerTime = defaultValue;
    // if (systemModel?.basicInfoVo?.createTime != null) {
    //   installerTime = dateFormat.format(systemModel!.basicInfoVo!.createTime!);
    // }
    // _basicInfo.add(CardItemData(
    //     title: S.current.text('systemDetail.InstallationDate'),
    //     content: installerTime,
    //     showIcon: false));

    // _operateSettingList.add(CardItemData(
    //     titleFlex: 3,
    //     title: S.current.text('systemDetail.Backup_Power_SOC'),
    //     content: SystemConfig.doubleToPercentage(
    //         systemModel?.systemDetailCommonRunSetUp?.dischargeLimit),
    //     showIcon: false));
    // _operateSettingList.add(CardItemData(
    //     titleFlex: 3,
    //     title: S.current.text('systemDetail.Maximum_Rechargeable'),
    //     content: SystemConfig.doubleToPercentage(
    //         systemModel?.systemDetailCommonRunSetUp?.chargingLimit),
    //     showIcon: false));
    // _operateSettingList.add(CardItemData(
    //     titleFlex: 4,
    //     title: S.current.text('systemDetail.Allow_Charge_From_the_Grid'),
    //     content: SystemConfig.getEnableByData(
    //         systemModel?.systemDetailCommonRunSetUp?.gridChargingAllowed),
    //     showIcon: false));
    // _operateSettingList.add(CardItemData(
    //     titleFlex: 4,
    //     title: S.current.text('systemDetail.Allow_Return_to_the_Grid'),
    //     content: SystemConfig.getEnableByData(
    //         systemModel?.systemDetailCommonRunSetUp?.gridFeedbackAllowed),
    //     showIcon: false));
    // _operateSettingList.add(CardItemData(
    //     titleFlex: 4,
    //     title: S.current.text('systemDetail.Extreme_Weather_Protection'),
    //     content: SystemConfig.getEnableByData(
    //         systemModel?.systemDetailCommonRunSetUp?.extremeWeatherGuardian),
    //     showIcon: false));
    _rateList.add(CardItemData(
        title: S.current.text('systemDetail.Currency'),
        content: SystemConfig.getSystemRunModeName(
            systemModel?.rateAllocationVo?.currency, currencyList),
        showIcon: false));
    // _rateList.add(CardItemData(
    //     titleFlex: 4,
    //     title: S.current.text('systemDetail.purchase_unit_price'),
    //     content: systemModel?.rateAllocationVo?.unitPrice?.toString() ??
    //         defaultValue,
    //     showIcon: false));
    _rateList.add(CardItemData(
        titleFlex: 4,
        title: S.current.text('systemDetail.sales_unit_price'),
        content: systemModel?.rateAllocationVo?.sellingPrice?.toString() ??
            defaultValue,
        showIcon: false));

    _notifyList.add(CardItemData(
      showIcon: false,
      titleFlex: 4,
      title: S.current.text('systemDetail.Email_Notification'),
      content: SystemConfig.getEnableByData(
          systemModel?.systemDetailNoticeVo?.email),
    ));
    _notifyList.add(CardItemData(
      showIcon: false,
      titleFlex: 4,
      title: S.current.text('systemDetail.APP_Notification'),
      content: SystemConfig.getEnableByData(
          systemModel?.systemDetailNoticeVo?.inmail),
    ));
    _notifyList.add(CardItemData(
      showIcon: false,
      titleFlex: 4,
      title: S.current.text('systemDetail.PUSH_Notification'),
      content:
          SystemConfig.getEnableByData(systemModel?.systemDetailNoticeVo?.push),
    ));
  }

  void clickItems(CardItemData data) {
    switch (data.title) {}
  }

  TextEditingController _systemDetailController = TextEditingController();

  void _showInputDialog(bool flag) {
    if (systemModel == null) {
      return;
    }
    var suffix = flag ? "_offline" : "";
    _systemDetailController.text = "";
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CustomInputDialogBox(
            title: isCombine()
                ? S.current.text('systemDetail.Cancel_System_Combination')
                : S.current.text('systemDetail.Delete_System$suffix'),
            // textFieldController: _systemDetailController,
            content: isCombine()
                ? S.current
                    .text('systemDetail.Cancel_System_Combination_content')
                : S.current.text('systemDetail.Delete_System_content$suffix'),
            hint: S.current.text('systemDetail.Delete_System_hint$suffix'),
            actions: [
              CustomInputButton(
                text: isCombine()
                    ? S.current.text('common.cancel_combination')
                    : S.current.text('common.cancel_not_combination'),
                textColor: ColorsUtil.textColor,
                onTap: () {
                  Navigator.of(context).pop();
                },
              ),
              CustomInputButton(
                text: S.current.text(isCombine()
                    ? 'systemDetail.Cancel_System_Combination_ok'
                    : flag
                        ? 'systemDetail.Delete_System_ok_offline'
                        : 'common.delete'),
                textColor: ColorsUtil.systemFaultColor,
                onTap: () {
                  // if (_systemDetailController.text != 'Delete') {
                  //   CustomToast.showToast(context,
                  //       S.current.text('systemDetail.Delete_System_hint'));
                  //   return;
                  // }
                  bool isParallel =
                      systemModel!.clustered == Clustered.yes.toInt();
                  BlocProvider.of<SystemDetailFetchBloc>(this.context).add(
                      DeleteSystemEvent(systemModel!.basicInfoVo!.systemNo!,
                          flag, isParallel));
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });
  }

  void _loginPush() {
    Navigator.of(context).push(
      LoginRoutes.onGenerateRoute(
        const RouteSettings(name: LoginRoutesType.loginRoute),
      ),
    );
  }

  void _showGridFeedingPowerDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _GridFeedingPowerDialog(
        currentValue: systemModel?.systemDetailCommonRunSetUp?.maxFeedPower ??
            SystemConfig.gridPowerDefault,
        onValueChanged: (value) {
          _onGridFeedingPowerSelected('${value}W', null);
        },
      ),
    );
  }
}

class _GridFeedingPowerDialog extends StatefulWidget {
  final int currentValue;
  final ValueChanged<int> onValueChanged;

  const _GridFeedingPowerDialog({
    Key? key,
    required this.currentValue,
    required this.onValueChanged,
  }) : super(key: key);

  @override
  State<_GridFeedingPowerDialog> createState() =>
      _GridFeedingPowerDialogState();
}

class _GridFeedingPowerDialogState extends State<_GridFeedingPowerDialog> {
  late int _currentValue;
  final int _minValue = 0;
  final int _maxValue = 800;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.currentValue;
  }

  void _updateValue(int value) {
    if (value >= _minValue && value <= _maxValue) {
      setState(() {
        _currentValue = value;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title bar with close button
          Container(
            height: 56.h,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
              border: Border(
                bottom: BorderSide(
                  color: ColorsUtil.aboutCardLineColor,
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.current.text('systemDetail.Grid_Feeding_Power_Setting'),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    width: 24.w,
                    height: 24.w,
                    decoration: BoxDecoration(
                      color: ColorsUtil.aboutCardLineColor,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.close,
                      size: 16.sp,
                      color: ColorsUtil.assistTextColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content area
          Padding(
            padding: EdgeInsets.all(20.w),
            child: Column(
              children: [
                // Power control section - all in one card
                Container(
                  padding:
                      EdgeInsets.symmetric(vertical: 24.h, horizontal: 24.w),
                  decoration: BoxDecoration(
                    color: ColorsUtil.backgroundColor,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Column(
                    children: [
                      // Button and power display row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Minus button
                          Opacity(
                            opacity: _currentValue > _minValue ? 1.0 : 0.4,
                            child: GestureDetector(
                              onTap: _currentValue > _minValue
                                  ? () {
                                      _updateValue(_currentValue - 10);
                                    }
                                  : null,
                              child: Container(
                                width: 32.w,
                                height: 32.w,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: ColorsUtil.themeColor,
                                    width: 2,
                                  ),
                                  boxShadow: _currentValue > _minValue
                                      ? [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.05),
                                            blurRadius: 4,
                                            offset: Offset(0, 2),
                                          ),
                                        ]
                                      : [],
                                ),
                                child: Icon(
                                  Icons.remove,
                                  color: ColorsUtil.themeColor,
                                  size: 18.sp,
                                ),
                              ),
                            ),
                          ),

                          // Power display (centered)
                          Text(
                            '${_currentValue}W',
                            style: TextStyle(
                              fontSize: 32.sp,
                              fontWeight: FontWeight.w600,
                              color: ColorsUtil.themeColor,
                            ),
                          ),

                          // Plus button
                          Opacity(
                            opacity: _currentValue < _maxValue ? 1.0 : 0.4,
                            child: GestureDetector(
                              onTap: _currentValue < _maxValue
                                  ? () {
                                      _updateValue(_currentValue + 10);
                                    }
                                  : null,
                              child: Container(
                                width: 32.w,
                                height: 32.w,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: ColorsUtil.themeColor,
                                    width: 2,
                                  ),
                                  boxShadow: _currentValue < _maxValue
                                      ? [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.05),
                                            blurRadius: 4,
                                            offset: Offset(0, 2),
                                          ),
                                        ]
                                      : [],
                                ),
                                child: Icon(
                                  Icons.add,
                                  color: ColorsUtil.themeColor,
                                  size: 18.sp,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 20.h),

                      // Slider section
                      Row(
                        children: [
                          Text(
                            '$_minValue',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: ColorsUtil.assistTextColor,
                            ),
                          ),
                          Expanded(
                            child: SliderTheme(
                              data: SliderTheme.of(context).copyWith(
                                activeTrackColor: ColorsUtil.themeColor,
                                inactiveTrackColor:
                                    ColorsUtil.aboutCardLineColor,
                                thumbColor: ColorsUtil.themeColor,
                                overlayColor:
                                    ColorsUtil.themeColor.withOpacity(0.2),
                                thumbShape: RoundSliderThumbShape(
                                  enabledThumbRadius: 12.r,
                                ),
                                trackHeight: 4.h,
                              ),
                              child: Slider(
                                value: _currentValue.toDouble(),
                                min: _minValue.toDouble(),
                                max: _maxValue.toDouble(),
                                divisions: 80,
                                onChanged: (value) {
                                  _updateValue(value.round());
                                },
                              ),
                            ),
                          ),
                          Text(
                            '$_maxValue',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: ColorsUtil.assistTextColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 24.h),

                // Action buttons
                Row(
                  children: [
                    SizedBox(
                      width: 20.w,
                    ),
                    Expanded(
                        flex: 1,
                        child: CusInkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 36.h,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(22.h),
                                color: ColorsUtil.backgroundColor,
                                border: Border.all(
                                    color: ColorsUtil.backgroundColor,
                                    width: 1)),
                            child: Text(
                              S.current.text('common.cancel'),
                              style: TextStyle(
                                  fontSize: 13.sp, color: ColorsUtil.textColor),
                            ),
                          ),
                        )),
                    SizedBox(width: 10.w),
                    Expanded(
                        flex: 1,
                        child: CusInkWell(
                          onTap: () => {
                            widget.onValueChanged(_currentValue),
                            Navigator.of(context).pop(),
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 36.h,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(22.h),
                              color: ColorsUtil.themeColor,
                            ),
                            child: Text(
                              S.current.text('common.confirm'),
                              style: TextStyle(
                                  fontSize: 13.sp, color: Colors.white),
                            ),
                          ),
                        )),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
