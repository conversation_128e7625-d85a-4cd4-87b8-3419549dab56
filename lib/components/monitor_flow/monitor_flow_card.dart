import 'dart:convert';

import 'package:flutter_basic/event/receive_energy_flow_event.dart';
import 'package:flutter_basic/features/devices/config/device_config.dart';
import 'package:flutter_basic/generated/l10n.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/mqtt/mqtt_manager.dart';
import 'package:flutter_basic/platform/utils/event_bus_manager.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/monitor_response_graph_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

import '../../platform/platform.dart';
import '../../platform/utils/color_utils.dart';
import '../../platform/utils/combine.dart';
import '../../repositories/monitor_repository/model/monitor_response_model.dart';
import '../../router/system_feature_routes/system_routes.dart';
import '../../router/system_feature_routes/system_routes_type.dart';
import '../cus_InkWell/CusInkWell.dart';
import '../custom_image_asset.dart';
import '../monitor_popup/monitor_dialog_new.dart';

typedef CardTapCallback = void Function();

class BatteryCard extends StatelessWidget {
  final Widget? child;
  final EdgeInsetsGeometry? margin;
  final CardTapCallback? onTap;
  final int? deviceStatus;

  const BatteryCard({
    super.key,
    this.child,
    this.margin,
    this.onTap,
    this.deviceStatus,
  });

  @override
  Widget build(BuildContext context) {
    final card = NewMonitorCard(
      margin: margin ?? EdgeInsets.zero,
      deviceStatus: deviceStatus,
      onTap: onTap,
      child: child,
    );

    return DecoratedBox(
      decoration: const BoxDecoration(color: Colors.white),
      child: card,
    );
  }
}

class NewMonitorCard extends StatelessWidget {
  final Widget? child;
  final EdgeInsetsGeometry? margin;
  final Widget? image;
  final String? title;
  final String? content;
  final double? width;
  final double? height;
  final CardTapCallback? onTap;
  final int? deviceStatus;

  /// 标签名显示到底部
  final bool bottomTitle;

  const NewMonitorCard({
    super.key,
    this.child,
    this.margin,
    this.image,
    this.title,
    this.content,
    this.width,
    this.height,
    this.onTap,
    this.deviceStatus,
    this.bottomTitle = false,
  });

  @override
  Widget build(BuildContext context) {
    final label = Text(
      title ?? '',
      style: TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: 10.sp,
        color: ColorsUtil.contentColor,
        height: 1,
      ),
    );

    final card = Container(
      margin: margin ?? EdgeInsets.zero,
      decoration: BoxDecoration(
        color: DeviceConfig.getEnergyStatusContentColor(deviceStatus),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: DeviceConfig.getEnergyStatusBorderColor(deviceStatus),
        ),
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 2.r),
            color: ColorsUtil.monitorFlowArrowBgColor,
            blurRadius: 5.r,
          )
        ],
      ),
      child: CusInkWell(
        onTap: onTap,
        child: child ??
            SizedBox(
              width: width,
              height: height,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (image != null) ...[
                    image!,
                    SizedBox(height: 4.w),
                  ],
                  Text(
                    content ?? '',
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: ColorsUtil.textColor,
                    ),
                  ),
                ],
              ),
            ),
      ),
    );

    List<Widget> children = [card];
    if (title != null) {
      if (bottomTitle) {
        children.add(SizedBox(height: 4.w));
        children.add(label);
      } else {
        children.insertAll(0, [
          label,
          SizedBox(height: 4.w),
        ]);
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: children,
    );
  }
}

class NewDeviceCard extends StatelessWidget {
  final Widget? child;
  final EdgeInsetsGeometry? margin;
  final Widget? image;
  final String? title;
  final String? content;
  final double? width;
  final double? height;
  final bool isShowIcon;
  final Axis direction;
  final CardTapCallback? onTap;
  final int? deviceStatus;

  const NewDeviceCard(
      {super.key,
      this.child,
      this.margin,
      this.image,
      this.title,
      this.content,
      this.width,
      this.height,
      this.isShowIcon = true,
      this.direction = Axis.vertical,
      this.onTap,
      this.deviceStatus});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: margin ?? EdgeInsets.zero,
      color: DeviceConfig.getEnergyStatusContentColor(deviceStatus),
      elevation: 0,
      shape: RoundedRectangleBorder(
        side: BorderSide(
            color: DeviceConfig.getEnergyStatusBorderColor(deviceStatus)),
        borderRadius: BorderRadius.all(
          Radius.circular(8.w),
        ),
      ),
      child: CusInkWell(
        onTap: onTap,
        child: child ??
            Container(
              // padding: EdgeInsets.only(
              //   left: direction == Axis.vertical ? 0.w : 15.w,
              // ),
              width: width,
              height: height,
              alignment: Alignment.center,
              child: Flex(
                direction: direction,
                crossAxisAlignment: direction == Axis.vertical
                    ? CrossAxisAlignment.center
                    : CrossAxisAlignment.center,
                mainAxisAlignment: direction == Axis.vertical
                    ? MainAxisAlignment.center
                    : MainAxisAlignment.start,
                children: [
                  image ?? const SizedBox(),
                  direction == Axis.vertical
                      ? SizedBox(
                          height: 6.w,
                        )
                      : SizedBox(
                          width: 10.w,
                        ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title ?? '',
                        maxLines: 1,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 11.sp,
                          color: ColorsUtil.textColor,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 2.w),
                        child: Text(
                          content ?? '',
                          maxLines: 1,
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: ColorsUtil.contentColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
      ),
    );
  }
}

class GridDevice extends StatelessWidget {
  final MonitorResponseModel? model;

  const GridDevice({super.key, this.model});

  @override
  Widget build(BuildContext context) {
    final energyFlowChartVO = model?.energyFlowChartVO;

    var value = energyFlowChartVO?.gridVo?.getPowerDisplayValue() ?? '-';
    if (model?.isOffline == true) {
      value = '-';
    }

    final hasCt = energyFlowChartVO?.ctDevice != null;

    return Stack(
      children: [
        NewMonitorCard(
          // margin: EdgeInsets.only(bottom: hasCt ? 27.w : 0),
          image: CustomImageAsset('new_diy_grid', width: 22.w, height: 22.w),
          title: $t('systemMonitor.energy_card_grid'),
          content: '${value}W',
          width: 52.w,
          height: 56.w,
          onTap: () {
            MonitorNewDialog.showMonitorDialog(context, MonitorDialogType.GRID,
                energyFlowChartVO: energyFlowChartVO);
          },
        ),
        if (hasCt) _buildCtIcon(EnergyFlowCTVO(ctStatus: CTStatusType.normal))
      ],
    );
  }

  /// 渲染CT 显示
  _buildCtIcon(EnergyFlowCTVO ctDevice) => Positioned(
        bottom: 0,
        left: 18.w,
        child: Transform.translate(
          offset: Offset(0, 27.w),
          child: DecoratedBox(
            decoration: BoxDecoration(
              border: Border.all(color: ctDevice.ctStatus.color, width: 2.w),
              borderRadius: BorderRadius.circular(10.w),
              boxShadow: [
                BoxShadow(
                  offset: Offset(0, 2.r),
                  color: ColorsUtil.monitorFlowArrowBgColor,
                  blurRadius: 5.r,
                )
              ],
            ),
            child: CircleAvatar(
              backgroundColor: Colors.white,
              radius: 10.w,
              child: Center(
                child: Opacity(
                  opacity: ctDevice.ctStatus == CTStatusType.offline ? 0.6 : 1,
                  child: CustomImageAsset('monitor_ct', width: 18.w),
                ),
              ),
            ),
          ),
        ),
      );
}

class AcMainDevice extends StatelessWidget {
  final MonitorResponseModel? model;

  const AcMainDevice({super.key, this.model});

  @override
  Widget build(BuildContext context) {
    final energyFlowChartVO = model?.energyFlowChartVO;
    final deviceStatus = energyFlowChartVO?.acMainVo?.deviceStatus;
    return NewDeviceCard(
      deviceStatus: deviceStatus,
      image: CustomImageAsset(
        'monitor_ac_main',
        width: 24.w,
        height: 24.w,
      ),
      title: S.current.text('systemMonitor.energy_card_ac_main'),
      content: '${energyFlowChartVO?.acMainVo?.getPowerDisplayValue() ?? '-'}W',
      width: 110.w,
      height: 82.w,
      onTap: () {
        MonitorNewDialog.showMonitorDialog(context, MonitorDialogType.ACMAIN,
            energyFlowChartVO: energyFlowChartVO);
      },
    );
  }
}

class SmartPlugDevice extends StatelessWidget {
  final MonitorResponseModel? model;

  const SmartPlugDevice({super.key, this.model});

  @override
  Widget build(BuildContext context) {
    final energyFlowChartVO = model?.energyFlowChartVO;
    final deviceStatus = energyFlowChartVO?.plugInfo?.deviceStatus;
    var value = energyFlowChartVO?.plugInfo?.getPowerDisplayValue() ?? '-';
    if (model?.isOffline == true) {
      value = '-';
    }
    return NewMonitorCard(
      deviceStatus: deviceStatus,
      bottomTitle: true,
      image: SizedBox(
        width: 22.w,
        height: 22.w,
        child: Stack(children: [
          CustomImageAsset('new_diy_plug', width: 22.w, height: 22.w),
          if (energyFlowChartVO?.plugInfo?.plugNum != null)
            Transform.translate(
              offset: Offset(13.w, -7.w),
              child: FlowTag(
                title: 'x${energyFlowChartVO?.plugInfo?.plugNum}',
              ),
            )
        ]),
      ),
      title: $t('systemMonitor.energy_card_smart_plug'),
      content: '${value}W',
      width: 52.w,
      height: 56.w,
      onTap: () {
        MonitorNewDialog.showMonitorDialog(context, MonitorDialogType.SMARTPLUG,
            energyFlowChartVO: energyFlowChartVO);
      },
    );
  }
}

class OtherLoadDevice extends StatelessWidget {
  final MonitorResponseModel? model;

  const OtherLoadDevice({super.key, this.model});

  @override
  Widget build(BuildContext context) {
    final energyFlowChartVO = model?.energyFlowChartVO;
    var value = isCombine()
        ? energyFlowChartVO?.otherLoadVo?.getCombinePowerDisplayValue()
        : energyFlowChartVO?.otherLoadVo?.getPowerDisplayValue();
    if (model?.isOffline == true) {
      value = '-';
    }
    String energy_card_other_load = energyFlowChartVO?.ctDevice != null
        ? S.current.text('systemMonitor.energy_card_other_load_has_ct')
        : S.current.text('systemMonitor.energy_card_other_load_no_ct');
    return NewMonitorCard(
      image: CustomImageAsset('new_diy_otherLoad', width: 22.w, height: 22.w),
      title: energy_card_other_load,
      content: '${value ?? '-'}W',
      width: 52.w,
      height: 56.w,
      bottomTitle: true,
      onTap: () {
        final status = model?.energyFlowChartVO?.ctDevice?.ctStatus ??
            CTStatusType.offline;
        if (![CTStatusType.offline, CTStatusType.fault].contains(status)) {
          MonitorNewDialog.showMonitorDialog(
              context, MonitorDialogType.OTHERLOAD,
              energyFlowChartVO: energyFlowChartVO);
        } else {
          _openOtherLoadSetting(context, model);
        }
      },
    );
  }

  Future<void> _openOtherLoadSetting(
      BuildContext context, MonitorResponseModel? model) async {
    final energyFlowChartVO = model?.energyFlowChartVO;
    logger.d(
        'flow card _openOtherLoadSetting : ${energyFlowChartVO?.otherLoadVo?.toJson().toString()}');

    /// 修改完other load值以后，立即再次点击进入设置页面，可能会出现数据未更新的情况
    /// 为了避免这种情况，使用缓存数据，有缓存直接显示缓存的数据
    var data = energyFlowChartVO?.otherLoadVo?.otherLoadJson;

    var list = await Navigator.of(context).push(
      SystemRoutes.onGenerateRoute(
        RouteSettings(name: SystemRoutesType.powerLimitRoute, arguments: {
          'data': data,
          'identifier': energyFlowChartVO?.otherLoadVo?.otherLoadIdentify,
          'innerInvoke': true,
          'modelKey': energyFlowChartVO?.otherLoadVo?.modelKey,
          'productKey': energyFlowChartVO?.otherLoadVo?.productKey,
          'deviceNo': energyFlowChartVO?.otherLoadVo?.deviceNo,
          'from': 'monitor'
        }),
      ),
    );
    if (list is List<Map<String, dynamic>>) {
      var str = jsonEncode(list);
      // SpUtil.putString(key, str);
      model?.energyFlowChartVO?.otherLoadVo?.otherLoadJson = str;
      if (model != null) {
        EventBusManager.eventBus.fire(ReceiveEnergyFlowEvent(model));
      }
    }
  }
}

class PvDevice extends StatelessWidget {
  final MonitorResponseModel? model;

  const PvDevice({super.key, this.model});

  @override
  Widget build(BuildContext context) {
    final energyFlowChartVO = model?.energyFlowChartVO;
    return NewMonitorCard(
      width: 52.w,
      height: 56.w,
      title: $t('systemMonitor.energy_card_pv'),
      content: '${energyFlowChartVO?.pvInfo?.getPowerDisplayValue() ?? '-'}W',
      image: CustomImageAsset('new_diy_pv', width: 22.w, height: 22.w),
      onTap: () {
        MonitorNewDialog.showMonitorDialog(context, MonitorDialogType.PV,
            energyFlowChartVO: energyFlowChartVO);
      },
    );
  }
}

class AcOutputDevice extends StatelessWidget {
  final MonitorResponseModel? model;

  const AcOutputDevice({super.key, this.model});

  @override
  Widget build(BuildContext context) {
    final energyFlowChartVO = model?.energyFlowChartVO;
    var value = energyFlowChartVO?.acInfo?.getPowerDisplayValue() ?? '-';
    if (model?.isOffline == true) {
      value = '-';
    }

    return NewMonitorCard(
      title: $t('systemMonitor.energy_card_ac_output'),
      content: '${value}W',
      width: 52.w,
      height: 56.w,
      bottomTitle: true,
      image: CustomImageAsset('new_diy_ac', width: 22.w, height: 22.w),
      onTap: () {
        MonitorNewDialog.showMonitorDialog(
          context,
          MonitorDialogType.ACOUTPUT,
          energyFlowChartVO: energyFlowChartVO,
          isOffline: model?.isOffline ?? false,
        );
      },
    );
  }
}

class DcOutputDevice extends StatelessWidget {
  final MonitorResponseModel? model;

  const DcOutputDevice({super.key, this.model});

  @override
  Widget build(BuildContext context) {
    final energyFlowChartVO = model?.energyFlowChartVO;
    final deviceStatus = energyFlowChartVO?.dcInfo?.deviceStatus;
    return NewMonitorCard(
      deviceStatus: deviceStatus,
      title: $t('systemMonitor.energy_card_dc_output'),
      content: '${energyFlowChartVO?.dcInfo?.getDCPowerDisplayValue()}W',
      width: 52.w,
      height: 56.w,
      bottomTitle: true,
      image: CustomImageAsset('new_diy_dc', width: 24.w, height: 24.w),
      onTap: () {
        MonitorNewDialog.showMonitorDialog(context, MonitorDialogType.DCOUTPUT,
            energyFlowChartVO: energyFlowChartVO);
      },
    );
  }
}

class BatteryDevice extends StatelessWidget {
  final MonitorResponseModel? model;
  final double? width;
  final double? height;
  final double? padding;

  const BatteryDevice({
    super.key,
    this.model,
    this.width,
    this.height,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final energyFlowChartVO = model?.energyFlowChartVO;
    var soc = energyFlowChartVO?.emsGwVo?.soc;
    var socDisplay = energyFlowChartVO?.emsGwVo?.getSOCDisplayValue() ?? '-';
    var energyRemain = energyFlowChartVO?.emsGwVo?.energyRemain;
    final chargeStatus = energyFlowChartVO?.emsGwVo?.chargeStatus;
    final powerPacks = energyFlowChartVO?.emsGwVo?.powerPacks;
    var deviceStatus = energyFlowChartVO?.emsGwVo?.deviceStatus;
    final slaverList = energyFlowChartVO?.emsGwVo?.slaverList;
    final hasSlaver = (slaverList?.length ?? 0) > 0 ? true : false;
    final slaverListLength = hasSlaver ? (slaverList?.length ?? 0) + 1 : 0;
    final hasPower = powerPacks != null && powerPacks > 0;
    if (model?.isOffline == true) {
      energyRemain = null;
      socDisplay = '-';
      soc = null;
      deviceStatus = 0;
    }

    var iconImage = Stack(alignment: Alignment.center, children: [
      SizedBox(
        width: 44.w,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CustomImageAsset('new_diy_machine', width: 31.w),
            if (hasPower) CustomImageAsset('new_diy_battery', width: 31.w),
          ],
        ),
      ),
      if (hasSlaver)
        Transform.translate(
          offset: Offset(20.w, -8.w),
          child: FlowTag(title: 'x$slaverListLength'),
        ),
      if (hasPower)
        Transform.translate(
          offset: Offset(20.w, 8.w),
          child: FlowTag(title: 'x$powerPacks'),
        ),
    ]);

    return BatteryCard(
      deviceStatus: deviceStatus,
      child: Container(
        width: width ?? 114.w,
        padding: const EdgeInsets.symmetric(vertical: 10).w,
        // height: height,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            iconImage,
            SizedBox(height: 3.w),
            Text(
              '${energyRemain ?? '-'}kWh',
              style: TextStyle(
                color: ColorsUtil.textColor,
                fontSize: 10.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 13.w),
            if (!hasSlaver && soc != null) _buildSocView(soc, socDisplay),
            if (!hasSlaver)
              Row(children: [
                SizedBox(width: 5.w),
                if (chargeStatus != null)
                  CustomImageAsset(_getStatusIcon(chargeStatus),
                      width: 20.w, height: 20.w),
                // 如果功率值为null 这里也不显示
                Text(
                  _getStatusText(energyRemain == null ? null : chargeStatus),
                  style: TextStyle(
                    color: ColorsUtil.contentColor,
                    fontSize: 9.sp,
                  ),
                )
              ]),
          ],
        ),
      ),
    );
  }

  /// 构建soc进度条
  Widget _buildSocView(double soc, String socDisplay) {
    return Row(children: [
      SizedBox(width: 10.w),
      ...List.generate(
        _getPowerLineValue(soc),
        (index) => Container(
          margin: const EdgeInsets.only(right: 2),
          decoration: BoxDecoration(
            color: ColorsUtil.primaryColor,
            borderRadius: BorderRadius.circular(1),
          ),
          height: 10.w,
          width: 2.w,
        ),
      ),
      ...List.generate(
        10 - _getPowerLineValue(soc),
        (index) => Container(
          margin: const EdgeInsets.only(right: 2),
          decoration: BoxDecoration(
            color: ColorsUtil.percent20TextColor,
            borderRadius: BorderRadius.circular(1),
          ),
          height: 10.w,
          width: 2.w,
        ),
      ),
      const Spacer(),
      Text(
        '$socDisplay%',
        style: TextStyle(color: ColorsUtil.contentColor, fontSize: 10.sp),
      ),
      SizedBox(width: 10.w),
    ]);
  }

  int _getPowerLineValue(num value) {
    int v = value.toInt();
    if (v <= 10) {
      return 1;
    }
    if (v >= 100) {
      return 10;
    }
    return v ~/ 10;
  }

  String _getStatusIcon(int? chargeStatus) {
    switch (chargeStatus) {
      case 0:
        return 'monitor_charge_free';
      case 1:
        return 'monitor_charge';
      case 2:
        return 'monitor_dis_charge';
      default:
        return 'monitor_charge_standby';
    }
  }

  String _getStatusText(int? chargeStatus) {
    if (chargeStatus == null) return "-";
    switch (chargeStatus) {
      case 0:
        return S.current.text('systemMonitor.energy_card_battery_free');
      case 1:
        return S.current.text('systemMonitor.energy_card_battery_Charging');
      case 2:
        return S.current.text('systemMonitor.energy_card_battery_Discharging');
      default:
        return S.current.text('systemMonitor.energy_card_battery_Standby');
    }
  }
}

class FlowTag extends StatelessWidget {
  final String? title;
  final double? x;
  final double? y;
  final double? width;
  final double? height;

  const FlowTag(
      {super.key, this.title, this.x, this.y, this.width, this.height});

  @override
  Widget build(BuildContext context) {
    return title != null
        ? Container(
            alignment: Alignment.center,
            width: width,
            height: height,
            child: Container(
              // height: 40,
              padding:
                  EdgeInsets.only(left: 3.w, right: 3.w, top: 0.w, bottom: 0.w),
              decoration: BoxDecoration(
                  color: ColorsUtil.monitorTagBgColor,
                  borderRadius: BorderRadius.circular(20.w),
                  border: Border.all(
                    color: ColorsUtil.monitorTagBorderColor,
                  )),
              child: Text(
                title ?? '',
                style: TextStyle(
                    fontSize: 9.sp,
                    color: ColorsUtil.monitorTagTextColor,
                    fontWeight: FontWeight.w500),
              ),
            ),
          )
        : const SizedBox();
  }
}

class NetworkView extends StatelessWidget {
  final Widget child;
  final MonitorResponseModel? model;
  const NetworkView({super.key, required this.child, this.model});

  @override
  Widget build(BuildContext context) {
    bool isOffline = !MqttManager.instance.isOnlineNow;

    var isLocalMode = LocalModeManger.instance.isLocalModeNow;

    /// wifi状态图标
    var wifiIcon = DeviceConfig.getNetStatus(
        model?.energyFlowChartVO?.emsGwVo?.netType,
        model?.energyFlowChartVO?.emsGwVo?.csqWifi,
        model?.energyFlowChartVO?.emsGwVo?.csq4G,
        isOffline);

    /// 蓝牙状态图标
    var bluetoothIcon = isLocalMode
        ? CustomImageAsset('icon_bluetooth_linked', width: 18.w, height: 18.w)
        : LocalModeManger.instance.autoConnectedStatusNow ==
                AutoLocalModeStatus.progress
            ? Opacity(
                opacity: .5,
                child: Lottie.asset(
                  'lib/assets/json/anim_bluetooth${isWhite ? "" : '_white'}.json',
                  height: 18.w,
                ),
              )
            : CustomImageAsset('icon_bluetooth_unlink',
                width: 18.w, height: 18.w);

    return Stack(
      alignment: Alignment.center,
      children: [
        child,
        Positioned(
          right: 0,
          top: 0,
          child: Transform.translate(
            offset: Offset(0, -20.w),
            child: Row(children: [
              if (!isLocalMode) wifiIcon,
              if (isOffline) bluetoothIcon,
            ]),
          ),
        )
      ],
    );
  }
}
