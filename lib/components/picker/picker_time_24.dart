import 'package:flutter/material.dart';

import 'picker.dart';

class Time24PickerAdapter extends PickerAdapter<int> {
  Time24PickerAdapter({
    Picker? picker,
    this.value,
    this.minuteInterval,
  }) {
    super.picker = picker;
    value ??= DateTime.utc(1970);
  }

  DateTime? value;
  final int? minuteInterval;

  @override
  int getLength() {
    if (_col == 0) {
      return 25; // 0-24 hours
    } else {
      // If hour is 24, only show 00 minutes
      if (picker!.selecteds[0] == 24) {
        return 1;
      }
      // Otherwise show all minutes
      if (minuteInterval == null || minuteInterval! < 2) {
        return 60;
      }

      // Remove 00:00 option - start from first interval (e.g., 00:15)
      return 60 ~/ minuteInterval!;
    }
  }

  @override
  int getMaxLevel() => 2;

  @override
  bool getIsLinkage() => true;

  int _col = 0;

  @override
  void setColumn(int index) {
    if (index != -1 && _col == index + 1) return;
    _col = index + 1;
  }

  @override
  void initSelects() {
    if (picker!.selecteds == null) picker!.selecteds = <int>[];
    if (picker!.selecteds.isEmpty) {
      // Check if the value represents 24:00 (which would be day 2 in the DateTime)
      if (value!.day > 1 && value!.hour == 0 && value!.minute == 0) {
        picker!.selecteds.add(24); // Set to 24 hours
      } else {
        picker!.selecteds.add(value!.hour);
      }

      // Special handling for minutes to ensure 00:00 is always selectable
      if (value!.hour == 0 && value!.minute == 0) {
        picker!.selecteds.add(0); // For 00:00
      } else if (minuteInterval == null || minuteInterval! < 2) {
        picker!.selecteds.add(value!.minute);
      } else {
        // Ensure we handle 00:00 correctly
        picker!.selecteds.add(value!.minute ~/ minuteInterval!);
      }
    }
  }

  @override
  Widget buildItem(BuildContext context, int index) {
    String _text = "";
    if (_col == 0) {
      _text = "${index.toString().padLeft(2, '0')}";
    } else {
      if (picker!.selecteds[0] == 24) {
        _text = "00";
      } else {
        if (minuteInterval == null || minuteInterval! < 2) {
          _text = "${index.toString().padLeft(2, '0')}";
        } else {
          // Add special case for first item when hour is 0
          if (picker!.selecteds[0] == 0 && index == 0) {
            _text = "00"; // Always show 00:00
          } else {
            // Calculate the actual minute value based on the index and interval
            int minuteValue = (index * minuteInterval!);
            // First index should be 0, others follow the interval
            if (picker!.selecteds[0] == 0 && index > 0) {
              minuteValue = ((index - 1) * minuteInterval!) + minuteInterval!;
            }
            _text = "${minuteValue.toString().padLeft(2, '0')}";
          }
        }
      }
    }

    final isSel = picker!.selecteds[_col] == index;
    if (picker!.onBuilderItem != null) {
      final _v =
          picker!.onBuilderItem!(context, _text, null, isSel, _col, index);
      if (_v != null) return makeText(_v, null, isSel);
    }
    return makeText(null, _text, isSel);
  }

  @override
  void doSelect(int column, int index) {
    if (column == 0) {
      // When hour changes, update minutes if needed
      if (index == 24) {
        // If hour is 24, set minutes to 0
        picker!.selecteds[1] = 0;
        picker!.updateColumn(1);
      } else if (picker!.selecteds[0] == 24) {
        // If changing from 24 to another hour, reset minutes
        picker!.selecteds[1] = 0;
        picker!.updateColumn(1);
      }
    }

    // Update the value property based on the selected values
    List<int> selectedValues = getSelectedValues();
    if (selectedValues.length >= 2) {
      int hour = selectedValues[0];
      int minute = selectedValues[1];

      if (hour == 24) {
        // For 24:00, we need to create a DateTime for the next day at 00:00
        final now = DateTime.utc(1970);
        value = DateTime(now.year, now.month, now.day + 1, 0, 0);
      } else {
        value = DateTime(value!.year, value!.month, value!.day, hour, minute);
      }
    }
  }

  @override
  void doShow() {
    for (int i = 0; i < getMaxLevel(); i++) {
      if (i == 0) {
        // Check if the value represents 24:00 (which would be day 2 in the DateTime)
        if (value!.day > 1 && value!.hour == 0 && value!.minute == 0) {
          picker!.selecteds[i] = 24; // Set to 24 hours
        } else {
          picker!.selecteds[i] = value!.hour;
        }
      } else {
        // Special handling for 00:00
        if (value!.hour == 0 && value!.minute == 0) {
          picker!.selecteds[i] = 0;
        } else if (minuteInterval == null || minuteInterval! < 2) {
          picker!.selecteds[i] = value!.minute;
        } else {
          picker!.selecteds[i] = value!.minute ~/ minuteInterval!;
        }
      }
    }
  }

  @override
  List<int> getSelectedValues() {
    List<int> _items = [];
    for (int i = 0; i < picker!.selecteds.length; i++) {
      int j = picker!.selecteds[i];
      if (i == 0) {
        _items.add(j);
        // If hour is 24, update the value to represent 24:00
        if (j == 24) {
          final now = DateTime.utc(1970);
          value = DateTime(now.year, now.month, now.day + 1, 0, 0);
        }
      } else {
        // Special case for 00:00
        if (picker!.selecteds[0] == 0 && j == 0) {
          _items.add(0); // Always 0 for 00:00
        } else if (minuteInterval == null || minuteInterval! < 2) {
          _items.add(j);
        } else {
          // Handle the special case for hour 0
          if (picker!.selecteds[0] == 0 && j > 0) {
            _items.add(((j - 1) * minuteInterval!) + minuteInterval!);
          } else {
            // Calculate the minute value based on the selected index and interval
            int minuteValue = j * minuteInterval!;
            _items.add(minuteValue);
          }
        }
      }
    }
    return _items;
  }

  void updateValue(DateTime time) {
    this.value = time;
    notifyDataChanged();
  }

  /// Updates the picker with a time string in format "HH:MM"
  void updateTimeString(String timeString) {
    if (timeString == "24:00") {
      // For 24:00, we need to create a DateTime for the next day at 00:00
      final now = DateTime.utc(1970);
      this.value = DateTime(now.year, now.month, now.day + 1, 0, 0);
    } else {
      // Parse the time string
      final parts = timeString.split(":");
      if (parts.length == 2) {
        final hour = int.tryParse(parts[0]) ?? 0;
        final minute = int.tryParse(parts[1]) ?? 0;
        final now = DateTime.utc(1970);
        this.value = DateTime(now.year, now.month, now.day, hour, minute);
      }
    }
    notifyDataChanged();
  }
}
