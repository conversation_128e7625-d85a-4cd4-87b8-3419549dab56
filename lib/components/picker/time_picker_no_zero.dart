import 'package:flutter/material.dart';

import 'picker.dart';

/// A time picker adapter that excludes 00:00 as an option
/// This is similar to Time24PickerNoZeroAdapter but without 24:00 option
class TimePickerNOZeroAdapter extends PickerAdapter<int> {
  TimePickerNOZeroAdapter({
    Picker? picker,
    this.value,
    this.minuteInterval,
  }) {
    super.picker = picker;
    value ??= DateTime.utc(1970);

    // If value is 00:00, adjust it to the first available interval
    if (value!.hour == 0 && value!.minute == 0) {
      if (minuteInterval != null && minuteInterval! > 1) {
        // Set to first interval (e.g., 00:15)
        value =
            DateTime(value!.year, value!.month, value!.day, 0, minuteInterval!);
      } else {
        // Set to 00:01 if no interval
        value = DateTime(value!.year, value!.month, value!.day, 0, 1);
      }
    }
  }

  DateTime? value;
  final int? minuteInterval;

  @override
  int getLength() {
    if (_col == 0) {
      return 24; // 0-23 hours (no 24 hour option)
    } else {
      // For hour 0, exclude the 00:00 option
      if (picker!.selecteds[0] == 0) {
        if (minuteInterval == null || minuteInterval! < 2) {
          return 59; // 01-59 minutes (excluding 00)
        } else {
          return 60 ~/
              minuteInterval!; // Normal intervals, but start from first interval
        }
      }

      // For other hours, show all minutes normally
      if (minuteInterval == null || minuteInterval! < 2) {
        return 60;
      }
      return 60 ~/ minuteInterval!;
    }
  }

  @override
  int getMaxLevel() => 2;

  @override
  bool getIsLinkage() => true;

  int _col = 0;

  @override
  void setColumn(int index) {
    if (index != -1 && _col == index + 1) return;
    _col = index + 1;
  }

  @override
  void initSelects() {
    if (picker!.selecteds.isEmpty) {
      picker!.selecteds.add(value!.hour);

      // Handle minute selection, excluding 00:00
      if (value!.hour == 0 && value!.minute == 0) {
        // For 00:00, set to first available option
        picker!.selecteds.add(0); // This will map to first non-zero option
      } else if (minuteInterval == null || minuteInterval! < 2) {
        // For regular minutes, but handle hour 0 specially
        if (value!.hour == 0) {
          picker!.selecteds.add(value!.minute - 1); // Offset by 1 for hour 0
        } else {
          picker!.selecteds.add(value!.minute);
        }
      } else {
        // For intervals
        if (value!.hour == 0) {
          picker!.selecteds.add((value!.minute ~/ minuteInterval!) - 1);
        } else {
          picker!.selecteds.add(value!.minute ~/ minuteInterval!);
        }
      }
    }
  }

  @override
  Widget buildItem(BuildContext context, int index) {
    String _text = "";
    if (_col == 0) {
      _text = "${index.toString().padLeft(2, '0')}";
    } else {
      if (picker!.selecteds[0] == 0) {
        // For hour 0, exclude 00:00 and start from first available option
        if (minuteInterval == null || minuteInterval! < 2) {
          // Start from 01 instead of 00
          _text = "${(index + 1).toString().padLeft(2, '0')}";
        } else {
          // Start from first interval (e.g., 15, 30, 45)
          int minuteValue = (index + 1) * minuteInterval!;
          _text = "${minuteValue.toString().padLeft(2, '0')}";
        }
      } else {
        // For other hours, normal behavior
        if (minuteInterval == null || minuteInterval! < 2) {
          _text = "${index.toString().padLeft(2, '0')}";
        } else {
          int minuteValue = index * minuteInterval!;
          _text = "${minuteValue.toString().padLeft(2, '0')}";
        }
      }
    }

    final isSel = picker!.selecteds[_col] == index;
    if (picker!.onBuilderItem != null) {
      final _v =
          picker!.onBuilderItem!(context, _text, null, isSel, _col, index);
      if (_v != null) return makeText(_v, null, isSel);
    }
    return makeText(null, _text, isSel);
  }

  @override
  void doSelect(int column, int index) {
    // Update the value property based on the selected values
    List<int> selectedValues = getSelectedValues();
    if (selectedValues.length >= 2) {
      int hour = selectedValues[0];
      int minute = selectedValues[1];
      value = DateTime(value!.year, value!.month, value!.day, hour, minute);
    }
  }

  @override
  void doShow() {
    for (int i = 0; i < getMaxLevel(); i++) {
      if (i == 0) {
        picker!.selecteds[i] = value!.hour;
      } else {
        // Handle minute selection, excluding 00:00
        if (value!.hour == 0) {
          if (minuteInterval == null || minuteInterval! < 2) {
            // For hour 0, offset by 1 (01-59 instead of 00-59)
            picker!.selecteds[i] = (value!.minute == 0) ? 0 : value!.minute - 1;
          } else {
            // For intervals with hour 0, adjust the index
            picker!.selecteds[i] = (value!.minute == 0)
                ? 0
                : (value!.minute ~/ minuteInterval!) - 1;
          }
        } else {
          // Normal behavior for other hours
          if (minuteInterval == null || minuteInterval! < 2) {
            picker!.selecteds[i] = value!.minute;
          } else {
            picker!.selecteds[i] = value!.minute ~/ minuteInterval!;
          }
        }
      }
    }
  }

  @override
  List<int> getSelectedValues() {
    List<int> _items = [];
    for (int i = 0; i < picker!.selecteds.length; i++) {
      int j = picker!.selecteds[i];
      if (i == 0) {
        _items.add(j);
      } else {
        // Handle minute values, excluding 00:00
        if (picker!.selecteds[0] == 0) {
          // For hour 0, we skip 00:00
          if (minuteInterval == null || minuteInterval! < 2) {
            _items.add(j + 1); // Add 1 to skip 00 minute
          } else {
            _items.add((j + 1) * minuteInterval!); // Start from first interval
          }
        } else {
          // Normal behavior for other hours
          if (minuteInterval == null || minuteInterval! < 2) {
            _items.add(j);
          } else {
            _items.add(j * minuteInterval!);
          }
        }
      }
    }
    return _items;
  }

  void updateValue(DateTime time) {
    this.value = time;
    notifyDataChanged();
  }

  /// Updates the picker with a time string in format "HH:MM"
  void updateTimeString(String timeString) {
    // Parse the time string
    final parts = timeString.split(":");
    if (parts.length == 2) {
      final hour = int.tryParse(parts[0]) ?? 0;
      final minute = int.tryParse(parts[1]) ?? 0;

      // Avoid 00:00, use first interval instead
      if (hour == 0 &&
          minute == 0 &&
          minuteInterval != null &&
          minuteInterval! > 1) {
        final now = DateTime.utc(1970);
        this.value = DateTime(now.year, now.month, now.day, 0, minuteInterval!);
      } else {
        final now = DateTime.utc(1970);
        this.value = DateTime(now.year, now.month, now.day, hour, minute);
      }
    }
    notifyDataChanged();
  }
}
