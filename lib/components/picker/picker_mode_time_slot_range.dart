import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/components/picker/flutter_picker.dart';
import 'package:flutter_basic/components/picker/picker_time_24_no_zero.dart';
import 'package:flutter_basic/components/picker/time_picker_no_zero.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/repositories/installer_repository/model/power_setting_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../features/monitor/bloc/monitor_fetch_bloc.dart';
import '../../features/monitor/bloc/mqtt/mqtt_bloc.dart';
import '../../platform/utils/version.dart';
import '../cus_InkWell/CusInkWell.dart';

typedef _ClickCallBack = void Function(
    dynamic leftSelectValue, dynamic rightSelectValue, TimeShareType type);

enum PickerType {
  string,
  array,
}

class TouModeTimeSlotRangePicker {
  static bool _isShowPicker = false;

  /// 单列
  /// 单列选择器返回选中行对象和index
  static void showStringPicker<T>(
    BuildContext context, {
    required List data,
    String? title,
    String? labelKey, // 对象数组的文字字段
    int selectIndex = 0,
    _ClickCallBack? clickCallBack,
    String? startTime,
    String? endTime,
    double? height,
    int? minuteInterval = 1,
    TimeShareType? chargeType,
  }) {
    if (_isShowPicker || data.isEmpty) {
      return;
    }
    _isShowPicker = true;

    showModalBottomSheet(
      useSafeArea: true,
      context: context,
      // 设置圆角
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      // 抗锯齿
      clipBehavior: Clip.antiAlias,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return TimeSlotPickerView(
          startTime: startTime,
          endTime: endTime,
          height: height,
          data: data,
          chargeType: chargeType,
          title: title,
          selecteds: [selectIndex],
          pickerType: PickerType.string,
          minuteInterval: minuteInterval,
          adapter: labelKey != null
              ? PickerDataAdapter(
                  pickerData: data.map((e) => e[labelKey]).toList())
              : PickerDataAdapter(pickerData: data),
          clickCallBack: clickCallBack,
        );
      },
    ).then((value) => _isShowPicker = false);
  }
}

/// 自定义picker
class TimeSlotPickerView extends StatefulWidget {
  const TimeSlotPickerView({
    Key? key,
    this.data,
    this.title,
    this.selecteds,
    this.pickerType,
    required this.adapter,
    this.clickCallBack,
    this.height,
    this.startTime,
    this.chargeType,
    this.endTime,
    this.minuteInterval,
  }) : super(key: key);

  final List? data;
  final String? title;
  final List<int>? selecteds;
  final PickerType? pickerType;
  final PickerAdapter adapter;
  final _ClickCallBack? clickCallBack;
  final double? height;
  final String? startTime;
  final String? endTime;

  final TimeShareType? chargeType;
  final int? minuteInterval;

  @override
  State<TimeSlotPickerView> createState() => TimeSlotPickerViewState();
}

class TimeSlotPickerViewState extends State<TimeSlotPickerView> {
  late Picker leftPicker;
  late Picker rightPicker;
  bool clickedSecondary = false;
  late TimeShareType _selectType = widget.chargeType ?? TimeShareType.charge;

  @override
  void initState() {
    super.initState();
    clickedSecondary = false;
  }

  @override
  Widget build(BuildContext context) {
    return _body();
  }

  _body() {
    var headerColor = ColorsUtil.windowBgColor;
    var selectTextColor = ColorsUtil.textColor;
    var selectItemBgColor = Colors.grey.withOpacity(0.15);
    var itemExtent = 36.h;
    var delimitWidth = 50.w;
    var innerDelimitWidth = 30.w;
    var delimitPadding = 20.w;
    final delimiterChild = Align(
      alignment: Alignment.center,
      child: Container(
          width: delimitWidth, height: itemExtent, color: selectItemBgColor),
    );
    final leftLeftDelimiterChild = Align(
      alignment: Alignment.center,
      child: Padding(
        padding: EdgeInsets.only(left: delimitPadding),
        child: Container(
            width: innerDelimitWidth,
            height: itemExtent,
            margin: EdgeInsets.zero,
            decoration: BoxDecoration(
              color: selectItemBgColor,
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(4).r,
                  topLeft: Radius.circular(4).r),
            )),
      ),
    );
    final rightDelimiterChild = Align(
      alignment: Alignment.center,
      child: Padding(
        padding: EdgeInsets.only(right: delimitPadding),
        child: Container(
            width: innerDelimitWidth,
            height: itemExtent,
            margin: EdgeInsets.zero,
            decoration: BoxDecoration(
              color: selectItemBgColor,
              borderRadius: BorderRadius.only(
                  bottomRight: Radius.circular(4).r,
                  topRight: Radius.circular(4).r),
            )),
      ),
    );
    final softVar = context
            .read<MqttBloc>()
            .deviceList
            .firstWhere((element) => element.productKey == 'HB-EMS')
            .softVer ??
        "";
    final factoryStr = context
            .read<MonitorFetchBloc>()
            .state
            .monitorModel
            ?.systemVO
            ?.factoryModel ??
        "";
    DateTime? leftValue;
    final formatTime = DateFormat('HH:mm');

    // Handle default values
    String? effectiveStartTime = widget.startTime;
    String? effectiveEndTime = widget.endTime;

    // If start time is null, use current time as default
    if (widget.startTime == null) {
      final now = DateTime.now();
      // Round to nearest interval if minuteInterval is specified
      int startMinute = now.minute;
      if (widget.minuteInterval != null && widget.minuteInterval! > 1) {
        startMinute =
            (now.minute ~/ widget.minuteInterval!) * widget.minuteInterval!;
      }
      effectiveStartTime =
          "${now.hour.toString().padLeft(2, '0')}:${startMinute.toString().padLeft(2, '0')}";
    }

    // Set default end time if start time is provided but end time is not
    if (effectiveStartTime != null && widget.endTime == null) {
      // Parse the start time
      final startTime = formatTime.parse(effectiveStartTime);

      // Set default end time to start time + 1 hour, max 24:00
      int endHour = startTime.hour + 1;
      int endMinute = startTime.minute;

      if (endHour >= 24) {
        effectiveEndTime = "24:00";
      } else {
        effectiveEndTime =
            "${endHour.toString().padLeft(2, '0')}:${endMinute.toString().padLeft(2, '0')}";
      }
    }

    if (effectiveStartTime != null) {
      try {
        final parsedTime = formatTime.parse(effectiveStartTime);
        final now = DateTime.now();
        leftValue = DateTime(
            now.year, now.month, now.day, parsedTime.hour, parsedTime.minute);
      } catch (e) {
        // If parsing fails, fall back to current time
        final now = DateTime.now();
        leftValue =
            DateTime(now.year, now.month, now.day, now.hour, now.minute);
      }
    }

    // Create the DateTimePickerAdapter with proper initialization
    var leftAdapter = DateTimePickerAdapter(
      type: PickerDateTimeType.kHM,
      value: leftValue, // Use the calculated leftValue directly
      minuteInterval: widget.minuteInterval,
    );

    leftPicker = Picker(
      height: widget.height ?? 150.0.h,
      adapter: leftAdapter,
      title: _buildLeftHeader(),
      cancelText: '',
      confirmText: '',
      textAlign: TextAlign.center,
      textStyle: TextStyle(color: ColorsUtil.hintColor, fontSize: 17),
      selectedTextStyle: TextStyle(color: selectTextColor, fontSize: 20),
      selectionOverlay: Container(height: 50, color: selectItemBgColor),
      backgroundColor: ColorsUtil.backgroundColor,
      headerDecoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.transparent, width: 0)),
      ),
      itemExtent: itemExtent,
      onSelect: (Picker picker, int index, List<int> selected) {},
      delimiter: [
        PickerDelimiter(column: 0, child: leftLeftDelimiterChild),
        PickerDelimiter(
            column: 2,
            child: Align(
              alignment: Alignment.center,
              child: Container(
                width: 15,
                height: itemExtent,
                color: selectItemBgColor,
                alignment: Alignment.center,
                child: Text(':',
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: selectTextColor)),
              ),
            )),
        PickerDelimiter(column: 4, child: delimiterChild),
      ],
    );

    DateTime? rightValue;
    if (effectiveEndTime != null) {
      rightValue = formatTime.parse(effectiveEndTime);
    }
    if (isOtherLoadSupport2400(factoryStr, softVar)) {
      var adapter = Time24PickerNoZeroAdapter(
        value: null, // Initialize with null value first
        minuteInterval: widget.minuteInterval ??
            15, // Force use 15-minute intervals with no 00:00
      );

      // Special handling for the endTime string (especially for "24:00")
      if (effectiveEndTime != null) {
        adapter.updateTimeString(effectiveEndTime);
      } else if (rightValue != null) {
        adapter.value = rightValue;
      }

      rightPicker = Picker(
        height: widget.height ?? 150.0.h,
        adapter: adapter,
        title: _buildRightHeader(),
        cancelText: '',
        confirmText: '',
        textAlign: TextAlign.center,
        textStyle: TextStyle(color: ColorsUtil.hintColor, fontSize: 17),
        selectedTextStyle: TextStyle(color: selectTextColor, fontSize: 20),
        selectionOverlay: Container(height: 50, color: selectItemBgColor),
        backgroundColor: ColorsUtil.backgroundColor,
        headerDecoration: const BoxDecoration(
          border:
              Border(bottom: BorderSide(color: Colors.transparent, width: 0)),
        ),
        itemExtent: itemExtent,
        delimiter: [
          PickerDelimiter(column: 0, child: delimiterChild),
          PickerDelimiter(
              column: 2,
              child: Align(
                alignment: Alignment.center,
                child: Container(
                  width: 15,
                  height: itemExtent,
                  color: selectItemBgColor,
                  alignment: Alignment.center,
                  child: Text(':',
                      style: TextStyle(
                          fontWeight: FontWeight.bold, color: selectTextColor)),
                ),
              )),
          PickerDelimiter(column: 4, child: rightDelimiterChild),
        ],
      );
    } else {
      // Create and configure adapter for TimePickerNOZeroAdapter
      var adapter = TimePickerNOZeroAdapter(
        value: null, // Initialize with null value first
        minuteInterval: widget.minuteInterval,
      );

      // Special handling for the endTime string
      if (effectiveEndTime != null) {
        adapter.updateTimeString(effectiveEndTime);
      } else if (rightValue != null) {
        adapter.value = rightValue;
      }

      rightPicker = Picker(
        height: widget.height ?? 150.0.h,
        adapter: adapter,
        title: _buildRightHeader(),
        cancelText: '',
        confirmText: '',
        textAlign: TextAlign.center,
        textStyle: TextStyle(color: ColorsUtil.hintColor, fontSize: 17),
        selectedTextStyle: TextStyle(color: selectTextColor, fontSize: 20),
        selectionOverlay: Container(height: 50, color: selectItemBgColor),
        backgroundColor: ColorsUtil.backgroundColor,
        headerDecoration: const BoxDecoration(
          border:
              Border(bottom: BorderSide(color: Colors.transparent, width: 0)),
        ),
        itemExtent: itemExtent,
        delimiter: [
          PickerDelimiter(column: 0, child: delimiterChild),
          PickerDelimiter(
              column: 2,
              child: Align(
                alignment: Alignment.center,
                child: Container(
                  width: 15,
                  height: itemExtent,
                  color: selectItemBgColor,
                  alignment: Alignment.center,
                  child: Text(':',
                      style: TextStyle(
                          fontWeight: FontWeight.bold, color: selectTextColor)),
                ),
              )),
          PickerDelimiter(column: 4, child: rightDelimiterChild),
        ],
      );
    }

    return Material(
      color: headerColor,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(8),
        topRight: Radius.circular(8),
      ),
      child: SafeArea(
        bottom: true,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 50.w,
              padding: EdgeInsets.only(left: 20.w, right: 20.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    widget.title ?? '',
                    style: TextStyle(
                      color: ColorsUtil.textColor,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  InkResponse(
                    onTap: () => Navigator.pop(context),
                    child: CustomImageAsset('icon_search_clean', width: 22),
                  )
                ],
              ),
            ),
            Container(
              height: 1,
              color: ColorsUtil.dividerColor,
            ),
            _buildSelector(),
            _buildContentWidget(),
            Container(
              margin: EdgeInsets.only(bottom: 10.h),
              child: Row(
                children: [
                  SizedBox(
                    width: 20.w,
                  ),
                  Expanded(
                      flex: 1,
                      child: CusInkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 36.h,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(22.h),
                              color: isWhite
                                  ? ColorsUtil.backgroundColor
                                  : ColorsUtil.percent10ButtonColor,
                              border: Border.all(
                                  color: isWhite
                                      ? ColorsUtil.backgroundColor
                                      : ColorsUtil.buttonColor,
                                  width: 1)),
                          child: Text(
                            S.current.text('common.cancel'),
                            style: TextStyle(
                                fontSize: 13.sp,
                                color: isWhite
                                    ? ColorsUtil.textColor
                                    : ColorsUtil.buttonColor),
                          ),
                        ),
                      )),
                  SizedBox(width: 10.w),
                  Expanded(
                      flex: 1,
                      child: CusInkWell(
                        onTap: () => {_onConfirm()},
                        child: Container(
                          alignment: Alignment.center,
                          height: 36.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(22.h),
                            color: ColorsUtil.buttonColor,
                          ),
                          child: Text(
                            S.current.text('common.ok'),
                            style: TextStyle(
                                fontSize: 13.sp,
                                color: ColorsUtil.buttonTextColor),
                          ),
                        ),
                      )),
                  SizedBox(width: 20.w),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildRightHeader() {
    return Container(
      height: 36.w,
      alignment: Alignment.center,
      child: Text($t('component.End_Time'),
          style: TextStyle(color: ColorsUtil.assistTextColor, fontSize: 14.sp)),
    );
  }

  Widget _buildLeftHeader() {
    return Container(
      alignment: Alignment.center,
      height: 36.w,
      child: Text($t('component.Start_Time'),
          style: TextStyle(color: ColorsUtil.assistTextColor, fontSize: 14.sp)),
    );
  }

  _onConfirm() {
    final softVar = context
            .read<MqttBloc>()
            .deviceList
            .firstWhere((element) => element.productKey == 'HB-EMS')
            .softVer ??
        "";
    final factoryStr = context
            .read<MonitorFetchBloc>()
            .state
            .monitorModel
            ?.systemVO
            ?.factoryModel ??
        "";
    final rightTime = isOtherLoadSupport2400(factoryStr, softVar)
        ? (rightPicker.adapter as Time24PickerNoZeroAdapter).value!
        : (rightPicker.adapter as TimePickerNOZeroAdapter).value!;
    final leftDate = (leftPicker.adapter as DateTimePickerAdapter).value!;

    final leftH = leftDate.hour.toString().padLeft(2, '0');
    final leftM = leftDate.minute.toString().padLeft(2, '0');
    final rightH = rightTime.hour.toString().padLeft(2, '0');
    final rightM = rightTime.minute.toString().padLeft(2, '0');

    String leftValue = '$leftH:$leftM';
    String rightValue = '$rightH:$rightM';
    if (DateTime.utc(1970).day + 1 == rightTime.day &&
        rightTime.hour == 0 &&
        rightTime.minute == 0) rightValue = '24:00';
    widget.clickCallBack?.call(
      leftValue,
      rightValue,
      _selectType,
    );
    Navigator.pop(context);
  }

  Widget _buildContentWidget() {
    return SizedBox(
      width: 1.sw,
      child: Row(children: [
        Expanded(child: leftPicker.makePicker()),
        Expanded(child: rightPicker.makePicker())
      ]),
    );
  }

  _buildSelector() {
    item(TimeShareType type) {
      return InkResponse(
        onTap: () {
          _selectType = type;
          setState(() {});
        },
        child: Container(
          height: 45.w,
          padding: EdgeInsets.only(left: 20.w, right: 20.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                $t(type.label),
                style: TextStyle(color: ColorsUtil.textColor, fontSize: 13.sp),
              ),
              if (_selectType == type)
                CustomImageAsset('icon_list_checked', width: 20.w, height: 20.w)
            ],
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: ColorsUtil.backgroundColor,
        borderRadius: BorderRadius.all(Radius.circular(8.w)),
      ),
      margin: EdgeInsets.all(20.w),
      child: Column(
        children: [
          item(TimeShareType.charge),
          Divider(
            color: ColorsUtil.aboutCardLineColor,
            height: 1.w,
            indent: 20.w,
          ),
          item(TimeShareType.discharge),
        ],
      ),
    );
  }
}
