import 'package:flutter_basic/components/bottom_button.dart';
import 'package:flutter_basic/components/custom_card_list.dart';
import 'package:flutter_basic/components/custom_checkbox.dart';
import 'package:flutter_basic/components/custom_image_asset.dart';
import 'package:flutter_basic/components/custom_switch.dart';
import 'package:flutter_basic/components/picker/picker.dart';
import 'package:flutter_basic/components/picker/time_picker_no_zero.dart';
import 'package:flutter_basic/generated/l10n.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TimeSlotWeekPiker {
  static bool isShowPicker = false;

  static void showPicker(
    BuildContext context, {
    required int id,
    required List<int> checkWeekList,
    required bool opera,
    required int hour,
    required int minute,
    required Function(TimerSlotBackModel) weekCallBack,
    bool Function(TimerSlotBackModel)? verifyCallBack,
  }) {
    showModalBottomSheet(
      isScrollControlled: true,
      useSafeArea: true,
      context: context,
      // 设置圆角
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      // 抗锯齿
      clipBehavior: Clip.antiAlias,
      builder: (BuildContext context) {
        return StatefulBuilder(
            builder: (BuildContext context, StateSetter state) {
          return TimeSlotWeekPickerView(
            id: id,
            checkWeekList: checkWeekList,
            opera: opera,
            hour: hour,
            minute: minute,
            verifyCallBack: verifyCallBack,
          );
        });
      },
    ).then((value) {
      isShowPicker = false;
      if (value != null) {
        logger.d('TimeSlotWeekPiker：$value');
        weekCallBack.call(value);
      }
    });
  }
}

class TimerSlotBackModel {
  final String week;
  final bool opera;
  final bool enable;
  final int hour;
  final int minute;
  final int id;

  TimerSlotBackModel({
    required this.week,
    required this.opera,
    required this.hour,
    required this.minute,
    required this.id,
    required this.enable,
  });
}

class WeekItemData {
  final String weekName;
  final int index;

  WeekItemData({required this.weekName, required this.index});
}

class TimeSlotWeekPickerView extends StatefulWidget {
  final double? height;
  final List<int> checkWeekList;
  final bool opera;
  final int hour;
  final int minute;
  final int id;
  final bool Function(TimerSlotBackModel)? verifyCallBack;

  const TimeSlotWeekPickerView({
    super.key,
    this.height,
    required this.checkWeekList,
    required this.opera,
    required this.hour,
    required this.minute,
    required this.id,
    this.verifyCallBack,
  });

  @override
  State<StatefulWidget> createState() => _TimeSlotWeekPickerViewState();
}

class _TimeSlotWeekPickerViewState extends State<TimeSlotWeekPickerView> {
  Widget? timePicker;

  bool switchStatus = false;
  int hour = 0;
  int minute = 0;

  ///选中的星期
  List<int> selectedWeek = [];

  List<WeekItemData> weekItemList = [];

  @override
  void initState() {
    super.initState();
    selectedWeek = widget.checkWeekList;
    switchStatus = widget.opera;
    hour = widget.hour;
    minute = widget.minute;
    weekItemList.add(
        WeekItemData(weekName: S.current.text('component.Sunday'), index: 7));
    weekItemList.add(
        WeekItemData(weekName: S.current.text('component.Monday'), index: 1));
    weekItemList.add(
        WeekItemData(weekName: S.current.text('component.Tuesday'), index: 2));
    weekItemList.add(WeekItemData(
        weekName: S.current.text('component.Wednesday'), index: 3));
    weekItemList.add(
        WeekItemData(weekName: S.current.text('component.Thursday'), index: 4));
    weekItemList.add(
        WeekItemData(weekName: S.current.text('component.Friday'), index: 5));
    weekItemList.add(
        WeekItemData(weekName: S.current.text('component.Saturday'), index: 6));
  }

  @override
  Widget build(BuildContext context) {
    if (timePicker == null) _buildPickerView();

    var maxHeight =
        MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top;

    var switchWidget = StatefulBuilder(
        builder: (BuildContext builder, StateSetter switchState) {
      return Row(children: [
        Text(
          switchStatus
              ? $t('device.plug_timer_on')
              : $t('device.plug_timer_off'),
          style: TextStyle(fontSize: 13.sp, color: ColorsUtil.hintColor),
        ),
        SizedBox(width: 8.w),
        CustomSwitch(
          onToggle: (value) => switchState(() => switchStatus = value),
          value: switchStatus,
        ),
        SizedBox(width: 15.w)
      ]);
    });

    return Material(
      child: SafeArea(
        bottom: true,
        child: Container(
            constraints: BoxConstraints(maxHeight: maxHeight),
            color: Colors.white,
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                    child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.only(left: 22.w, right: 22.w),
                    child: Column(children: [
                      Container(
                        color: ColorsUtil.transparentColor,
                        child: timePicker,
                      ),
                      SizedBox(height: 20.w),
                      CustomCardList(
                        color: ColorsUtil.backgroundColor,
                        cardList: [
                          CardItemData(
                            title: S.current.text('component.action'),
                            showIcon: false,
                            child: switchWidget,
                          )
                        ],
                      ),
                      _buildWeekView(),
                    ]),
                  ),
                )),
                _buildBottomButton()
              ],
            )),
      ),
    );
  }

  _buildPickerView() {
    var selectTextColor = ColorsUtil.textColor;
    var selectItemBgColor = ColorsUtil.percent5TextColor;
    var bgColor = Colors.white;
    var itemExtent = 36.h;
    final delimiterChild = Align(
      alignment: Alignment.center,
      child:
          Container(width: 126.w, height: itemExtent, color: selectItemBgColor),
    );

    var tempDataTime = DateTime(2024, 1, 1, hour, minute);
    final mPicker = Picker(
      height: widget.height ?? 180.0.h,
      adapter: TimePickerNOZeroAdapter(value: tempDataTime, minuteInterval: 15),
      cancelText: '',
      confirmText: '',
      containerColor: bgColor,
      backgroundColor: bgColor,
      textAlign: TextAlign.center,
      textStyle: TextStyle(color: ColorsUtil.hintColor, fontSize: 14.sp),
      selectedTextStyle: TextStyle(color: selectTextColor, fontSize: 14.sp),
      selectionOverlay: Container(height: 50.h, color: selectItemBgColor),
      headerDecoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.transparent, width: 0)),
      ),
      itemExtent: itemExtent,
      onSelect: (Picker picker, int index, List<int> selected) {
        logger.d(selected);
        hour = selected[0];
        minute = selected[1];
      },
      delimiter: [
        PickerDelimiter(column: 0, child: delimiterChild),
        PickerDelimiter(
            column: 2,
            child: Align(
              alignment: Alignment.center,
              child: Container(
                width: 15.w,
                height: itemExtent,
                color: selectItemBgColor,
                alignment: Alignment.center,
                child: Text(':',
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: selectTextColor)),
              ),
            )),
        PickerDelimiter(column: 4, child: delimiterChild),
      ],
    );

    timePicker = mPicker.makePicker();
  }

  _buildWeekView() {
    selectWeek(int index) {
      logger.i('点击了 $index');
      if (selectedWeek.contains(index)) {
        selectedWeek.remove(index);
      } else {
        selectedWeek.add(index);
      }
      setState(() {});
    }

    var list = weekItemList
        .map((e) => CardItemData(
            title: e.weekName,
            showIcon: false,
            onTap: () => selectWeek(e.index),
            child: Padding(
              padding: EdgeInsets.only(right: 15.w),
              child: CheckBoxWidget(
                initChecked: selectedWeek.contains(e.index),
                onChanged: (value) => selectWeek(e.index),
              ),
            )))
        .toList();
    return Container(
      padding: EdgeInsets.only(top: 20.h),
      child: CustomCardList(
        color: ColorsUtil.backgroundColor,
        sectionItem: Padding(
          padding: EdgeInsets.only(bottom: 6.h),
          child: Row(children: [
            Expanded(
                child: Text(
              S.current.text('component.set_repeat'),
              style: TextStyle(fontSize: 14.sp, color: ColorsUtil.textColor),
            )),
            InkWell(
              onTap: () {
                if (selectedWeek.length == 7) {
                  selectedWeek.clear();
                } else {
                  selectedWeek = [1, 2, 3, 4, 5, 6, 7];
                }
                setState(() {});
              },
              child: Text(
                selectedWeek.length == 7
                    ? $t('component.cancel_set_everyday')
                    : $t('component.set_everyday'),
                style: TextStyle(
                    fontSize: 14.sp, color: ColorsUtil.highlightTextColor),
              ),
            )
          ]),
        ),
        cardList: list,
      ),
    );
  }

  _buildBottomButton() {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h, top: 20.h),
      child: Row(
        children: [
          Expanded(
              flex: 1,
              child: BottomButtonWidget(
                padding: EdgeInsets.only(left: 20.w, right: 5.w),
                buttonType: BottomButtonType.cancel,
                text: S.current.text('common.cancel'),
                buttonHeight: 36.w,
                onPressed: () {
                  Navigator.of(context).pop();
                },
              )),
          Expanded(
            flex: 1,
            child: BottomButtonWidget(
                padding: EdgeInsets.only(left: 5.w, right: 20.w),
                text: S.current.text('common.ok'),
                buttonHeight: 36.w,
                enabled: selectedWeek.isNotEmpty,
                onPressed: () {
                  var model = TimerSlotBackModel(
                    id: widget.id,
                    week: selectedWeek.join(','),
                    hour: hour,
                    minute: minute,
                    opera: switchStatus,
                    enable: true,
                  );
                  if (widget.verifyCallBack?.call(model) ?? true) {
                    Navigator.of(context).pop(model);
                  }
                }),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: BoxDecoration(
          border: Border(
              top: BorderSide(width: 0, color: ColorsUtil.dividerColor),
              bottom: BorderSide(width: 1, color: ColorsUtil.dividerColor),
              left: BorderSide(width: 0, color: ColorsUtil.dividerColor),
              right: BorderSide(width: 0, color: ColorsUtil.dividerColor))),
      height: 50.h,
      child: Padding(
        padding: EdgeInsets.only(left: 20.w, right: 20.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(S.current.text('component.Choose_Time_Slot'),
                style: TextStyle(color: ColorsUtil.textColor, fontSize: 13.sp)),
            InkResponse(
              onTap: () => {Navigator.pop(context)},
              child: CustomImageAsset(
                'icon_close',
                width: 22,
                height: 22,
              ),
            )
          ],
        ),
      ),
    );
  }
}
