import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/components/picker/flutter_picker.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../features/monitor/bloc/monitor_fetch_bloc.dart';
import '../../features/monitor/bloc/mqtt/mqtt_bloc.dart';
import '../../platform/utils/version.dart';
import '../cus_InkWell/CusInkWell.dart';
import 'picker_time_24_no_zero.dart';
import 'time_picker_no_zero.dart';

typedef _ClickCallBack = void Function(
    dynamic leftSelectValue, dynamic rightSelectValue);

enum PickerType {
  string,
  array,
}

class TimeSlotRangePicker {
  static bool _isShowPicker = false;

  /// 单列
  /// 单列选择器返回选中行对象和index
  static void showStringPicker<T>(
    BuildContext context, {
    required List data,
    String? title,
    String? labelKey, // 对象数组的文字字段
    int selectIndex = 0,
    _ClickCallBack? clickCallBack,
    double? height,
    int minuteInterval = 1,
    String? startTime,
    String? endTime,
  }) {
    if (_isShowPicker || data.isEmpty) {
      return;
    }
    _isShowPicker = true;

    showModalBottomSheet(
      enableDrag: false,
      isScrollControlled: false,
      useSafeArea: true,
      context: context,
      elevation: 0,
      // 设置圆角
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      // 抗锯齿
      clipBehavior: Clip.antiAliasWithSaveLayer,
      builder: (BuildContext context) {
        return TimeSlotPickerView(
          startTime: startTime,
          endTime: endTime,
          height: height,
          data: data,
          title: title,
          selecteds: [selectIndex],
          pickerType: PickerType.string,
          minuteInterval: minuteInterval,
          adapter: labelKey != null
              ? PickerDataAdapter(
                  pickerData: data.map((e) => e[labelKey]).toList())
              : PickerDataAdapter(pickerData: data),
          clickCallBack: clickCallBack,
        );
      },
    ).then((value) => _isShowPicker = false);
  }
}

/// 自定义picker
class TimeSlotPickerView extends StatefulWidget {
  const TimeSlotPickerView({
    Key? key,
    this.data,
    this.title,
    this.selecteds,
    this.pickerType,
    required this.adapter,
    this.clickCallBack,
    this.height,
    this.startTime,
    this.endTime,
    this.minuteInterval,
  }) : super(key: key);

  final List? data;
  final String? title;
  final List<int>? selecteds;
  final PickerType? pickerType;
  final PickerAdapter adapter;
  final _ClickCallBack? clickCallBack;
  final double? height;
  final String? startTime;
  final String? endTime;
  final int? minuteInterval;

  @override
  State<TimeSlotPickerView> createState() => TimeSlotPickerViewState();
}

class TimeSlotPickerViewState extends State<TimeSlotPickerView> {
  late Picker leftPicker;
  late Picker rightPicker;
  bool clickedSecondary = false;

  @override
  void initState() {
    super.initState();
    clickedSecondary = false;
  }

  @override
  Widget build(BuildContext context) {
    return _body();
  }

  _body() {
    var headerColor = isWhite ? Colors.white : ColorsUtil.backgroundColor;
    var selectTextColor = ColorsUtil.textColor;
    var selectItemBgColor = Colors.grey.withOpacity(0.15);
    var itemExtent = 36.h;
    var delimitWidth = 50.w;
    var innerDelimitWidth = 30.w;
    var delimitPadding = 20.w;
    final delimiterChild = Align(
      alignment: Alignment.center,
      child: Container(
          width: delimitWidth, height: itemExtent, color: selectItemBgColor),
    );
    final leftLeftDelimiterChild = Align(
      alignment: Alignment.center,
      child: Padding(
        padding: EdgeInsets.only(left: delimitPadding),
        child: Container(
            width: innerDelimitWidth,
            height: itemExtent,
            margin: EdgeInsets.zero,
            decoration: BoxDecoration(
              color: selectItemBgColor,
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(4).r,
                  topLeft: Radius.circular(4).r),
            )),
      ),
    );
    final rightDelimiterChild = Align(
      alignment: Alignment.center,
      child: Padding(
        padding: EdgeInsets.only(right: delimitPadding),
        child: Container(
            width: innerDelimitWidth,
            height: itemExtent,
            margin: EdgeInsets.zero,
            decoration: BoxDecoration(
              color: selectItemBgColor,
              borderRadius: BorderRadius.only(
                  bottomRight: Radius.circular(4).r,
                  topRight: Radius.circular(4).r),
            )),
      ),
    );
    DateTime? leftValue;
    final formatTime = DateFormat('HH:mm');

    // Handle default values
    String? effectiveStartTime = widget.startTime;
    String? effectiveEndTime = widget.endTime;

    // If start time is null, use current time as default
    if (widget.startTime == null) {
      final now = DateTime.now();
      // Round to nearest interval if minuteInterval is specified
      int startMinute = now.minute;
      if (widget.minuteInterval != null && widget.minuteInterval! > 1) {
        startMinute =
            (now.minute ~/ widget.minuteInterval!) * widget.minuteInterval!;
      }
      effectiveStartTime =
          "${now.hour.toString().padLeft(2, '0')}:${startMinute.toString().padLeft(2, '0')}";

      // Also set leftValue directly to ensure proper initialization
      leftValue = DateTime(now.year, now.month, now.day, now.hour, startMinute);
    } else {
      // If start time is provided, parse it immediately
      if (effectiveStartTime != null) {
        try {
          final parsedTime = formatTime.parse(effectiveStartTime);
          final now = DateTime.now();
          leftValue = DateTime(
              now.year, now.month, now.day, parsedTime.hour, parsedTime.minute);
        } catch (e) {
          // If parsing fails, fall back to current time
          final now = DateTime.now();
          leftValue =
              DateTime(now.year, now.month, now.day, now.hour, now.minute);
        }
      }
    }

    // Set default end time if start time is provided but end time is not
    if (effectiveStartTime != null && widget.endTime == null) {
      // Parse the start time
      final startTime = formatTime.parse(effectiveStartTime);

      // Set default end time to start time + 1 hour, max 24:00
      int endHour = startTime.hour + 1;
      int endMinute = startTime.minute;

      if (endHour >= 24) {
        effectiveEndTime = "24:00";
      } else {
        effectiveEndTime =
            "${endHour.toString().padLeft(2, '0')}:${endMinute.toString().padLeft(2, '0')}";
      }
    }

    // Get version info for configuration
    final softVar = context
            .read<MqttBloc>()
            .deviceList
            .firstWhere((element) => element.productKey == 'HB-EMS')
            .softVer ??
        "";
    final factoryStr = context
            .read<MonitorFetchBloc>()
            .state
            .monitorModel
            ?.systemVO
            ?.factoryModel ??
        "";

    // Create the DateTimePickerAdapter with proper initialization
    var leftAdapter = DateTimePickerAdapter(
      type: PickerDateTimeType.kHM,
      value: leftValue, // Directly use the calculated value
      minuteInterval: widget.minuteInterval,
    );

    leftPicker = Picker(
      height: widget.height ?? 150.0.h,
      adapter: leftAdapter,
      title: Text(S.current.text('component.Start_Time'),
          style: TextStyle(color: ColorsUtil.assistTextColor, fontSize: 14.sp)),
      cancelText: '',
      confirmText: '',
      textAlign: TextAlign.center,
      textStyle: TextStyle(color: ColorsUtil.hintColor, fontSize: 17),
      selectedTextStyle: TextStyle(color: selectTextColor, fontSize: 20),
      selectionOverlay: Container(height: 50, color: selectItemBgColor),
      backgroundColor: isWhite ? Colors.white : ColorsUtil.backgroundColor,
      headerDecoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.transparent, width: 0)),
      ),
      itemExtent: itemExtent,
      onSelect: (Picker picker, int index, List<int> selected) {},
      delimiter: [
        PickerDelimiter(column: 0, child: leftLeftDelimiterChild),
        PickerDelimiter(
            column: 2,
            child: Align(
              alignment: Alignment.center,
              child: Container(
                width: 15,
                height: itemExtent,
                color: selectItemBgColor,
                alignment: Alignment.center,
                child: Text(':',
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: selectTextColor)),
              ),
            )),
        PickerDelimiter(column: 4, child: delimiterChild),
      ],
    );
    DateTime? rightValue;
    if (effectiveEndTime != null) {
      rightValue = formatTime.parse(effectiveEndTime);
    }

    if (isOtherLoadSupport2400(factoryStr, softVar)) {
      var adapter = Time24PickerNoZeroAdapter(
        value: rightValue, // Directly use the calculated value
        minuteInterval: 15, // Force use 15-minute intervals with no 00:00
      );

      // Special handling for the endTime string (especially for "24:00")
      if (effectiveEndTime != null) {
        adapter.updateTimeString(effectiveEndTime);
      }

      rightPicker = Picker(
        height: widget.height ?? 150.0.h,
        adapter: adapter,
        title: Text(
          S.current.text('component.End_Time'),
          style: TextStyle(color: ColorsUtil.assistTextColor, fontSize: 14.sp),
        ),
        cancelText: '',
        confirmText: '',
        textAlign: TextAlign.center,
        textStyle: TextStyle(color: ColorsUtil.hintColor, fontSize: 17),
        selectedTextStyle: TextStyle(color: selectTextColor, fontSize: 20),
        selectionOverlay: Container(height: 50, color: selectItemBgColor),
        backgroundColor: ColorsUtil.backgroundColor,
        headerDecoration: const BoxDecoration(
          border:
              Border(bottom: BorderSide(color: Colors.transparent, width: 0)),
        ),
        itemExtent: itemExtent,
        delimiter: [
          PickerDelimiter(column: 0, child: delimiterChild),
          PickerDelimiter(
              column: 2,
              child: Align(
                alignment: Alignment.center,
                child: Container(
                  width: 15,
                  height: itemExtent,
                  color: selectItemBgColor,
                  alignment: Alignment.center,
                  child: Text(':',
                      style: TextStyle(
                          fontWeight: FontWeight.bold, color: selectTextColor)),
                ),
              )),
          PickerDelimiter(column: 4, child: rightDelimiterChild),
        ],
      );
    } else {
      // Create and configure adapter for TimePickerNOZeroAdapter
      var adapter = TimePickerNOZeroAdapter(
        value: rightValue, // Directly use the calculated value
        minuteInterval: 15,
      );

      // Special handling for the endTime string
      if (effectiveEndTime != null) {
        adapter.updateTimeString(effectiveEndTime);
      }

      rightPicker = Picker(
        height: widget.height ?? 150.0.h,
        adapter: adapter,
        title: Text(
          S.current.text('component.End_Time'),
          style: TextStyle(color: ColorsUtil.assistTextColor, fontSize: 14.sp),
        ),
        cancelText: '',
        confirmText: '',
        textAlign: TextAlign.center,
        textStyle: TextStyle(color: ColorsUtil.hintColor, fontSize: 17),
        selectedTextStyle: TextStyle(color: selectTextColor, fontSize: 20),
        selectionOverlay: Container(height: 50, color: selectItemBgColor),
        backgroundColor: ColorsUtil.backgroundColor,
        headerDecoration: const BoxDecoration(
          border:
              Border(bottom: BorderSide(color: Colors.transparent, width: 0)),
        ),
        itemExtent: itemExtent,
        delimiter: [
          PickerDelimiter(column: 0, child: delimiterChild),
          PickerDelimiter(
              column: 2,
              child: Align(
                alignment: Alignment.center,
                child: Container(
                  width: 15,
                  height: itemExtent,
                  color: selectItemBgColor,
                  alignment: Alignment.center,
                  child: Text(':',
                      style: TextStyle(
                          fontWeight: FontWeight.bold, color: selectTextColor)),
                ),
              )),
          PickerDelimiter(column: 4, child: rightDelimiterChild),
        ],
      );
    }
    // final bgColor = Colors.greenAccent.shade700;
    // final txtColor = Colors.white;
    // final txtStyle = TextStyle(color: txtColor);
    // final selectColor = Colors.black.withOpacity(0.20);
    // leftPicker = Picker(
    //     itemExtent: itemExtent,
    //     backgroundColor: Colors.transparent,
    //     containerColor: bgColor,
    //     selectionOverlay: Container(height: itemExtent, color: selectColor),
    //     headerDecoration: BoxDecoration(color: Colors.black.withOpacity(0.05)),
    //     textStyle: txtStyle,
    //     cancelTextStyle: txtStyle,
    //     confirmTextStyle: txtStyle,
    //     selectedTextStyle: TextStyle(color: txtColor, fontSize: 20),
    //     adapter: DateTimePickerAdapter(type: PickerDateTimeType.kHM),
    //     delimiter: [
    //       PickerDelimiter(column: 0, child: delimiterChild),
    //       PickerDelimiter(
    //           column: 2,
    //           child: Align(
    //             alignment: Alignment.center,
    //             child: Container(
    //               width: 15,
    //               height: itemExtent,
    //               color: selectColor,
    //               alignment: Alignment.center,
    //               child: Text(':',
    //                   style: TextStyle(
    //                       fontWeight: FontWeight.bold, color: txtColor)),
    //             ),
    //           )),
    //       PickerDelimiter(column: 4, child: delimiterChild),
    //     ],
    //     title: Padding(
    //       padding: const EdgeInsets.fromLTRB(0, 12, 0, 12),
    //       child: Text("Select Time", style: txtStyle),
    //     ),
    //     onConfirm: (Picker picker, List value) {
    //       print(picker.adapter.text);
    //       print(value);
    //     });

    return Material(
      color: headerColor,
      child: SafeArea(
        bottom: true,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            SizedBox(height: 20.w),
            _buildContentWidget(),
            Container(
              margin: EdgeInsets.only(bottom: 10.w),
              child: Row(
                children: [
                  SizedBox(width: 20.w),
                  Expanded(
                      flex: 1,
                      child: CusInkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 36.h,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(22.h),
                              color: isWhite
                                  ? ColorsUtil.backgroundColor
                                  : ColorsUtil.percent10ButtonColor,
                              border: Border.all(
                                  color: isWhite
                                      ? ColorsUtil.backgroundColor
                                      : ColorsUtil.buttonColor,
                                  width: 1)),
                          child: Text(
                            S.current.text('component.cancel'),
                            style: TextStyle(
                                fontSize: 13.sp,
                                color: isWhite
                                    ? ColorsUtil.textColor
                                    : ColorsUtil.buttonColor),
                          ),
                        ),
                      )),
                  SizedBox(width: 10.w),
                  Expanded(
                      flex: 1,
                      child: CusInkWell(
                        onTap: () => {_onConfirm()},
                        child: Container(
                          alignment: Alignment.center,
                          height: 36.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(22.h),
                            color: ColorsUtil.buttonColor,
                          ),
                          child: Text(
                            S.current.text('common.ok'),
                            style: TextStyle(
                                fontSize: 13.sp,
                                color: ColorsUtil.buttonTextColor),
                          ),
                        ),
                      )),
                  SizedBox(
                    width: 20.w,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 1, color: ColorsUtil.dividerColor),
        ),
      ),
      height: 50.h,
      padding: EdgeInsets.only(left: 20.w, right: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            widget.title ?? '',
            style: TextStyle(
              color: ColorsUtil.textColor,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          InkResponse(
            onTap: () => Navigator.pop(context),
            child: CustomImageAsset('icon_close', width: 22),
          )
        ],
      ),
    );
  }

  _onConfirm() {
    // Get the left time from the appropriate adapter
    DateTime leftTime;
    if (leftPicker.adapter is TimePickerNOZeroAdapter) {
      leftTime = (leftPicker.adapter as TimePickerNOZeroAdapter).value!;
    } else {
      leftTime = (leftPicker.adapter as DateTimePickerAdapter).value!;
    }

    final softVar = context
            .read<MqttBloc>()
            .deviceList
            .firstWhere((element) => element.productKey == 'HB-EMS')
            .softVer ??
        "";
    final factoryStr = context
            .read<MonitorFetchBloc>()
            .state
            .monitorModel
            ?.systemVO
            ?.factoryModel ??
        "";

    final rightTime = isOtherLoadSupport2400(factoryStr, softVar)
        ? (rightPicker.adapter as Time24PickerNoZeroAdapter).value!
        : (rightPicker.adapter as TimePickerNOZeroAdapter).value!;
    logger.d('leftTime $leftTime , rightTime $rightTime');
    final leftH = leftTime.hour.toString().padLeft(2, '0');
    final leftM = leftTime.minute.toString().padLeft(2, '0');
    final rightH = rightTime.hour.toString().padLeft(2, '0');
    final rightM = rightTime.minute.toString().padLeft(2, '0');

    String leftValue = '$leftH:$leftM';
    String rightValue = '$rightH:$rightM';
    // rightTime.day == leftTime.day + 1 // 表示24:00
    if (DateTime.utc(1970).day + 1 == rightTime.day &&
        rightTime.hour == 0 &&
        rightTime.minute == 0) rightValue = '24:00';

    widget.clickCallBack?.call(leftValue, rightValue);
    Navigator.pop(context);
  }

  Widget _buildContentWidget() {
    return Container(
      width: 1.sw,
      child: Row(children: [
        Expanded(child: leftPicker.makePicker()),
        Expanded(child: rightPicker.makePicker())
      ]),
    );
  }
}
