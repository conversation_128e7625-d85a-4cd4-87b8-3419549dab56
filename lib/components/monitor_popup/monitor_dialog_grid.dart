import 'dart:async';

import 'package:flutter_basic/components/cus_InkWell/CusInkWell.dart';
import 'package:flutter_basic/features/monitor/bloc/monitor_fetch_bloc.dart';
import 'package:flutter_basic/features/monitor/bloc/monitor_fetch_event.dart';
import 'package:flutter_basic/features/monitor/bloc/monitor_fetch_state.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/community/local_mode_message_manager.dart';
import 'package:flutter_basic/platform/config/combine.dart';
import 'package:flutter_basic/platform/utils/combine.dart';
import 'package:flutter_basic/platform/utils/sp_utils.dart';
import 'package:flutter_basic/router/devices_feature_routes/devices_feature_routes.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../generated/l10n.dart';
import '../../platform/community/monitor/constants.dart';
import '../../platform/mqtt/mqtt_manager.dart';
import '../../platform/platform.dart';
import '../../platform/utils/color_utils.dart';
import '../../repositories/combine_repository/model/combine_common_model.dart';
import '../../repositories/monitor_repository/model/monitor_response_graph_model.dart';
import '../custom_image_asset.dart';
import '../dialog_header.dart';

class MonitorGridDialog extends StatefulWidget {
  final EnergyFlowChartVo? energyFlowChartVO;

  const MonitorGridDialog({super.key, required this.energyFlowChartVO});

  @override
  State<StatefulWidget> createState() {
    return _MonitorGridDialog();
  }
}

class _MonitorGridDialog extends State<MonitorGridDialog> {
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    LocalModeMessageManager.instance
        .setCurrentMessageType(LocalModelPackageType.package2);

    if (isCombine()) {
      _publishDataForDevices();
      _setupSlaverMode();
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    LocalModeMessageManager.instance
        .setCurrentMessageType(LocalModelPackageType.package1);
    super.dispose();
  }

  void _setupSlaverMode() {
    _timer =
        Timer.periodic(combineMqttDuration, (_) => _publishDataForDevices());
  }

  Future<void> _publishDataForDevices() async {
    for (var gridSnModel in _getDeviceSnList()) {
      MqttManager.instance.publishMessage(
        'data_get',
        gridSnModel.sn,
        {
          "dev_list": [
            {
              "dev_sn": 'ems_${gridSnModel.sn}',
              "meter_list": [
                diy_point_CT_status,
                diy_point_ct_power,
                diy_point_on_grid_power,
                diy_point_grid_connect_status
              ]
            }
          ]
        },
        '${MqttDevicesConfig.publishClusterTopic}${gridSnModel.sn}',
      );
    }
    await Future.delayed(combineApiDuration);
    final curSystemId = SpUtil.getString(currentSystemIdKey, defValue: '');
    context
        .read<MonitorFetchBloc>()
        .add(MonitorInfoFetched(curSystemId, needLoading: false));
  }

  List<MasteredSnModel> _getDeviceSnList() {
    final slaverList = context
        .read<MonitorFetchBloc>()
        .state
        .monitorModel
        ?.energyFlowChartVO
        ?.acInfo
        ?.slaverList;
    return [
      MasteredSnModel(
        sn: widget.energyFlowChartVO?.acInfo?.deviceNo
                ?.replaceAll('ems_', '') ??
            '',
        mastered: Mastered.host,
      ),
      ...?slaverList?.map((element) => MasteredSnModel(
            sn: element.deviceNo?.replaceAll('ems_', '') ?? '',
            mastered: Mastered.client,
          )),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MonitorFetchBloc, MonitorFetchState>(
      builder: (context, state) {
        EnergyFlowChartVo? energyFlowChartVO =
            state.monitorModel?.energyFlowChartVO;
        //
        String dialog_grid_tips =
            state.monitorModel?.energyFlowChartVO?.ctDevice != null
                ? $t('systemMonitor.dialog_grid_tips')
                : $t('systemMonitor.dialog_grid_tips_no_ct');
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DialogHeader(title: $t('systemMonitor.dialog_grid_title')),
              Container(
                width: double.infinity,
                padding: EdgeInsets.only(
                    top: 20.h, left: 20.w, right: 20.w, bottom: 20.w),
                child: Text(
                  dialog_grid_tips,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: ColorsUtil.contentColor,
                  ),
                ),
              ),
              Flexible(
                fit: FlexFit.loose,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildGridList(energyFlowChartVO),
                      if (energyFlowChartVO?.ctDevice != null)
                        _buildCtDevice(energyFlowChartVO!.ctDevice!),
                      SizedBox(height: 20.h),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildGridList(EnergyFlowChartVo? energyFlowChartVO) {
    final gridVo = energyFlowChartVO?.gridVo;
    final slaverList = gridVo?.slaverList;
    List<Widget> gridItems = [];
    if (isCombine() && gridVo != null) {
      gridItems.add(GridItemCard(
        isShowDeviceName: false,
        item: GridItemMode(
          languageKey:
              'systemMonitor.dialog_grid_item_name_combine_${gridVo.toGridDirection}',
          value: '${gridVo.powerDisplayValue()}W',
          direction: energyFlowChartVO?.toGridDirection ?? 0,
          deviceName: gridVo.deviceName ?? "",
        ),
      ));
      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: gridItems,
      );
    }
    if (gridVo != null) {
      gridItems.add(GridItemCard(
        isShowDeviceName: slaverList != null && slaverList.isNotEmpty,
        item: GridItemMode(
          languageKey:
              'systemMonitor.dialog_grid_item_name_${gridVo.toGridDirection}',
          value: '${gridVo.powerDisplayValue()}W',
          direction: energyFlowChartVO?.toGridDirection ?? 0,
          deviceName: gridVo.deviceName ?? "设备型号名称",
        ),
      ));
    }

    if (slaverList != null && slaverList.isNotEmpty) {
      gridItems.addAll(slaverList.map((e) => GridItemCard(
            isShowDeviceName: true,
            item: GridItemMode(
              languageKey:
                  'systemMonitor.dialog_grid_item_name_${e.toGridDirection}',
              value: '${e.powerDisplayValue()}W',
              direction: e.toGridDirection ?? 0,
              deviceName: e.deviceName ?? "设备型号名称",
            ),
          )));
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: gridItems,
    );
  }

  _buildCtDevice(EnergyFlowCTVO ct) {
    item(EnergyFlowCTVO ctDevice) => CusInkWell(
          onTap: () {
            Navigator.of(context).push(
              IntergratedRoutes.onGenerateRoute(
                RouteSettings(name: IntergratedRoutesType.ctDetail, arguments: {
                  'deviceNo': ctDevice.deviceNo,
                  'productKey': ctDevice.productKey
                }),
              ),
            );
          },
          child: Card(
            margin: EdgeInsets.fromLTRB(20.w, 0, 20.w, 20.w),
            color: ColorsUtil.dialogCardBackgroundColor,
            elevation: 0,
            shape: RoundedRectangleBorder(
              side: BorderSide(color: ColorsUtil.transparentColor),
              borderRadius: BorderRadius.all(Radius.circular(8.w)),
            ),
            shadowColor: ColorsUtil.transparentColor,
            child: Container(
              // height: 75.h,
              width: double.infinity,
              padding: EdgeInsets.all(12.w),
              child: Row(
                children: [
                  CircleAvatar(
                    backgroundColor: ColorsUtil.monitorTagBgColor,
                    radius: 18.w,
                    child: Center(
                        child: CustomImageAsset('monitor_ct', width: 24.w)),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Text(
                      ctDevice.ctDeviceName,
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: ColorsUtil.textColor,
                      ),
                    ),
                  ),
                  if (ctDevice.ctStatus == CTStatusType.offline)
                    Text(
                      S.current.text('device.Offline'),
                      style: TextStyle(
                          color: ColorsUtil.systemOfflineColor,
                          fontSize: 12.sp),
                    ),
                  CustomImageAsset('icon_arrow_right_gray', width: 20.w)
                ],
              ),
            ),
          ),
        );
    var list = [];
    if (ct.slaverList != null && ct.slaverList!.isNotEmpty) {
      list = ct.slaverList!.map((e) => item(e)).toList();
    }
    return Column(
      children: [item(ct), ...list],
    );
  }
}

class GridListSection extends StatelessWidget {
  final String title;
  final GridItemMode item;

  const GridListSection({super.key, required this.title, required this.item});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title,
            style: TextStyle(fontSize: 14.sp, color: ColorsUtil.hintColor)),
        SizedBox(height: 10.w),
        GridItemCard(item: item),
      ],
    );
  }
}

class GridItemCard extends StatelessWidget {
  final GridItemMode item;
  final bool isShowDeviceName;

  const GridItemCard(
      {super.key, required this.item, this.isShowDeviceName = false});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.fromLTRB(20.w, 0, 20.w, 20.w),
      color: ColorsUtil.dialogCardBackgroundColor,
      elevation: 0,
      shape: RoundedRectangleBorder(
        side: BorderSide(color: ColorsUtil.transparentColor),
        borderRadius: BorderRadius.all(
          Radius.circular(8.w),
        ),
      ),
      shadowColor: ColorsUtil.transparentColor,
      child: Container(
        // height: 75.h,
        width: double.infinity,
        padding: EdgeInsets.all(12.w),
        child: Row(
          children: [
            Container(
              width: 36.w,
              height: 36.w,
              decoration: BoxDecoration(
                color: ColorsUtil.monitorTagBgColor,
                borderRadius: BorderRadius.all(Radius.circular(18.w)),
              ),
              child: Center(
                child: SizedBox(
                  width: 24.w,
                  height: 24.w,
                  child: CustomImageAsset('new_diy_grid',
                      width: 24.w, height: 24.w),
                ),
              ),
            ),
            SizedBox(width: 10.w),
            Flexible(
              flex: 1,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.current.text(item.languageKey),
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: ColorsUtil.textColor,
                        ),
                      ),
                      if (isShowDeviceName)
                        Text(
                          item.deviceName,
                          style: TextStyle(
                              fontSize: 12.sp, color: ColorsUtil.hintColor),
                        )
                    ],
                  ),
                  Text(
                    item.value,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: ColorsUtil.contentColor,
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

class GridItemMode {
  String value;
  int direction;
  String deviceName;
  String languageKey;
  GridItemMode(
      {required this.value,
      required this.direction,
      required this.deviceName,
      required this.languageKey});
}
