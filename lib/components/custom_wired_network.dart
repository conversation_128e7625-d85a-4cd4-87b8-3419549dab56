import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../platform/utils/log_utils.dart';

/// 有线 信号图片样式
class CustomWiredNetwork extends StatelessWidget {
  final int signal;
  final Color? color;
  final bool isOffline = true; // 是否在线
  const CustomWiredNetwork(this.signal,
      {super.key, this.color, required bool isOffline});

  @override
  Widget build(BuildContext context) {
    return _getCellularImage(signal, isOffline, color);
  }

  _getCellularImage(int signal, bool isOffline, Color? color) {
    logger.d('有线网络信号强度: $signal');
    if (isOffline) {
      // 无网络
      return SvgPicture.asset(
        'lib/assets/icons/icon_wired_network_not_connect.svg',
        width: 18.w,
        height: 18.w,
        colorFilter:
            ColorFilter.mode(Color.fromRGBO(0, 0, 0, 0.5), BlendMode.srcIn),
      );
    } else {
      // 有网络
      return SvgPicture.asset(
        'lib/assets/icons/icon_wired_network_connect.svg',
        width: 18.w,
        height: 18.w,
        colorFilter:
            ColorFilter.mode(Color.fromRGBO(0, 0, 0, 0.5), BlendMode.srcIn),
      );
    }
  }
}
