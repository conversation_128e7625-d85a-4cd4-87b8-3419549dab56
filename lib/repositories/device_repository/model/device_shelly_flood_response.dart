import 'package:json_annotation/json_annotation.dart';

part 'device_shelly_flood_response.g.dart';

enum OnlineStatus {
  @JsonValue(1)
  online,
  @JsonValue(2)
  offline,
}

enum FloodAlarmStatus {
  normal,
  alarm;

  static FloodAlarmStatus fromString(String? value) {
    if (value == null) return FloodAlarmStatus.normal;
    switch (value) {
      case 'ALARM':
        return FloodAlarmStatus.alarm;
      default:
        return FloodAlarmStatus.normal;
    }
  }
}

enum MuteStatus {
  normal,
  mute;

  static MuteStatus fromString(String? value) {
    if (value == null) return MuteStatus.normal;
    switch (value) {
      case 'MUTE':
        return MuteStatus.mute;
      default:
        return MuteStatus.normal;
    }
  }
}

@JsonSerializable()
class ShellyFloodResponse {
  @JsonKey(name: "baseVO")
  DeviceDetailModel? baseVo;
  @JsonKey(name: "devicePropertyVO")
  DevicePropertyVO? devicePropertyVO;
  @JsonKey(name: "devices")
  List<FloodDevice>? devices;

  @Json<PERSON>ey(name: "deviceStatus")
  OnlineStatus deviceStatus;

  /// 设备状态 0 未激活 1 在线 2 离线
  @JsonKey(name: "parentDeviceStatus")
  OnlineStatus parentDeviceStatus;

  ///
  bool get isOffline =>
      parentDeviceStatus == OnlineStatus.offline ||
      deviceStatus == OnlineStatus.offline;

  /// 安全获取属性值的辅助方法
  String? _getPropertyValue(String identifier) {
    try {
      final propertyList = devices?.first.propertyVoList;
      if (propertyList == null || propertyList.isEmpty) return null;

      final property = propertyList.firstWhere(
        (element) => element.identifier == identifier,
        orElse: () => FloodPropertyVoList(),
      );
      return property.value;
    } catch (e) {
      return null;
    }
  }

  /// 工作状态
  FloodAlarmStatus get alarmStatus {
    final alarmValue = _getPropertyValue("alarm");
    final processedValue =
        alarmValue?.split("tsl.HB-FLOOD-MODEL.properties.alarm.value.").last;
    return FloodAlarmStatus.fromString(processedValue);
  }

  /// 电量
  String? get battery => _getPropertyValue("battCap");

  /// 静音状态
  MuteStatus get muteStatus {
    final silentValue = _getPropertyValue("silent");
    final processedValue =
        silentValue?.split("tsl.HB-FLOOD-MODEL.properties.silent.value.").last;
    return MuteStatus.fromString(processedValue);
  }

  /// 电压
  String? get voltage => _getPropertyValue("battVol");

  /// 信号强度 (rssi)
  String? get rssi => _getPropertyValue("rssi");

  /// 雨天模式设置 (rainModSet)
  String? get rainModeSet => _getPropertyValue("rainModSet");

  /// 雨天模式 (rainMod)
  String? get rainMode => _getPropertyValue("rainMod");

  /// 故障状态 (fault) - 0：正常；非0：故障
  String? get fault => _getPropertyValue("fault");

  /// 温度 (temp)
  String? get temperature => _getPropertyValue("temp");

  /// 设备重启 (deviceRebot)
  String? get deviceReboot => _getPropertyValue("deviceRebot");

  /// 设备时间 (devTime)
  String? get deviceTime => _getPropertyValue("devTime");

  /// 设备型号 (devMode)
  String? get deviceModel => _getPropertyValue("devMode");

  /// 厂家信息 (factory)
  String? get factory => _getPropertyValue("factory");

  /// SN码 (snCode)
  String? get snCode => _getPropertyValue("snCode");

  /// 软件版本 (softVer)
  String? get softwareVersion => _getPropertyValue("softVer");

  /// 通讯状态 (connSta)
  String? get connectionStatus => _getPropertyValue("connSta");

  /// 设备名称
  String get deviceName => baseVo?.deviceName ?? "";
  ShellyFloodResponse({
    this.baseVo,
    this.devicePropertyVO,
    this.devices,
    this.deviceStatus = OnlineStatus.online,
    this.parentDeviceStatus = OnlineStatus.online,
  });

  factory ShellyFloodResponse.fromJson(Map<String, dynamic> json) =>
      _$ShellyFloodResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ShellyFloodResponseToJson(this);
}

@JsonSerializable()
class DeviceDetailModel {
  @JsonKey(name: "deviceName")
  String? deviceName;
  @JsonKey(name: "deviceNo")
  String? deviceNo;
  DeviceDetailModel({
    this.deviceName,
    this.deviceNo,
  });

  factory DeviceDetailModel.fromJson(Map<String, dynamic> json) =>
      _$DeviceDetailModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceDetailModelToJson(this);
}

@JsonSerializable()
class DevicePropertyVO {
  @JsonKey(name: "deviceName")
  String? deviceName;
  @JsonKey(name: "deviceNo")
  String? deviceNo;
  @JsonKey(name: "modelKey")
  String? modelKey;
  @JsonKey(name: "productKey")
  String? productKey;

  @JsonKey(name: "dcOutEnable")
  int? dcOutEnable;
  DevicePropertyVO({
    this.deviceName,
    this.deviceNo,
    this.modelKey,
    this.productKey,
  });

  factory DevicePropertyVO.fromJson(Map<String, dynamic> json) =>
      _$DevicePropertyVOFromJson(json);

  Map<String, dynamic> toJson() => _$DevicePropertyVOToJson(this);
}

@JsonSerializable()
class FloodDevice {
  @JsonKey(name: "productKey")
  String? productKey;

  @JsonKey(name: "propertyVOList")
  List<FloodPropertyVoList>? propertyVoList;

  FloodDevice({
    this.propertyVoList,
    this.productKey,
  });

  factory FloodDevice.fromJson(Map<String, dynamic> json) =>
      _$FloodDeviceFromJson(json);

  Map<String, dynamic> toJson() => _$FloodDeviceToJson(this);
}

@JsonSerializable()
class FloodPropertyVoList {
  @JsonKey(name: "identifier")
  String? identifier;
  @JsonKey(name: "name")
  String? name;
  @JsonKey(name: "unit")
  String? unit;
  @JsonKey(name: "value")
  String? value;
  @JsonKey(name: "enumType")
  bool? enumType;

  FloodPropertyVoList({
    this.identifier,
    this.name,
    this.unit,
    this.value,
    this.enumType,
  });

  factory FloodPropertyVoList.fromJson(Map<String, dynamic> json) =>
      _$FloodPropertyVoListFromJson(json);

  Map<String, dynamic> toJson() => _$FloodPropertyVoListToJson(this);
}
