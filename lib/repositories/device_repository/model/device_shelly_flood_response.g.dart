// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_shelly_flood_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShellyFloodResponse _$ShellyFloodResponseFromJson(Map<String, dynamic> json) =>
    ShellyFloodResponse(
      baseVo: json['baseVO'] == null
          ? null
          : DeviceDetailModel.fromJson(json['baseVO'] as Map<String, dynamic>),
      devicePropertyVO: json['devicePropertyVO'] == null
          ? null
          : DevicePropertyVO.fromJson(
              json['devicePropertyVO'] as Map<String, dynamic>),
      devices: (json['devices'] as List<dynamic>?)
          ?.map((e) => FloodDevice.fromJson(e as Map<String, dynamic>))
          .toList(),
      deviceStatus:
          $enumDecodeNullable(_$OnlineStatusEnumMap, json['deviceStatus']) ??
              OnlineStatus.online,
      parentDeviceStatus: $enumDecodeNullable(
              _$OnlineStatusEnumMap, json['parentDeviceStatus']) ??
          OnlineStatus.online,
    );

Map<String, dynamic> _$ShellyFloodResponseToJson(
        ShellyFloodResponse instance) =>
    <String, dynamic>{
      'baseVO': instance.baseVo,
      'devicePropertyVO': instance.devicePropertyVO,
      'devices': instance.devices,
      'deviceStatus': _$OnlineStatusEnumMap[instance.deviceStatus]!,
      'parentDeviceStatus': _$OnlineStatusEnumMap[instance.parentDeviceStatus]!,
    };

const _$OnlineStatusEnumMap = {
  OnlineStatus.online: 1,
  OnlineStatus.offline: 2,
};

DeviceDetailModel _$DeviceDetailModelFromJson(Map<String, dynamic> json) =>
    DeviceDetailModel(
      deviceName: json['deviceName'] as String?,
      deviceNo: json['deviceNo'] as String?,
    );

Map<String, dynamic> _$DeviceDetailModelToJson(DeviceDetailModel instance) =>
    <String, dynamic>{
      'deviceName': instance.deviceName,
      'deviceNo': instance.deviceNo,
    };

DevicePropertyVO _$DevicePropertyVOFromJson(Map<String, dynamic> json) =>
    DevicePropertyVO(
      deviceName: json['deviceName'] as String?,
      deviceNo: json['deviceNo'] as String?,
      modelKey: json['modelKey'] as String?,
      productKey: json['productKey'] as String?,
    )..dcOutEnable = json['dcOutEnable'] as int?;

Map<String, dynamic> _$DevicePropertyVOToJson(DevicePropertyVO instance) =>
    <String, dynamic>{
      'deviceName': instance.deviceName,
      'deviceNo': instance.deviceNo,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'dcOutEnable': instance.dcOutEnable,
    };

FloodDevice _$FloodDeviceFromJson(Map<String, dynamic> json) => FloodDevice(
      propertyVoList: (json['propertyVOList'] as List<dynamic>?)
          ?.map((e) => FloodPropertyVoList.fromJson(e as Map<String, dynamic>))
          .toList(),
      productKey: json['productKey'] as String?,
    );

Map<String, dynamic> _$FloodDeviceToJson(FloodDevice instance) =>
    <String, dynamic>{
      'productKey': instance.productKey,
      'propertyVOList': instance.propertyVoList,
    };

FloodPropertyVoList _$FloodPropertyVoListFromJson(Map<String, dynamic> json) =>
    FloodPropertyVoList(
      identifier: json['identifier'] as String?,
      name: json['name'] as String?,
      unit: json['unit'] as String?,
      value: json['value'] as String?,
      enumType: json['enumType'] as bool?,
    );

Map<String, dynamic> _$FloodPropertyVoListToJson(
        FloodPropertyVoList instance) =>
    <String, dynamic>{
      'identifier': instance.identifier,
      'name': instance.name,
      'unit': instance.unit,
      'value': instance.value,
      'enumType': instance.enumType,
    };
