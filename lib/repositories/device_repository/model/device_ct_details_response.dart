import 'package:flutter_basic/features/diy_new/accessories/model/accessorise_device_filter.dart';
import 'package:flutter_basic/repositories/device_repository/model/device_detail_response.dart';
import 'package:flutter_js/quickjs/ffi.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../platform/utils/log_utils.dart';

part 'device_ct_details_response.g.dart';

@JsonSerializable()
class CtDeviceDetailsResponse {
  /// 设备名称
  @Json<PERSON>ey(name: "deviceName")
  String deviceName;

  @JsonKey(name: "modelKey")
  String modelKey;

  @JsonKey(name: "deviceShap")
  int? deviceShap;

  @Json<PERSON>ey(name: "phasePropertyIdentifier")
  String phasePropertyIdentifier;

  @JsonKey(name: "productKey")
  String productKey;

  /// 能量调度相
  @JsonKey(name: "phasePropertyValue")
  String phasePropertyValue;

  /// 相位多语言显示key
  @JsonKey(name: "phasePropertyLanguageKey")
  String phasePropertyLanguageKey;

  /// ECO模式
  @Json<PERSON><PERSON>(name: "lowModeSetPropertyValue")
  LowModeSetPropertyValue? lowModeSetPropertyValue;

  /// 相位反转bit0值
  @JsonKey(name: "phaseReverseBit0Value")
  int? phaseReverseBit0Value;

  /// 相位反转bit1值
  @JsonKey(name: "phaseReverseBit1Value")
  int? phaseReverseBit1Value;

  /// 相位反转bit2值
  @JsonKey(name: "phaseReverseBit2Value")
  int? phaseReverseBit2Value;

  @JsonKey(name: "phaseReverseIdentifier")
  String? phaseReverseIdentifier;

  @JsonKey(name: "lowModeSetPropertyIdentifier")
  String? lowModeSetPropertyIdentifier;

  @JsonKey(name: "propertyVOList")
  List<PropertyVoList> propertyVOList;

  @JsonKey(name: "energyList")
  List<Map> energylist;

  /// 设备状态 0 未激活 1 在线 2 离线
  @JsonKey(name: "deviceStatus")
  int deviceStatus;

  /// 父级设备状态 0 未激活 1 在线 2 离线
  @JsonKey(name: "parentDeviceStatus")
  int parentDeviceStatus;

  bool get isOffline => parentDeviceStatus == 2 || deviceStatus == 2;

  /// 相位反转bit
  List<int> get phaseReverseBit => [
        phaseReverseBit0Value ?? 0,
        phaseReverseBit1Value ?? 0,
        phaseReverseBit2Value ?? 0,
      ];

  Map<String, double?> get energyMap {
    var map = <String, double?>{};
    for (var item in energylist) {
      map[item['propertyLanguageIdentifier'].toString()] = item["value"];
    }
    return map;
  }

  /// 设备类型
  AccessoriesDeviceType get deviceType {
    switch (deviceShap) {
      case 1:
        return AccessoriesDeviceType.ct;
      case 2:
        return AccessoriesDeviceType.ct3;
      default:
        logger.e("未知的设备类型");
        return AccessoriesDeviceType.ct;
    }
  }

  /// 相位
  ShellyCtPhaseType? get phase {
    var value = int.parse(phasePropertyValue);
    // 使用设备类型获取可用的相位类型列表
    // Note: Here we can't access the factoryStr, so we're just checking if the
    // value matches any phase type. The filtering for what's displayed in the UI is done
    // in the valuesFor method.
    return ShellyCtPhaseType.values
        .firstWhereOrNull((element) => element.value == value);
  }

  /// 通道
  ShellyCtChannelType? get channel {
    var value = int.parse(phasePropertyValue);
    return ShellyCtChannelType.values
        .firstWhereOrNull((element) => element.value == value);
  }

  CtDeviceDetailsResponse({
    this.phasePropertyValue = "0",
    this.deviceShap,
    required this.productKey,
    required this.phasePropertyIdentifier,
    required this.phasePropertyLanguageKey,
    this.lowModeSetPropertyValue,
    required this.propertyVOList,
    required this.deviceName,
    required this.phaseReverseBit0Value,
    required this.phaseReverseBit1Value,
    required this.phaseReverseBit2Value,
    required this.modelKey,
    this.phaseReverseIdentifier,
    this.lowModeSetPropertyIdentifier,
    this.energylist = const [],
    this.deviceStatus = 1,
    this.parentDeviceStatus = 1,
  });

  factory CtDeviceDetailsResponse.fromJson(Map<String, dynamic> json) =>
      _$CtDeviceDetailsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CtDeviceDetailsResponseToJson(this);
}

enum LowModeSetPropertyValue {
  @JsonValue('0')
  close,
  @JsonValue('1')
  open;

  static LowModeSetPropertyValue parse(int? value) => switch (value) {
        0 => LowModeSetPropertyValue.close,
        1 => LowModeSetPropertyValue.open,
        _ => LowModeSetPropertyValue.close,
      };
}
