import 'dart:convert';

import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/user/UserManager.dart';
import 'package:flutter_basic/platform/utils/file_utils.dart';
import 'package:flutter_basic/repositories/api/api.dart';
import 'package:flutter_basic/repositories/download_repository/model/tsl_model.dart';
import 'package:flutter_basic/repositories/download_repository/model/tsl_property_model.dart';
import 'package:flutter_basic/repositories/download_repository/model/tsl_services_model.dart';
import 'package:flutter_basic/repositories/system_repository/model/device_model.dart';
import 'package:flutter_basic/repositories/system_repository/model/dynamic_electricity.dart';
import 'package:flutter_basic/repositories/system_repository/model/standby_mode_record.dart';
import 'package:flutter_basic/repositories/system_repository/model/system_detail/system_detail_response.dart';
import 'package:flutter_js/quickjs/ffi.dart';
import 'package:http/http.dart' as http;
import 'package:http/http.dart';

import '../../platform/community/monitor/constants.dart';
import '../../platform/platform.dart';
import '../custom_exception/ServiceException.dart';
import 'model/country_info.dart';

enum invokeType { setProperties, invokeService, none }

class SystemClientRepository {
  static SystemClientRepository? _instance;

  static SystemClientRepository get instance {
    _instance ??= SystemClientRepository();
    return _instance!;
  }

  Future<CountryInfo> getCountryInfo() async {
    try {
      Response data = (await http
          .get(Uri(scheme: 'http', host: 'ip-api.com', path: '/json')));
      Map map = jsonDecode(data.body);
      return CountryInfo(
        country: map['country'] ?? '',
        countryCode: map['countryCode'] ?? '',
      );
    } catch (_) {
      return CountryInfo(
        country: '',
        countryCode: '',
      );
    }
  }

  Future<void> updateSystemInfo(Map<String, dynamic> data) async {
    final res = await HttpManager.instance
        .post(SystemEndPoint.updateSystem, body: data);
    if (res.code == 200 && res.result != null) {
      return res.result;
    }
    throw ServiceException(code: res.code, msg: res.key);
  }

  Future<SystemDetailResponse> getSystemInfo(String systemId) async {
    try {
      final baseResult = await HttpManager.instance
          .get(SystemEndPoint.getSystemInfo + systemId);
      var res = SystemDetailResponse.fromJson(baseResult.result);
      if (LocalModeManger.instance.isLocalModeNow) {
        res = await _getSystemInfoFromLocal(res);
      }
      return res;
    } catch (_, s) {
      logger.d('error ${_.toString()}');
      logger.d('error ${s.toString()}');
      rethrow;
    }
  }

  /// 本地模式获取系统信息
  Future<SystemDetailResponse> _getSystemInfoFromLocal(
      SystemDetailResponse model) async {
    var systemDetailCommonRunSetUp = model.systemDetailCommonRunSetUp;
    var deviceSn = systemDetailCommonRunSetUp?.emsDeviceNo;
    if (deviceSn == null) return model;

    var params = [
      diy_point_runMode,
      diy_point_self_use_charge_limit,
      diy_point_self_use_discharge_limit,
      diy_point_battery_charge_soc_limit,
      diy_point_battery_discharge_soc_limit,
      diy_point_allow_grid_connect,
      diy_point_power_limit
    ];
    final res = await LocalModeManger.instance.getDeviceData(
      sn: deviceSn,
      params: params,
    );
    if (res.info?.isSuccess != true) return model;

    systemDetailCommonRunSetUp?.runModelFunctionIdentifier = diy_point_runMode;

    var list = res.info!.devList!
        .firstWhere((element) => element.devSn == deviceSn)
        .meterList!;

    var runModeRes = list.firstWhereOrNull((e) => e[0] == diy_point_runMode);
    if (runModeRes == null) return model;
    int runMode = int.parse(runModeRes[1]);
    systemDetailCommonRunSetUp?.runModel = runMode;
    var lang = systemDetailCommonRunSetUp?.runModelLanguageKey?.split('.');
    if (lang != null) {
      lang[lang.length - 1] = runModeRes[1];
      systemDetailCommonRunSetUp?.runModelLanguageKey = lang.join('.');
    }

    for (var item in list) {
      switch (item[0]) {
        case diy_point_self_use_charge_limit:
          systemDetailCommonRunSetUp?.chargingLimit =
              double.parse(item[1]) / 10;
          break;
        case diy_point_self_use_discharge_limit:
          systemDetailCommonRunSetUp?.dischargeLimit =
              double.parse(item[1]) / 10;
          break;
      }
      if (item[0] == diy_point_allow_grid_connect) {
        systemDetailCommonRunSetUp?.antiBackflowEnable = int.parse(item[1]);
      }
      if (item[0] == diy_point_power_limit) {
        systemDetailCommonRunSetUp?.maxFeedPower = int.parse(item[1]);
      }
    }

    return model;
  }

  /// 删除系统
  /// flag = true 不管系统是否在线，都删除
  Future<String> deleteSystem(String systemId, bool flag) async {
    try {
      var res = await HttpManager.instance
          .delete('${SystemEndPoint.deleteSystem}$systemId/$flag');
      if (res.code == 200 && res.result != null) {
        return res.result;
      }
      throw ServiceException(code: res.code, msg: res.key);
    } catch (_, s) {
      logger.d('error ${_.toString()}');
      logger.d('error ${s.toString()}');
      rethrow;
    }
  }

  /// 取消并机
  Future<String> cancelParallel(String systemId) async {
    try {
      var res = await HttpManager.instance
          .post('${SystemEndPoint.cancelCluster}$systemId');
      if (res.code == 200 && res.result != null) {
        return "";
      }
      throw ServiceException(code: res.code, msg: res.key);
    } catch (_, s) {
      logger.d('error ${_.toString()}');
      logger.d('error ${s.toString()}');
      rethrow;
    }
  }

  Future<StandbyModeRecord> standbyModelRecord(
      int pageNo, String systemId) async {
    try {
      final baseResultData = await HttpManager.instance
          .post(SystemEndPoint.standbyModelRecord, body: {
        "pageSize": 20,
        "pageNo": pageNo,
        "search": [
          {"field": "systemId", "value": systemId, "operator": "EQ"}
        ]
      });
      if (baseResultData.code == 200 && baseResultData.result != null) {
        baseResultData.extra;
        final mode = StandbyModeRecord.fromJson(baseResultData.result);
        mode.extra = Extra(
            events: baseResultData.extra['events'],
            total: baseResultData.extra['total'],
            max: baseResultData.extra['max']);
        return mode;
      }
      throw ServiceException(
          code: baseResultData.code, msg: baseResultData.msg);
    } catch (_) {
      throw ServiceException();
    }
  }

  Future<dynamic> invoke(String deviceNo, String modelKey, String identify,
      String productKey, dynamic params) async {
    invokeType type = invokeType.none;
    if (LocalModeManger.instance.isLocalModeNow) {
      try {
        await LocalModeManger.instance.setDeviceData(
            sn: deviceNo,
            params: params is List
                ? params
                : [
                    [identify, '$params']
                  ]);
        return;
      } catch (error) {
        rethrow;
      }
    }
    try {
      var tslMap = await FileUtils.singleton.tslJsonMap;
      TslModel? model = tslMap[modelKey];
      if (model == null) {
        logger.d('not found tsl model by $modelKey');
        throw Exception();
      }
      TslServicesModel? servicesModel;
      var servicesLength = model.services?.length ?? 0;
      for (int i = 0; i < servicesLength; i++) {
        var element = model.services![i];
        if (element.identifier == identify) {
          servicesModel = element;
          break;
        }
      }
      TslPropertyModel? propertyModel;
      var propertiesLength = model.properties?.length ?? 0;
      for (int i = 0; i < propertiesLength; i++) {
        var element = model.properties![i];
        if (element.identifier == identify) {
          propertyModel = element;
          break;
        }
      }
      if (propertyModel != null) {
        type = invokeType.setProperties;
      } else if (servicesModel != null) {
        type = invokeType.invokeService;
      }
      switch (type) {
        case invokeType.invokeService:
          logger.d(
              'params.runtimeType : ${params.runtimeType}  ${params is List<dynamic>}');
          if (params is List<dynamic> &&
              params.isNotEmpty &&
              params.first is! Map) {
            List<dynamic> list = params;
            Map<String, dynamic> fullParams = {};
            for (int i = 0; i < list.length; i++) {
              logger.d(
                  'servicesModel?.inputData?[i].identifier : ${servicesModel?.inputData?[i].identifier}');
              fullParams[servicesModel?.inputData?[i].identifier ?? ''] =
                  list[i];
            }
            logger.d('fullParams : ${fullParams.toString()}');
            return await invokeService(deviceNo, modelKey, identify, productKey,
                params, servicesModel!.name,
                fullParams: fullParams);
          }

          String? paramsKey = servicesModel?.inputData?.isEmpty == true
              ? null
              : servicesModel?.inputData?[0].identifier;
          // 一个指令需要传多的参数的，需要额外处理
          return await invokeService(deviceNo, modelKey, identify, productKey,
              params, servicesModel!.name,
              paramsKey: paramsKey);
        case invokeType.setProperties:
          return await setProperties(
              deviceNo, modelKey, identify, productKey, params);
        case invokeType.none:
        default:
          return;
      }
    } catch (_, s) {
      logger.d('error ${_.toString()}');
      logger.d('error ${s.toString()}');
      rethrow;
    }
  }

  Future<dynamic> invokeService(String deviceNo, String modelKey,
      String identify, String productKey, dynamic params, String name,
      {String? paramsKey, dynamic fullParams}) async {
    try {
      String method = 'link.device.service.invoke';
      final baseResult =
          await HttpManager.instance.post(SystemEndPoint.invokeDevice, body: {
        'deviceNo': deviceNo,
        'params': fullParams ?? {paramsKey ?? identify: params},
        'gmtCreate': DateTime.now().millisecondsSinceEpoch,
        'identifier': identify,
        'productKey': productKey,
        'modelKey': modelKey,
        'method': method,
        'name': name
      });
      if (baseResult.code == 200 && baseResult.success) {
        return baseResult.result;
      }
      throw ServiceException(code: baseResult.code, msg: baseResult.key);
    } catch (_, s) {
      logger.d('error ${_.toString()}');
      logger.d('error ${s.toString()}');
      rethrow;
    }
  }

  Future<void> setProperties(String deviceNo, String modelKey, String identify,
      String productKey, dynamic params) async {
    try {
      final baseResult = await HttpManager.instance.post(
          SystemEndPoint.setProperties + deviceNo,
          body: {'value': params, 'name': identify});
      if (baseResult.code == 200 &&
          baseResult.success &&
          baseResult.result != null &&
          baseResult.result != false) {
        return;
      }
      throw ServiceException(
        code: baseResult.code,
        msg: baseResult.key == 'base.action.success' ? null : baseResult.key,
      );
    } catch (_, s) {
      logger.d('error ${_.toString()}');
      logger.d('error ${s.toString()}');
      rethrow;
    }
  }

  /// 通知服务器更新设备信息
  Future updateSystemDeviceDataGet(String systemId) async {
    try {
      await HttpManager.instance
          .get(SystemEndPoint.updateSystemDeviceDataGet + systemId);
    } catch (_) {}
  }

  /// 获取系统设备列表
  Future<List<DeviceModel>> getDeviceList(String systemId) async {
    final baseResult = await HttpManager.instance
        .get(InstallerEndPoint.getDevicesBySystemId + systemId);
    if (baseResult.code == 200 && baseResult.result != null) {
      return baseResult.result
          .map<DeviceModel>((e) => DeviceModel.fromJsonFactory(e, systemId))
          .toList();
    }
    throw ServiceException(code: baseResult.code, msg: baseResult.key);
  }

  Future<SystemDetailResponse> getEpcSystemDetail(String systemId) async {
    final api = UserManager.instance.role == UserRole.installer
        ? InstallerEndPoint.getSystemDetail
        : SystemEndPoint.getEpcSystemInfo;

    final baseResult = await HttpManager.instance.get(api + systemId);
    logger.i('打印日志baseResult======:>  $baseResult');

    if (baseResult.code == 200 && baseResult.result != null) {
      var res = SystemDetailResponse.fromJson(baseResult.result);
      return res;
    }
    throw ServiceException(code: baseResult.code, msg: baseResult.key);
  }

  Future<void> updateEpcSystemInfo(
      String systemId, Map<String, dynamic> params) async {
    params['systemId'] = systemId;
    String api = UserManager.instance.role == UserRole.installer
        ? InstallerEndPoint.updateSystemInfo
        : SystemEndPoint.updateEpcSystemInfo;

    final baseResult = await HttpManager.instance.post(api, body: params);
    if (baseResult.code == 200 && baseResult.success) {
      if (baseResult.result) return;
      throw ServiceException(code: baseResult.code, msg: 'common.operateFail');
    }
    throw ServiceException(code: baseResult.code, msg: baseResult.key);
  }

  Future<void> removeEpcSystemById(String systemId) async {
    final baseResult = await HttpManager.instance
        .put(SystemEndPoint.removeEpcSystemById + systemId);
    if (baseResult.code == 200 &&
        baseResult.success &&
        baseResult.result != null &&
        baseResult.result != false) {
      return;
    }
    throw ServiceException(code: baseResult.code, msg: baseResult.key);
  }

  /// 设置设备离线
  /// [deviceSn] 网关设备sn
  Future<bool> setDeviceOffline(String deviceSn) async {
    final baseResult =
        await HttpManager.instance.put(SystemEndPoint.setOffline + deviceSn);
    if (baseResult.code == 200 && baseResult.success) {
      return true;
    }
    throw ServiceException(code: baseResult.code, msg: baseResult.key);
  }

  /// 判断当前系统是否已设置动态电价
  Future<bool> getIsDynamicTariff(String systemId) async {
    final baseResult = await HttpManager.instance
        .get(SystemEndPoint.getIsDynamicTariff + systemId);
    return baseResult.result['config'] as bool? ?? false;
  }

  /// 获取电价供应商列表
  Future<List<PriceSupplier>> getTariffSourceList() async {
    final baseResult = await HttpManager.instance
        .post(SystemEndPoint.getTariffSourceList, body: {});
    if (baseResult.code == 200 && baseResult.result != null) {
      return baseResult.result
          .map<PriceSupplier>((e) => PriceSupplier.fromJson(e))
          .toList();
    }
    throw ServiceException(code: baseResult.code, msg: baseResult.key);
  }

  /// 获取电价折线图数据
  Future<PriceChartResult> getTariffChartData(String priceId) async {
    final baseResult = await HttpManager.instance
        .post(SystemEndPoint.getTariffChartData + priceId, body: {});
    return PriceChartResult.fromJson(baseResult.result);
  }

  /// 设置用户动态电价详情
  Future<bool> setUserDynamicTariff(Map<String, dynamic> params) async {
    final baseResult = await HttpManager.instance
        .post(SystemEndPoint.setUserDynamicTariff, body: params);
    return baseResult.success;
  }

  /// 获取用户动态电价详情
  Future<UserDynamicTariff?> getUserDynamicTariff(String systemId) async {
    final baseResult = await HttpManager.instance
        .get(SystemEndPoint.getUserDynamicTariff + systemId);
    if (baseResult.code == 200 && baseResult.result != null) {
      return (baseResult.result as Map<String, dynamic>).isNotEmpty
          ? UserDynamicTariff.fromJson(baseResult.result)
          : null;
    }
    return null;
  }
}
