import 'package:flutter_basic/repositories/system_repository/model/device_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pcs_device_model.g.dart';

@JsonSerializable()
class EpcPcsDeviceModel extends DeviceModel {
  /// Pv 功率列表
  List<double?> pvPowerList;

  /// pcs总功率
  double? totalPower;

  /// 通讯状态 0 正常 1异常
  int? connSta;

  /// 在线
  bool get isOnline {
    if (connSta == null) return true;
    return connSta == 0;
  }

  EpcPcsDeviceModel({
    required super.id,
    required super.name,
    required super.deviceNo,
    required super.modelKey,
    required super.productKey,
    required super.status,
    super.indexNo,
    this.pvPowerList = const [],
    this.totalPower,
    this.connSta,
  });

  factory EpcPcsDeviceModel.fromJson(Map<String, dynamic> json) =>
      _$EpcPcsDeviceModelFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$EpcPcsDeviceModelToJson(this);

  @override
  String get mqttParams =>
      'connSta|activePower|pvChannels|pvP1|pvP2|pvP3|pvP4|pvP5|pvP6';

  @override
  EpcPcsDeviceModel mergePhysicalModel(Map<String, dynamic> json) {
    final params = json['propertiesObj'] as Map<String, dynamic>;
    pvPowerList = List.generate(6, (index) {
      return toKw(params['pvP${index + 1}']) ?? 0;
    });
    totalPower = toKw(params['activePower']) ?? totalPower;
    connSta = params['connSta'] ?? connSta;
    return this;
  }

  EpcPcsDeviceModel copyOfflineModel() {
    return EpcPcsDeviceModel(
      id: id,
      name: name,
      deviceNo: deviceNo,
      modelKey: modelKey,
      productKey: productKey,
      status: status,
      indexNo: indexNo,
      pvPowerList: List.generate(pvPowerList.length, (index) => null),
      totalPower: 0,
      connSta: 0,
    );
  }
}
