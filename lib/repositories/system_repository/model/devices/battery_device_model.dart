import 'package:flutter_basic/repositories/system_repository/model/device_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'battery_device_model.g.dart';

/// 电池包
@JsonSerializable()
class EpcBatteryDeviceModel extends DeviceModel {
  int? connSta;

  /// 是否在线
  bool get isOnline {
    if (connSta == null) return true;
    return connSta == 0;
  }

  EpcBatteryDeviceModel({
    required super.id,
    required super.name,
    required super.deviceNo,
    required super.modelKey,
    required super.productKey,
    required super.status,
    super.indexNo,
    this.connSta,
  });

  factory EpcBatteryDeviceModel.fromJson(Map<String, dynamic> json) =>
      _$EpcBatteryDeviceModelFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$EpcBatteryDeviceModelToJson(this);

  @override
  String get mqttParams => 'connSta';

  @override
  EpcBatteryDeviceModel mergePhysicalModel(Map<String, dynamic> json) {
    final params = json['propertiesObj'] as Map<String, dynamic>;
    connSta = params['connSta'] ?? connSta;

    return this;
  }

  EpcBatteryDeviceModel copyOfflineModel() {
    return EpcBatteryDeviceModel(
      id: id,
      name: name,
      deviceNo: deviceNo,
      modelKey: modelKey,
      productKey: productKey,
      status: status,
      indexNo: indexNo,
      connSta: 0,
    );
  }
}
