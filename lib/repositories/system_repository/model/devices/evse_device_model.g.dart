// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'evse_device_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EpcEvseDeviceModel _$EpcEvseDeviceModelFromJson(Map<String, dynamic> json) =>
    EpcEvseDeviceModel(
      id: json['id'] as String,
      name: json['name'] as String,
      deviceNo: json['deviceNo'] as String,
      modelKey: json['modelKey'] as String,
      productKey: json['productKey'] as String,
      status: $enumDecode(_$DeviceStatusEnumMap, json['status']),
      indexNo: json['indexNo'] as int?,
      hardwareVersion: json['hardwareVersion'] as String?,
      snCode: json['snCode'] as String?,
      workMode: $enumDecodeNullable(_$EvseWorkModeEnumMap, json['workMode']),
      appChargeStatus: $enumDecodeNullable(
          _$EvseAppChargeStatusEnumMap, json['appChargeStatus']),
      evseType: $enumDecodeNullable(_$EvseChargerTypeEnumMap, json['evseType']),
      chargeVoltage: (json['chargeVoltage'] as num?)?.toDouble(),
      chargeCurrent: (json['chargeCurrent'] as num?)?.toDouble(),
      chargeTime: json['chargeTime'] as int?,
      chargeAmount: (json['chargeAmount'] as num?)?.toDouble(),
      chargeStatus:
          $enumDecodeNullable(_$EvseChargeStatusEnumMap, json['chargeStatus']),
      connectionStatus: $enumDecodeNullable(
          _$EvseConnectionStatusEnumMap, json['connectionStatus']),
    )..softwareVersion = json['softwareVersion'] as String?;

Map<String, dynamic> _$EpcEvseDeviceModelToJson(EpcEvseDeviceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'deviceNo': instance.deviceNo,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'status': _$DeviceStatusEnumMap[instance.status]!,
      'indexNo': instance.indexNo,
      'hardwareVersion': instance.hardwareVersion,
      'softwareVersion': instance.softwareVersion,
      'snCode': instance.snCode,
      'workMode': _$EvseWorkModeEnumMap[instance.workMode],
      'appChargeStatus': _$EvseAppChargeStatusEnumMap[instance.appChargeStatus],
      'evseType': _$EvseChargerTypeEnumMap[instance.evseType],
      'chargeVoltage': instance.chargeVoltage,
      'chargeCurrent': instance.chargeCurrent,
      'chargeTime': instance.chargeTime,
      'chargeAmount': instance.chargeAmount,
      'chargeStatus': _$EvseChargeStatusEnumMap[instance.chargeStatus],
      'connectionStatus':
          _$EvseConnectionStatusEnumMap[instance.connectionStatus],
    };

const _$DeviceStatusEnumMap = {
  DeviceStatus.notActive: 0,
  DeviceStatus.online: 1,
  DeviceStatus.offline: 2,
};

const _$EvseWorkModeEnumMap = {
  EvseWorkMode.plugAndCharge: 0,
  EvseWorkMode.appManualCharge: 1,
  EvseWorkMode.plugAndDischarge: 2,
  EvseWorkMode.appManualDischarge: 3,
};

const _$EvseAppChargeStatusEnumMap = {
  EvseAppChargeStatus.stop: 0,
  EvseAppChargeStatus.manualCharging: 1,
};

const _$EvseChargerTypeEnumMap = {
  EvseChargerType.none: 0,
  EvseChargerType.dc: 1,
  EvseChargerType.ac: 2,
};

const _$EvseChargeStatusEnumMap = {
  EvseChargeStatus.standbyUnplugged: 0,
  EvseChargeStatus.pluggedNotCharging: 1,
  EvseChargeStatus.pluggedCharging: 2,
  EvseChargeStatus.charging: 3,
  EvseChargeStatus.stopped: 4,
  EvseChargeStatus.unpluggedToStandby: 5,
  EvseChargeStatus.fault: 6,
};

const _$EvseConnectionStatusEnumMap = {
  EvseConnectionStatus.normal: 0,
  EvseConnectionStatus.error: 1,
};
