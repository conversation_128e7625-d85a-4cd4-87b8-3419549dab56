// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'battery_device_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EpcBatteryDeviceModel _$EpcBatteryDeviceModelFromJson(
        Map<String, dynamic> json) =>
    EpcBatteryDeviceModel(
      id: json['id'] as String,
      name: json['name'] as String,
      deviceNo: json['deviceNo'] as String,
      modelKey: json['modelKey'] as String,
      productKey: json['productKey'] as String,
      status: $enumDecode(_$DeviceStatusEnumMap, json['status']),
      indexNo: json['indexNo'] as int?,
      connSta: json['connSta'] as int?,
    );

Map<String, dynamic> _$EpcBatteryDeviceModelToJson(
        EpcBatteryDeviceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'deviceNo': instance.deviceNo,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'status': _$DeviceStatusEnumMap[instance.status]!,
      'indexNo': instance.indexNo,
      'connSta': instance.connSta,
    };

const _$DeviceStatusEnumMap = {
  DeviceStatus.notActive: 0,
  DeviceStatus.online: 1,
  DeviceStatus.offline: 2,
};
