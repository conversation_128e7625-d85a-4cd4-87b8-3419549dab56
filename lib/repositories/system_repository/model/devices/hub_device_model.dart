import 'package:flutter_basic/repositories/installer_repository/installer_client_repository.dart';
import 'package:flutter_basic/repositories/installer_repository/model/hub_setting_model.dart';
import 'package:flutter_basic/repositories/system_repository/model/device_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'hub_device_model.g.dart';

@JsonSerializable()
class EpcHubDeviceModel extends DeviceModel {
  /// 系统ID
  late String _systemId;

  /// 门禁状态
  AccessControlStatus accessControlStatus;

  /// 烟雾传感器状态
  SensorStatus smokeStatus;

  /// 水浸传感器状态
  SensorStatus immerStatus;

  /// 支路信息
  List<BranchSettingModel?> _branchList = [];

  double? evChargePower;

  /// 是否存在充电桩
  bool get hasEvCharger =>
      _branchList.any((e) => e?.type == BranchSettingType.evChanger);

  /// 设置系统ID
  set systemId(String value) {
    _systemId = value;

    /// 获取支路详情
    InstallerClientRepository.instance.getHubInfo(_systemId).then((value) {
      _branchList = value.first.branchSettingMap.values.toList();
    });
  }

  /// pv 利旧
  bool get hasPv =>
      _branchList.any((e) => e?.type == BranchSettingType.acPortOfPv);

  /// 获取充电桩mqtt召唤参数
  List<String> get evChargerMqttParams => _branchList
      .where((e) => e?.type == BranchSettingType.evChanger)
      .map((e) => 'branchActPow${e!.branchRealNo}')
      .toList();

  EpcHubDeviceModel({
    required super.id,
    required super.name,
    required super.deviceNo,
    required super.modelKey,
    required super.productKey,
    required super.status,
    super.indexNo,
    this.accessControlStatus = AccessControlStatus.close,
    this.smokeStatus = SensorStatus.normal,
    this.immerStatus = SensorStatus.normal,
  });

  /// 获取mqtt召唤参数
  @override
  String get mqttParams => [
        'accessContrStatus',
        'smokeStatus',
        'immerStatus',
        ...evChargerMqttParams
      ].join('|');

  /// 合并物模型信息
  @override
  EpcHubDeviceModel mergePhysicalModel(Map<String, dynamic> json) {
    final params = json['propertiesObj'] as Map<String, dynamic>;

    accessControlStatus = $enumDecodeNullable(
            _$AccessControlStatusEnumMap, params['accessContrStatus']) ??
        accessControlStatus;

    smokeStatus =
        $enumDecodeNullable(_$SensorStatusEnumMap, params['smokeStatus']) ??
            smokeStatus;

    immerStatus =
        $enumDecodeNullable(_$SensorStatusEnumMap, params['immerStatus']) ??
            immerStatus;

    evChargePower = evChargerMqttParams.isNotEmpty
        ? evChargerMqttParams
                .map((e) => params[e])
                .whereType<num>()
                .reduce((value, element) => value + element) /
            1000
        : null;

    return this;
  }

  factory EpcHubDeviceModel.fromJson(Map<String, dynamic> json) =>
      _$EpcHubDeviceModelFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$EpcHubDeviceModelToJson(this);
}

/// 传感器状态
enum SensorStatus {
  /// 正常
  @JsonValue(0)
  normal,

  /// 告警
  @JsonValue(1)
  alarm,

  /// 未安装
  @JsonValue(-1)
  notInstall,
}

enum AccessControlStatus {
  @JsonValue(0)
  close,
  @JsonValue(1)
  open;

  @override
  toString() => 'systemMonitor.access_control_status_$index';
}
