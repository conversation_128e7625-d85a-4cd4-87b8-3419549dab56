import 'package:flutter_basic/repositories/system_repository/model/device_model.dart';
import 'package:flutter_basic/repositories/system_repository/model/devices/hub_device_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ems_device_model.g.dart';

@JsonSerializable()
class EpcEmsDeviceModel extends DeviceModel {
  /// 系统型号
  EpcSystemModelType systemModelType;

  /// Pv总功率
  double? pvTotalPower;

  /// Pv总路数
  int pvRoutes;

  /// pv利旧 光伏系统功率 有HUB时才有
  double? pvSystemPower;

  /// 电网功率
  double? gridPower;

  /// 电网并网状态
  GridStatus? gridStatus;

  /// 工作状态
  DeviceWorkStatus? workStatus;

  /// 电池电量
  int? soc;

  /// 剩余能量
  double? remainEnergy;

  /// 充放电状态
  EpcChangeStatus? chargeStatus;

  /// 电池功率
  double? batteryPower;

  /// 加电包数量
  int batteryCount;

  /// 电网侧家庭负载功率
  int? nonBackupLoadPower;

  /// 电网侧家庭负载功率来源
  NonBackupLoadPowerSourceType nonBackupLoadPowerSource;

  /// EPS负载功率
  int? backupLoadPower;

  /// 充电桩功率
  int? evChargerPower;

  /// 充电桩状态
  int? evChargerStatus;

  /// 充电桩电流
  double? evChargerCurrent;

  /// 充电桩电压
  double? evChargerVoltage;

  /// 充电桩容量
  double? evChargerCapacity;

  /// 家庭负载功率
  double? homeLoadPower;

  /// 油机功率
  double? engineOutPutP;

  /// 工作模式
  EpcWorkModeType? workMode;

  EpcEmsDeviceModel({
    required super.id,
    required super.name,
    required super.deviceNo,
    required super.modelKey,
    required super.productKey,
    required super.status,
    super.indexNo,
    this.pvTotalPower,
    this.pvRoutes = 0,
    this.gridPower,
    this.gridStatus,
    this.workStatus,
    this.soc,
    this.remainEnergy,
    this.chargeStatus,
    this.nonBackupLoadPower,
    this.backupLoadPower,
    this.evChargerPower,
    this.nonBackupLoadPowerSource = NonBackupLoadPowerSourceType.acGrid,
    this.systemModelType = EpcSystemModelType.hub,
    this.homeLoadPower,
    this.batteryPower = 0,
    this.batteryCount = 1,
    this.workMode,
    this.pvSystemPower,
    this.engineOutPutP,
    this.evChargerStatus,
    this.evChargerCurrent,
    this.evChargerVoltage,
    this.evChargerCapacity,
  });

  factory EpcEmsDeviceModel.fromJson(Map<String, dynamic> json) =>
      _$EpcEmsDeviceModelFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$EpcEmsDeviceModelToJson(this);

  /// 获取mqtt召唤参数
  @override
  String get mqttParams =>
      'gridPower|workStatus|pvPower|gridConnStatus|workMode|pvRoutes|systemSoc|remainEnergy|chargeStatus|batteryPower|powerPacks|gridFamLoadP|gridFamLoadPOrigin|epsLoadPower|familyTotalP|devMode|pvSystemP|engineOutPutP';

  /// 合并物模型信息
  @override
  EpcEmsDeviceModel mergePhysicalModel(Map<String, dynamic> json) {
    final params = json['propertiesObj'] as Map<String, dynamic>;

    final workStatus =
        $enumDecodeNullable(_$DeviceWorkStatusEnumMap, params['workStatus']);
    this.workStatus = workStatus ?? this.workStatus;
    gridPower = toKw(params['gridPower']) ?? gridPower;

    pvTotalPower = toKw(params['pvPower']) ?? pvTotalPower;

    engineOutPutP = toKw(params['engineOutPutP']) ?? engineOutPutP;

    gridStatus =
        $enumDecodeNullable(_$GridStatusEnumMap, params['gridConnStatus']) ??
            gridStatus;
    workMode =
        $enumDecodeNullable(_$EpcWorkModeTypeEnumMap, params['workMode']) ??
            workMode;
    pvRoutes = params['pvRoutes'] as int? ?? pvRoutes;
    soc = params['systemSoc'] == null ? soc : params['systemSoc'] ~/ 10;
    remainEnergy = toKw(params['remainEnergy']) ?? remainEnergy;
    chargeStatus =
        $enumDecodeNullable(_$EpcChangeStatusEnumMap, params['chargeStatus']) ??
            chargeStatus;
    batteryPower = toKw(params['batteryPower']) ?? batteryPower;

    batteryCount = params['powerPacks'] as int? ?? batteryCount;
    nonBackupLoadPower = f(params['gridFamLoadP']) ?? nonBackupLoadPower;
    nonBackupLoadPowerSource = $enumDecodeNullable(
            _$NonBackupLoadPowerSourceTypeEnumMap,
            params['gridFamLoadPOrigin']) ??
        nonBackupLoadPowerSource;
    backupLoadPower = f(params['epsLoadPower']) ?? backupLoadPower;

    homeLoadPower = toKw(params['familyTotalP']) ?? homeLoadPower;

    systemModelType = $enumDecodeNullable(
            _$EpcSystemModelTypeEnumMap, params['devMode'],
            unknownValue: systemModelType) ??
        systemModelType;

    pvSystemPower = toKw(params['pvSystemP']) ?? pvSystemPower;

    return this;
  }

  EpcEmsDeviceModel copyOfflineModel(EpcHubDeviceModel? hub) {
    return EpcEmsDeviceModel(
      id: id,
      name: name,
      deviceNo: deviceNo,
      modelKey: modelKey,
      productKey: productKey,
      status: status,
      systemModelType:
          hub != null ? EpcSystemModelType.hub : EpcSystemModelType.hvBox,
      indexNo: indexNo,
      pvTotalPower: null,
      pvRoutes: pvRoutes,
      pvSystemPower: null,
      gridPower: null,
      gridStatus: null,
      workStatus: null,
      soc: null,
      remainEnergy: null,
      chargeStatus: null,
      batteryPower: null,
      batteryCount: batteryCount,
      nonBackupLoadPower: null,
      nonBackupLoadPowerSource: NonBackupLoadPowerSourceType.acGrid,
      backupLoadPower: null,
      evChargerPower: null,
      homeLoadPower: null,
      workMode: null,
      engineOutPutP: null,
      evChargerStatus: null,
      evChargerCurrent: null,
      evChargerVoltage: null,
      evChargerCapacity: null,
    );
  }
}

/// 开关机状态   1关机 2开机
enum OnOffStatus {
  /// 关机
  off('1'),

  /// 开机
  on('2');

  final String value;
  const OnOffStatus(this.value);
}

///EMS 工作状态 0：正常 1：等待 2：告警 3：故障 4：待机,5:低功耗
enum DeviceWorkStatus {
  /// 正常
  @JsonValue(0)
  normal(0),

  /// 等待
  @JsonValue(1)
  wait(1),

  /// 告警
  @JsonValue(2)
  alarm(2),

  /// 故障
  @JsonValue(3)
  fault(3),

  /// 待机
  @JsonValue(4)
  standby(4),

  /// 离线
  @JsonValue(-1)
  offline(-1),

  /// 低功耗
  @JsonValue(5)
  eco(5),

  /// 锁定
  @JsonValue(6)
  lock(6);

  final int? value;
  const DeviceWorkStatus(this.value);
  static fromInt(int? value) {
    switch (value) {
      case 0:
        return DeviceWorkStatus.normal;
      case 1:
        return DeviceWorkStatus.wait;
      case 2:
        return DeviceWorkStatus.alarm;
      case 3:
        return DeviceWorkStatus.fault;
      case 4:
        return DeviceWorkStatus.standby;
      case 5:
        return DeviceWorkStatus.eco;
      case -1:
        return DeviceWorkStatus.offline;
      default:
        return DeviceWorkStatus.normal;
    }
  }

  /// 获取枚举值对应的描述
  @override
  toString() => 'tsl.HB-EMS-MODEL.properties.workStatus.value.$value';
}

enum GridStatus {
  /// 离网
  @JsonValue(0)
  offGrid,

  /// 电网并网
  @JsonValue(1)
  onGrid,

  /// 柴发并网
  @JsonValue(2)
  dieselOnGrid,
}

/// EPC充放电状态 0空闲、1充电、2放电 3、待机
enum EpcChangeStatus {
  /// 空闲
  @JsonValue(0)
  idle,

  /// 充电
  @JsonValue(1)
  charge,

  /// 放电
  @JsonValue(2)
  discharge,

  /// 待机
  @JsonValue(3)
  standby,
}

/// 电网侧家庭负载功率来源类型
enum NonBackupLoadPowerSourceType {
  /// 备用电源
  @JsonValue(0)
  acGrid,

  /// 电网
  @JsonValue(1)
  grid,
}

/// 系统型号 1：HvBox；2：SmartPanel；3：Diy
enum EpcSystemModelType {
  /// HvBox
  @JsonValue(1)
  hvBox,

  /// SmartPanel 也叫hub
  @JsonValue(2)
  hub,

  /// Diy
  @JsonValue(3)
  diy,
}

/// 定义枚举来表示电源模式
enum EpcWorkModeType {
  /// 无效值
  @JsonValue(0)
  invalid(0),

  /// 自发自用
  @JsonValue(2)
  selfUse(2),

  /// 备电模式
  @JsonValue(3)
  backup(3),

  /// 分时用电
  @JsonValue(5)
  timeOfUse(5),

  /// 馈网优先
  @JsonValue(6)
  feedInPriority(6);

  final int value;
  const EpcWorkModeType(this.value);

  /// 获取枚举值对应的描述
  @override
  toString() => switch (this) {
        EpcWorkModeType.selfUse => 'installer.self_consumption',
        EpcWorkModeType.backup => 'installer.battery_priority',
        EpcWorkModeType.timeOfUse => 'installer.time_of_use',
        EpcWorkModeType.feedInPriority => 'installer.feeder_in_priority',
        EpcWorkModeType.invalid => 'dictionary.systemRunModel.0',
      };

  static parse(int value) => EpcWorkModeType.values
      .firstWhere((element) => element.value == value, orElse: () => invalid);

  static List<EpcWorkModeType> get list => [
        EpcWorkModeType.selfUse,
        EpcWorkModeType.backup,
        EpcWorkModeType.timeOfUse,
        EpcWorkModeType.feedInPriority,
      ];
}
