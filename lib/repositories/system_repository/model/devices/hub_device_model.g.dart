// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hub_device_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EpcHubDeviceModel _$EpcHubDeviceModelFromJson(Map<String, dynamic> json) =>
    EpcHubDeviceModel(
      id: json['id'] as String,
      name: json['name'] as String,
      deviceNo: json['deviceNo'] as String,
      modelKey: json['modelKey'] as String,
      productKey: json['productKey'] as String,
      status: $enumDecode(_$DeviceStatusEnumMap, json['status']),
      indexNo: json['indexNo'] as int?,
      accessControlStatus: $enumDecodeNullable(
              _$AccessControlStatusEnumMap, json['accessControlStatus']) ??
          AccessControlStatus.close,
      smokeStatus:
          $enumDecodeNullable(_$SensorStatusEnumMap, json['smokeStatus']) ??
              SensorStatus.normal,
      immerStatus:
          $enumDecodeNullable(_$SensorStatusEnumMap, json['immerStatus']) ??
              SensorStatus.normal,
    )..evChargePower = (json['evChargePower'] as num?)?.toDouble();

Map<String, dynamic> _$EpcHubDeviceModelToJson(EpcHubDeviceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'deviceNo': instance.deviceNo,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'status': _$DeviceStatusEnumMap[instance.status]!,
      'indexNo': instance.indexNo,
      'accessControlStatus':
          _$AccessControlStatusEnumMap[instance.accessControlStatus]!,
      'smokeStatus': _$SensorStatusEnumMap[instance.smokeStatus]!,
      'immerStatus': _$SensorStatusEnumMap[instance.immerStatus]!,
      'evChargePower': instance.evChargePower,
    };

const _$DeviceStatusEnumMap = {
  DeviceStatus.notActive: 0,
  DeviceStatus.online: 1,
  DeviceStatus.offline: 2,
};

const _$AccessControlStatusEnumMap = {
  AccessControlStatus.close: 0,
  AccessControlStatus.open: 1,
};

const _$SensorStatusEnumMap = {
  SensorStatus.normal: 0,
  SensorStatus.alarm: 1,
  SensorStatus.notInstall: -1,
};
