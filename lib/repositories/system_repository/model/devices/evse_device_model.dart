import 'package:flutter_basic/repositories/system_repository/model/device_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'evse_device_model.g.dart';

// 交流充电桩召唤属性
const String evseMqttParams =
    'exSnCode|exHardVer|exSoftVer|exWorkMode|exAppChaDisSta|exChargeVolOutputVal|exChargeCurrOutputVal|exCurChargeTime|exCurChargeAmount|exChargStatus|exConnSta';
// 直流充电桩召唤属性
const String dcEvseMqttParams =
    'SnCode|hardVer|softVer|workMode|appChaDisSta|chargeVolOutputVal|chargeCurrOutputVal|curChargeTime|curChargeAmount|chargStatus|connSta';

@JsonSerializable()
class EpcEvseDeviceModel extends DeviceModel {
  /// 硬件版本
  String? hardwareVersion;

  /// 软件版本
  String? softwareVersion;

  /// SN码
  String? snCode;

  /// 工作模式 0:即插即充 1:APP手动充 2:即插即放 3:APP手动放
  EvseWorkMode? workMode;

  /// APP充放电状态 0:停止充放电 1:APP手动充放电
  EvseAppChargeStatus? appChargeStatus;

  /// 充电桩类型  0否 1:交流充电桩 2:直流充电桩
  EvseChargerType? evseType;

  /// 充电电压输出值 (V)
  double? chargeVoltage;

  /// 充电电流输出值 (A)
  double? chargeCurrent;

  /// 当前充电时长 (min)
  int? chargeTime;

  /// 当前充电电量 (Wh)
  double? chargeAmount;

  /// 充电状态
  EvseChargeStatus? chargeStatus;

  /// 通讯状态 0:通讯正常 1:通讯异常
  EvseConnectionStatus? connectionStatus;

  /// 充电功率
  /// 充电功率 = 充电电压 * 充电电流
  double? get chargePower => chargeVoltage != null && chargeCurrent != null
      ? toKw(chargeVoltage! * chargeCurrent!)
      : null;
  EpcEvseDeviceModel({
    required super.id,
    required super.name,
    required super.deviceNo,
    required super.modelKey,
    required super.productKey,
    required super.status,
    super.indexNo,
    this.hardwareVersion,
    this.snCode,
    this.workMode,
    this.appChargeStatus,
    this.evseType,
    this.chargeVoltage,
    this.chargeCurrent,
    this.chargeTime,
    this.chargeAmount,
    this.chargeStatus,
    this.connectionStatus,
  });

  factory EpcEvseDeviceModel.fromJson(Map<String, dynamic> json) =>
      _$EpcEvseDeviceModelFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$EpcEvseDeviceModelToJson(this);

  @override
  String get mqttParams {
    return evseType == EvseChargerType.ac ? evseMqttParams : dcEvseMqttParams;
  }

  EpcEvseDeviceModel copyOfflineModel() {
    return EpcEvseDeviceModel(
      id: id,
      name: name,
      deviceNo: deviceNo,
      modelKey: modelKey,
      productKey: productKey,
      status: status,
      indexNo: indexNo,
      evseType: evseType,
    );
  }

  double get chargeAmountDisplay => (chargeAmount ?? 0) / 10;

  double get chargeVoltageDisplay => (chargeVoltage ?? 0) / 10;

  double get chargeCurrentDisplay => (chargeCurrent ?? 0) / 10;

  EpcEvseDeviceModel clone() {
    return EpcEvseDeviceModel.fromJson(toJson());
  }

  @override
  EpcEvseDeviceModel mergePhysicalModel(Map<String, dynamic> json) {
    final params = json['propertiesObj'] as Map<String, dynamic>;
    if (evseType == EvseChargerType.ac) {
      hardwareVersion = params['exHardVer'] as String?;
      softwareVersion = params['exSoftVer'] as String?;
      snCode = params['exSnCode'] as String?;
      workMode =
          $enumDecodeNullable(_$EvseWorkModeEnumMap, params['exWorkMode']);
      appChargeStatus = $enumDecodeNullable(
          _$EvseAppChargeStatusEnumMap, params['exAppChaDisSta']);
      chargeVoltage =
          double.tryParse(params['exChargeVolOutputVal']?.toString() ?? '');
      chargeCurrent =
          double.tryParse(params['exChargeCurrOutputVal']?.toString() ?? '');
      chargeVoltage = (chargeVoltage ?? 0) / 10;
      chargeCurrent = (chargeCurrent ?? 0) / 100;
      chargeTime = int.tryParse(params['exCurChargeTime']?.toString() ?? '');
      chargeAmount =
          double.tryParse(params['exCurChargeAmount']?.toString() ?? '');
      chargeAmount = (chargeAmount ?? 0) / 100;
      chargeStatus = $enumDecodeNullable(
          _$EvseChargeStatusEnumMap, params['exChargStatus']);
      connectionStatus = $enumDecodeNullable(
          _$EvseConnectionStatusEnumMap, params['exConnSta']);
    } else {
      hardwareVersion = params['hardVer'] as String?;
      softwareVersion = params['softVer'] as String?;
      snCode = params['snCode'] as String?;
      workMode = $enumDecodeNullable(_$EvseWorkModeEnumMap, params['workMode']);
      appChargeStatus = $enumDecodeNullable(
          _$EvseAppChargeStatusEnumMap, params['appChaDisSta']);
      chargeVoltage =
          double.tryParse(params['chargeVolOutputVal']?.toString() ?? '');
      chargeCurrent =
          double.tryParse(params['chargeCurrOutputVal']?.toString() ?? '');
      chargeVoltage =
          double.tryParse(params['chargeVolOutputVal']?.toString() ?? '');
      // 除以10 四舍五入，保留一位小数
      chargeVoltage = (chargeVoltage ?? 0) / 10;
      chargeCurrent =
          double.tryParse(params['chargeCurrOutputVal']?.toString() ?? '');
      chargeTime = int.tryParse(params['curChargeTime']?.toString() ?? '');
      chargeAmount =
          double.tryParse(params['curChargeAmount']?.toString() ?? '');
      chargeAmount = (chargeAmount ?? 0) / 10;
      chargeStatus =
          $enumDecodeNullable(_$EvseChargeStatusEnumMap, params['chargStatus']);
      connectionStatus =
          $enumDecodeNullable(_$EvseConnectionStatusEnumMap, params['connSta']);
    }

    return this;
  }
}

/// 充电桩工作模式
enum EvseWorkMode {
  /// 即插即充
  @JsonValue(0)
  plugAndCharge,

  /// APP手动充
  @JsonValue(1)
  appManualCharge,

  /// 即插即放
  @JsonValue(2)
  plugAndDischarge,

  /// APP手动放
  @JsonValue(3)
  appManualDischarge,
}

/// APP充放电状态
enum EvseAppChargeStatus {
  /// 停止充放电
  @JsonValue(0)
  stop,

  /// APP手动充放电
  @JsonValue(1)
  manualCharging,
}

/// 充电桩类型
enum EvseChargerType {
  /// 未配置
  @JsonValue(0)
  none,

  /// 直流充电
  @JsonValue(1)
  dc,

  /// 交流充电
  @JsonValue(2)
  ac,
}

/// 充电状态
enum EvseChargeStatus {
  /// 待机未插枪
  @JsonValue(0)
  standbyUnplugged,

  /// 插枪未充电
  @JsonValue(1)
  pluggedNotCharging,

  /// 插枪充电
  @JsonValue(2)
  pluggedCharging,

  /// 插枪充电中
  @JsonValue(3)
  charging,

  /// 停止充电
  @JsonValue(4)
  stopped,

  /// 充电中拔枪返回0待机
  @JsonValue(5)
  unpluggedToStandby,

  /// 充电枪故障
  @JsonValue(6)
  fault,
}

/// 通讯状态
enum EvseConnectionStatus {
  /// 通讯正常
  @JsonValue(0)
  normal,

  /// 通讯异常
  @JsonValue(1)
  error,
}
