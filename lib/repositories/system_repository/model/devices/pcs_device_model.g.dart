// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pcs_device_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EpcPcsDeviceModel _$EpcPcsDeviceModelFromJson(Map<String, dynamic> json) =>
    EpcPcsDeviceModel(
      id: json['id'] as String,
      name: json['name'] as String,
      deviceNo: json['deviceNo'] as String,
      modelKey: json['modelKey'] as String,
      productKey: json['productKey'] as String,
      status: $enumDecode(_$DeviceStatusEnumMap, json['status']),
      indexNo: json['indexNo'] as int?,
      pvPowerList: (json['pvPowerList'] as List<dynamic>?)
              ?.map((e) => (e as num?)?.toDouble())
              .toList() ??
          const [],
      totalPower: (json['totalPower'] as num?)?.toDouble(),
      connSta: json['connSta'] as int?,
    );

Map<String, dynamic> _$EpcPcsDeviceModelToJson(EpcPcsDeviceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'deviceNo': instance.deviceNo,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'status': _$DeviceStatusEnumMap[instance.status]!,
      'indexNo': instance.indexNo,
      'pvPowerList': instance.pvPowerList,
      'totalPower': instance.totalPower,
      'connSta': instance.connSta,
    };

const _$DeviceStatusEnumMap = {
  DeviceStatus.notActive: 0,
  DeviceStatus.online: 1,
  DeviceStatus.offline: 2,
};
