import 'package:flutter_basic/repositories/system_repository/model/device_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'bms_device_model.g.dart';

@JsonSerializable()
class EpcBmsDeviceModel extends DeviceModel {
  int? soc;

  EpcBmsDeviceModel({
    required super.id,
    required super.name,
    required super.deviceNo,
    required super.modelKey,
    required super.productKey,
    required super.status,
    super.indexNo,
    this.soc,
  });

  factory EpcBmsDeviceModel.fromJson(Map<String, dynamic> json) =>
      _$EpcBmsDeviceModelFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$EpcBmsDeviceModelToJson(this);

  @override
  String get mqttParams => 'soc';

  @override
  EpcBmsDeviceModel mergePhysicalModel(Map<String, dynamic> json) {
    final params = json['propertiesObj'] as Map<String, dynamic>;
    soc = params['soc'] ~/ 10;
    return this;
  }
}
