// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ems_device_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EpcEmsDeviceModel _$EpcEmsDeviceModelFromJson(Map<String, dynamic> json) =>
    EpcEmsDeviceModel(
      id: json['id'] as String,
      name: json['name'] as String,
      deviceNo: json['deviceNo'] as String,
      modelKey: json['modelKey'] as String,
      productKey: json['productKey'] as String,
      status: $enumDecode(_$DeviceStatusEnumMap, json['status']),
      indexNo: json['indexNo'] as int?,
      pvTotalPower: (json['pvTotalPower'] as num?)?.toDouble(),
      pvRoutes: json['pvRoutes'] as int? ?? 0,
      gridPower: (json['gridPower'] as num?)?.toDouble(),
      gridStatus: $enumDecodeNullable(_$GridStatusEnumMap, json['gridStatus']),
      workStatus:
          $enumDecodeNullable(_$DeviceWorkStatusEnumMap, json['workStatus']),
      soc: json['soc'] as int?,
      remainEnergy: (json['remainEnergy'] as num?)?.toDouble(),
      chargeStatus:
          $enumDecodeNullable(_$EpcChangeStatusEnumMap, json['chargeStatus']),
      nonBackupLoadPower: json['nonBackupLoadPower'] as int?,
      backupLoadPower: json['backupLoadPower'] as int?,
      evChargerPower: json['evChargerPower'] as int?,
      nonBackupLoadPowerSource: $enumDecodeNullable(
              _$NonBackupLoadPowerSourceTypeEnumMap,
              json['nonBackupLoadPowerSource']) ??
          NonBackupLoadPowerSourceType.acGrid,
      systemModelType: $enumDecodeNullable(
              _$EpcSystemModelTypeEnumMap, json['systemModelType']) ??
          EpcSystemModelType.hub,
      homeLoadPower: (json['homeLoadPower'] as num?)?.toDouble(),
      batteryPower: (json['batteryPower'] as num?)?.toDouble() ?? 0,
      batteryCount: json['batteryCount'] as int? ?? 1,
      workMode: $enumDecodeNullable(_$EpcWorkModeTypeEnumMap, json['workMode']),
      pvSystemPower: (json['pvSystemPower'] as num?)?.toDouble(),
      engineOutPutP: (json['engineOutPutP'] as num?)?.toDouble(),
      evChargerStatus: json['evChargerStatus'] as int?,
      evChargerCurrent: (json['evChargerCurrent'] as num?)?.toDouble(),
      evChargerVoltage: (json['evChargerVoltage'] as num?)?.toDouble(),
      evChargerCapacity: (json['evChargerCapacity'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$EpcEmsDeviceModelToJson(EpcEmsDeviceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'deviceNo': instance.deviceNo,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'status': _$DeviceStatusEnumMap[instance.status]!,
      'indexNo': instance.indexNo,
      'systemModelType': _$EpcSystemModelTypeEnumMap[instance.systemModelType]!,
      'pvTotalPower': instance.pvTotalPower,
      'pvRoutes': instance.pvRoutes,
      'pvSystemPower': instance.pvSystemPower,
      'gridPower': instance.gridPower,
      'gridStatus': _$GridStatusEnumMap[instance.gridStatus],
      'workStatus': _$DeviceWorkStatusEnumMap[instance.workStatus],
      'soc': instance.soc,
      'remainEnergy': instance.remainEnergy,
      'chargeStatus': _$EpcChangeStatusEnumMap[instance.chargeStatus],
      'batteryPower': instance.batteryPower,
      'batteryCount': instance.batteryCount,
      'nonBackupLoadPower': instance.nonBackupLoadPower,
      'nonBackupLoadPowerSource': _$NonBackupLoadPowerSourceTypeEnumMap[
          instance.nonBackupLoadPowerSource]!,
      'backupLoadPower': instance.backupLoadPower,
      'evChargerPower': instance.evChargerPower,
      'evChargerStatus': instance.evChargerStatus,
      'evChargerCurrent': instance.evChargerCurrent,
      'evChargerVoltage': instance.evChargerVoltage,
      'evChargerCapacity': instance.evChargerCapacity,
      'homeLoadPower': instance.homeLoadPower,
      'engineOutPutP': instance.engineOutPutP,
      'workMode': _$EpcWorkModeTypeEnumMap[instance.workMode],
    };

const _$DeviceStatusEnumMap = {
  DeviceStatus.notActive: 0,
  DeviceStatus.online: 1,
  DeviceStatus.offline: 2,
};

const _$GridStatusEnumMap = {
  GridStatus.offGrid: 0,
  GridStatus.onGrid: 1,
  GridStatus.dieselOnGrid: 2,
};

const _$DeviceWorkStatusEnumMap = {
  DeviceWorkStatus.normal: 0,
  DeviceWorkStatus.wait: 1,
  DeviceWorkStatus.alarm: 2,
  DeviceWorkStatus.fault: 3,
  DeviceWorkStatus.standby: 4,
  DeviceWorkStatus.offline: -1,
  DeviceWorkStatus.eco: 5,
  DeviceWorkStatus.lock: 6,
};

const _$EpcChangeStatusEnumMap = {
  EpcChangeStatus.idle: 0,
  EpcChangeStatus.charge: 1,
  EpcChangeStatus.discharge: 2,
  EpcChangeStatus.standby: 3,
};

const _$NonBackupLoadPowerSourceTypeEnumMap = {
  NonBackupLoadPowerSourceType.acGrid: 0,
  NonBackupLoadPowerSourceType.grid: 1,
};

const _$EpcSystemModelTypeEnumMap = {
  EpcSystemModelType.hvBox: 1,
  EpcSystemModelType.hub: 2,
  EpcSystemModelType.diy: 3,
};

const _$EpcWorkModeTypeEnumMap = {
  EpcWorkModeType.invalid: 0,
  EpcWorkModeType.selfUse: 2,
  EpcWorkModeType.backup: 3,
  EpcWorkModeType.timeOfUse: 5,
  EpcWorkModeType.feedInPriority: 6,
};
