import 'package:flutter_basic/platform/utils/log_utils.dart';
import 'package:flutter_basic/repositories/custom_exception/ServiceException.dart';
import 'package:flutter_basic/repositories/trend_repository/model/cluster/trend_cluster_charge_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/cluster/trend_cluster_grid_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/cluster/trend_cluster_out_put_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/cluster/trend_cluster_pv_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/cluster/trend_cluster_soc_temp_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/cluster/trend_cluster_sta_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/epc/trend_battery_charge_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/epc/trend_pv_charge_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/epc/trend_pv_power_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/trend_bms_model/trend_bms_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/trend_line_model/trend_line_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/trend_out_put_model/trend_out_put_model.dart';
import 'package:flutter_basic/repositories/trend_repository/model/trend_pv_model/trend_pv_model.dart';

import '../../generated/l10n.dart';
import '../api/api.dart';
import 'model/epc/trend_grid_charge_model.dart';
import 'model/epc/trend_load_charge_model.dart';
import 'model/epc/trend_load_power_model.dart';
import 'model/trend_grid_model/trend_grid_model.dart';
import 'model/trend_soc_temp_model/trend_soc_model.dart';
import 'model/trend_sta_model/trend_sta_model.dart';

class TrendClientRepository {
  static final TrendClientRepository instance =
      TrendClientRepository._internal();

  TrendClientRepository._internal();

  /// 并机获取充电曲线
  Future<TrendClusterChargeModel> getTrendClusterCharge(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendClusterCharge, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendClusterChargeModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  /// 获取并机各种电量趋势柱状图/统计
  Future<TrendClusterStaModel> getTrendClusterSta(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendClusterSta, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendClusterStaModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  /// 获取并机 并机pv功率曲线
  Future<TrendClusterPvModel> getTrendClusterPvPower(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendClusterPvPower, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendClusterPvModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  /// 获取 并机Output功率
  Future<TrendClusterOutPutModel> getTrendClusterOutPut(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendClusterOutputPower, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendClusterOutPutModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  /// 获取并机on-grid功率
  Future<TrendClusterGridModel> getTrendClusterGridPower(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendClusterGridPower, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendClusterGridModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  ///startTime endTime 起始时间, 天：yyyyMMdd 周：yyyyMMdd, 月：yyyyMM, 年：yyyy
  ///type 时间格式选择 2:天,3:周,4:月,5:年
  Future<List<TrendLineModel>> getTrendCharge(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendChargeV2, body: params);
    if (baseResult.success && baseResult.result != null) {
      return [
        TrendLineModel.fromJson(
            {"trendLines": baseResult.result["chargeTrend"]}),
      ];
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendLineModel> getTrendGridPower(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendGridPower, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendLineModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendLineModel> getTrendLoadPower(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendLoadPower, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendLineModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendPvModel> getTrendPvPower(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendPvPower, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendPvModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendOutPutModel> getTrendOutPut(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendOutPut, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendOutPutModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendGridModel> getTrendGrid(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendGrid, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendGridModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendBmsModel> getTrendBms(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendBms, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendBmsModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendSocModel> getTrendSocTemp(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendSocTemp, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendSocModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendStaModel> getTrendSta(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendSta, body: params);
    logger.i(baseResult.success && baseResult.result != null);
    if (baseResult.success && baseResult.result != null) {
      return TrendStaModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendChargeModel> getTrendEpcBatteryCharge(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendEpcBmChargeDischarge, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendChargeModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendLineModel> getTrendEpcBatteryPower(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendEpcBmPower, body: params);
    logger.d(baseResult.success && baseResult.result != null);
    if (baseResult.success && baseResult.result != null) {
      return TrendLineModel.fromJson({"trendLines": baseResult.result});
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendLineModel> getTrendEpcBatterySoc(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendEpcBmSoc, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendLineModel.fromJson({"trendLines": baseResult.result});
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendLoadChargeModel> getTrendEpcLoadCharge(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendEpcLoadGen, body: params);
    logger.d(baseResult.success && baseResult.result != null);
    if (baseResult.success && baseResult.result != null) {
      return TrendLoadChargeModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendLoadPowerModel> getTrendEpcLoadPower(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendEpcLoadPower, body: params);
    logger.d(baseResult.success && baseResult.result != null);
    if (baseResult.success && baseResult.result != null) {
      return TrendLoadPowerModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendGridChargeModel> getTrendEpcGridCharge(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendEpcPowerGridGen, body: params);
    logger.d(baseResult.success && baseResult.result != null);
    if (baseResult.success && baseResult.result != null) {
      return TrendGridChargeModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendLineModel> getTrendEpcGridPower(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendEpcPowerGridPower, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendLineModel.fromJson({"trendLines": baseResult.result});
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendPvChargeModel> getTrendEpcPvCharge(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendEpcPvCharge, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendPvChargeModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendPvPowerModel> getTrendEpcPvPower(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendEpcPvPower, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendPvPowerModel.fromJson({"pvPower": baseResult.result});
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendClusterSocTempModel> getTrendClusterSocTemp({
    required String systemId,
    required String startTime,
    required String endTime,
    required String type,
  }) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendClusterSoc, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendClusterSocTempModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendChargeModel> getTrendEpcEvChargeData(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendEpcEvCharge, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendChargeModel.fromJson(baseResult.result);
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }

  Future<TrendLineModel> getTrendEpcEvChargerPower(
      {required String systemId,
      required String startTime,
      required String endTime,
      required String type}) async {
    var params = {"startTime": startTime, "endTime": endTime, "type": type};
    if (systemId.isNotEmpty) {
      params["systemId"] = systemId;
    }
    final baseResult = await HttpManager.instance
        .post(TrendEndPoint.getTrendEpcEvChargePower, body: params);
    if (baseResult.success && baseResult.result != null) {
      return TrendLineModel.fromJson({"trendLines": baseResult.result});
    }
    throw ServiceException(
        code: baseResult.code, msg: S.current.text(baseResult.key));
  }
}
