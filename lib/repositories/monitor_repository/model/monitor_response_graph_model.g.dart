// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'monitor_response_graph_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnergyFlowChartVo _$EnergyFlowChartVoFromJson(Map<String, dynamic> json) =>
    EnergyFlowChartVo(
      emsGwVo: json['emsGwVO'] == null
          ? null
          : EmsGwVo.fromJson(json['emsGwVO'] as Map<String, dynamic>),
      toPvDirection: json['toPvDirection'] as int?,
      pvInfo: json['pvInfo'] == null
          ? null
          : PvInfo.fromJson(json['pvInfo'] as Map<String, dynamic>),
      toAcOutputDirection: json['toAcOutputDirection'] as int?,
      acInfo: json['acInfo'] == null
          ? null
          : AcInfo.fromJson(json['acInfo'] as Map<String, dynamic>),
      toDcDirection: json['toDcDirection'] as int?,
      dcInfo: json['dcInfo'] == null
          ? null
          : DcInfo.fromJson(json['dcInfo'] as Map<String, dynamic>),
      toPlugDirection: json['toPlugDirection'] as int?,
      plugInfo: json['plugInfo'] == null
          ? null
          : PlugInfo.fromJson(json['plugInfo'] as Map<String, dynamic>),
      toAcMainDirection: json['toAcMainDirection'] as int?,
      acMainVo: json['acMainVO'] == null
          ? null
          : AcMainVo.fromJson(json['acMainVO'] as Map<String, dynamic>),
      toGridDirection: json['toGridDirection'] as int?,
      gridVo: json['gridVO'] == null
          ? null
          : GridVo.fromJson(json['gridVO'] as Map<String, dynamic>),
      toOtherLoadDirection: json['toOtherLoadDirection'] as int?,
      otherLoadVo: json['otherLoadVO'] == null
          ? null
          : OtherLoadVo.fromJson(json['otherLoadVO'] as Map<String, dynamic>),
      ctDevice: json['energyFlowCTVO'] == null
          ? null
          : EnergyFlowCTVO.fromJson(
              json['energyFlowCTVO'] as Map<String, dynamic>),
      ctValue: json['ctValue'] as String? ?? '0',
    )
      ..enableConfig = json['enableConfig'] as int?
      ..onGridPower = (json['onGridPower'] as num?)?.toDouble()
      ..toAcOnGridDirection = json['toAcOnGridDirection'] as int?
      ..combineCtValue = json['combineCtValue'] as String?
      ..dcs = (json['dcs'] as List<dynamic>?)
          ?.map((e) => DCPropertyModel.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$EnergyFlowChartVoToJson(EnergyFlowChartVo instance) =>
    <String, dynamic>{
      'enableConfig': instance.enableConfig,
      'emsGwVO': instance.emsGwVo,
      'toPvDirection': instance.toPvDirection,
      'onGridPower': instance.onGridPower,
      'pvInfo': instance.pvInfo,
      'toAcOutputDirection': instance.toAcOutputDirection,
      'acInfo': instance.acInfo,
      'toDcDirection': instance.toDcDirection,
      'dcInfo': instance.dcInfo,
      'toPlugDirection': instance.toPlugDirection,
      'plugInfo': instance.plugInfo,
      'toAcMainDirection': instance.toAcMainDirection,
      'acMainVO': instance.acMainVo,
      'toGridDirection': instance.toGridDirection,
      'gridVO': instance.gridVo,
      'toOtherLoadDirection': instance.toOtherLoadDirection,
      'otherLoadVO': instance.otherLoadVo,
      'toAcOnGridDirection': instance.toAcOnGridDirection,
      'combineCtValue': instance.combineCtValue,
      'ctValue': instance.ctValue,
      'energyFlowCTVO': instance.ctDevice,
      'dcs': instance.dcs,
    };

AcInfo _$AcInfoFromJson(Map<String, dynamic> json) => AcInfo(
      acOutEnable: json['acOutEnable'] as int?,
      epsLoadPower: (json['epsLoadPower'] as num?)?.toDouble(),
      combinePower: (json['combinePower'] as num?)?.toDouble(),
      combineSwitch: json['combineSwitch'] as int?,
      deviceNo: json['deviceNo'] as String?,
      deviceName: json['deviceName'] as String?,
      modelKey: json['modelKey'] as String?,
      productKey: json['productKey'] as String?,
      acOutEnableIdentify: json['acOutEnableIdentify'] as String?,
      slaverList: (json['slaverList'] as List<dynamic>?)
          ?.map((e) => AcInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AcInfoToJson(AcInfo instance) => <String, dynamic>{
      'combineSwitch': instance.combineSwitch,
      'acOutEnable': instance.acOutEnable,
      'epsLoadPower': instance.epsLoadPower,
      'combinePower': instance.combinePower,
      'deviceNo': instance.deviceNo,
      'deviceName': instance.deviceName,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'acOutEnableIdentify': instance.acOutEnableIdentify,
      'slaverList': instance.slaverList,
    };

AcMainVo _$AcMainVoFromJson(Map<String, dynamic> json) => AcMainVo(
      acMainPower: (json['acMainPower'] as num?)?.toDouble(),
    )..deviceStatus = json['deviceStatus'] as int?;

Map<String, dynamic> _$AcMainVoToJson(AcMainVo instance) => <String, dynamic>{
      'acMainPower': instance.acMainPower,
      'deviceStatus': instance.deviceStatus,
    };

DcInfo _$DcInfoFromJson(Map<String, dynamic> json) => DcInfo(
      dcOutEnable: json['dcOutEnable'] as int?,
      dcOutPower: (json['dcOutPower'] as num?)?.toDouble(),
      deviceNo: json['deviceNo'] as String?,
      deviceName: json['deviceName'] as String?,
      typeC1Power: (json['typeC1Power'] as num?)?.toDouble(),
      typeC1Switch: json['typeC1Switch'] as int?,
      typeC2Power: (json['typeC2Power'] as num?)?.toDouble(),
      typeC2Switch: json['typeC2Switch'] as int?,
      usbSwitchPower: (json['usbSwitchPower'] as num?)?.toDouble(),
      usbaswitch: json['usbaswitch'] as int?,
      dcLoadSwitchIdentifier: json['dcLoadSwitchIdentifier'] as String?,
      typeC1SwitchIdentify: json['typeC1SwitchIdentify'] as String?,
      typeC2SwitchIdentify: json['typeC2SwitchIdentify'] as String?,
      usbaSwitchIdentify: json['usbaSwitchIdentify'] as String?,
      modelKey: json['modelKey'] as String?,
      productKey: json['productKey'] as String?,
      dcModelKey: json['dcModelKey'] as String?,
      dcProductKey: json['dcProductKey'] as String?,
      deviceStatus: json['deviceStatus'] as int?,
      dcCommunicationStatus: json['dcStatus'] as int?,
      emsDeviceNo: json['emsDeviceNo'] as String?,
      slaverList: (json['slaverList'] as List<dynamic>?)
          ?.map((e) => DcInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    )..combinePower = (json['combinePower'] as num?)?.toDouble();

Map<String, dynamic> _$DcInfoToJson(DcInfo instance) => <String, dynamic>{
      'combinePower': instance.combinePower,
      'dcOutEnable': instance.dcOutEnable,
      'dcOutPower': instance.dcOutPower,
      'deviceNo': instance.deviceNo,
      'deviceName': instance.deviceName,
      'typeC1Power': instance.typeC1Power,
      'typeC1Switch': instance.typeC1Switch,
      'typeC2Power': instance.typeC2Power,
      'typeC2Switch': instance.typeC2Switch,
      'usbSwitchPower': instance.usbSwitchPower,
      'usbaswitch': instance.usbaswitch,
      'deviceStatus': instance.deviceStatus,
      'dcStatus': instance.dcCommunicationStatus,
      'usbaSwitchIdentify': instance.usbaSwitchIdentify,
      'typeC2SwitchIdentify': instance.typeC2SwitchIdentify,
      'typeC1SwitchIdentify': instance.typeC1SwitchIdentify,
      'dcLoadSwitchIdentifier': instance.dcLoadSwitchIdentifier,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'dcModelKey': instance.dcModelKey,
      'dcProductKey': instance.dcProductKey,
      'emsDeviceNo': instance.emsDeviceNo,
      'slaverList': instance.slaverList,
    };

EmsGwVo _$EmsGwVoFromJson(Map<String, dynamic> json) => EmsGwVo(
      chargeStatus: json['chargeStatus'] as int?,
      energyRemain: (json['energyRemain'] as num?)?.toDouble(),
      powerPackList: (json['powerPackList'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : PowerPackList.fromJson(e as Map<String, dynamic>))
          .toList(),
      powerPacks: json['powerPacks'] as int?,
      soc: (json['soc'] as num?)?.toDouble(),
      workStatusLanguageKey: json['workStatusLanguageKey'] as String?,
      productKey: json['productKey'] as String?,
      modelKey: json['modelKey'] as String?,
      deviceNo: json['deviceNo'] as String?,
      workModeIdentify: json['workModeIdentify'] as String?,
      csq4G: json['csq4G'] as int?,
      csqWifi: json['csqWifi'] as int?,
      netType: json['netType'] as int?,
      gridConnStatus: json['gridConnStatus'] as int?,
      slaverList: (json['slaverList'] as List<dynamic>?)
          ?.map((e) => EmsGwVo.fromJson(e as Map<String, dynamic>))
          .toList(),
    )
      ..gwStatus = json['gwStatus'] as int?
      ..deviceStatus = json['workStatus'] as int?;

Map<String, dynamic> _$EmsGwVoToJson(EmsGwVo instance) => <String, dynamic>{
      'gwStatus': instance.gwStatus,
      'chargeStatus': instance.chargeStatus,
      'energyRemain': instance.energyRemain,
      'powerPackList': instance.powerPackList,
      'powerPacks': instance.powerPacks,
      'soc': instance.soc,
      'workStatus': instance.deviceStatus,
      'workStatusLanguageKey': instance.workStatusLanguageKey,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'deviceNo': instance.deviceNo,
      'workModeIdentify': instance.workModeIdentify,
      'csq4G': instance.csq4G,
      'csqWifi': instance.csqWifi,
      'netType': instance.netType,
      'gridConnStatus': instance.gridConnStatus,
      'slaverList': instance.slaverList,
    };

PowerPackList _$PowerPackListFromJson(Map<String, dynamic> json) =>
    PowerPackList(
      bmsOptStatus: json['bmsOptStatus'] as int?,
      deviceNo: json['deviceNo'] as String?,
      energyRemain: (json['energyRemain'] as num?)?.toDouble(),
      soc: (json['soc'] as num?)?.toDouble(),
      masterDevice: json['masterDevice'] as bool?,
    );

Map<String, dynamic> _$PowerPackListToJson(PowerPackList instance) =>
    <String, dynamic>{
      'bmsOptStatus': instance.bmsOptStatus,
      'deviceNo': instance.deviceNo,
      'energyRemain': instance.energyRemain,
      'soc': instance.soc,
      'masterDevice': instance.masterDevice,
    };

GridVo _$GridVoFromJson(Map<String, dynamic> json) => GridVo(
      gridPower: (json['gridPower'] as num?)?.toDouble(),
      slaverList: (json['slaverList'] as List<dynamic>?)
          ?.map((e) => GridVo.fromJson(e as Map<String, dynamic>))
          .toList(),
    )
      ..combinePower = (json['combinePower'] as num?)?.toDouble()
      ..deviceName = json['deviceName'] as String?
      ..toGridDirection = json['toGridDirection'] as int?;

Map<String, dynamic> _$GridVoToJson(GridVo instance) => <String, dynamic>{
      'combinePower': instance.combinePower,
      'gridPower': instance.gridPower,
      'deviceName': instance.deviceName,
      'slaverList': instance.slaverList,
      'toGridDirection': instance.toGridDirection,
    };

OtherLoadVo _$OtherLoadVoFromJson(Map<String, dynamic> json) => OtherLoadVo(
      otherLoadPower: (json['otherLoadPower'] as num?)?.toDouble(),
      otherLoadIdentify: json['otherLoadIdentify'] as String?,
      otherLoadJson: json['otherLoadJson'] as String?,
      modelKey: json['modelKey'] as String?,
      productKey: json['productKey'] as String?,
      deviceNo: json['deviceNo'] as String?,
      slaverList: (json['slaverList'] as List<dynamic>?)
          ?.map((e) => OtherLoadVo.fromJson(e as Map<String, dynamic>))
          .toList(),
    )
      ..combinePower = (json['combinePower'] as num?)?.toDouble()
      ..deviceName = json['deviceName'] as String?;

Map<String, dynamic> _$OtherLoadVoToJson(OtherLoadVo instance) =>
    <String, dynamic>{
      'combinePower': instance.combinePower,
      'otherLoadPower': instance.otherLoadPower,
      'otherLoadIdentify': instance.otherLoadIdentify,
      'otherLoadJson': instance.otherLoadJson,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'deviceNo': instance.deviceNo,
      'slaverList': instance.slaverList,
      'deviceName': instance.deviceName,
    };

PlugInfo _$PlugInfoFromJson(Map<String, dynamic> json) => PlugInfo(
      plugNum: json['plugNum'] as int?,
      plugPower: (json['plugPower'] as num?)?.toDouble(),
      plugs: (json['plugs'] as List<dynamic>?)
          ?.map((e) => Plug.fromJson(e as Map<String, dynamic>))
          .toList(),
      slaverList: (json['slaverList'] as List<dynamic>?)
          ?.map((e) => PlugInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    )..deviceStatus = json['deviceStatus'] as int?;

Map<String, dynamic> _$PlugInfoToJson(PlugInfo instance) => <String, dynamic>{
      'plugNum': instance.plugNum,
      'plugPower': instance.plugPower,
      'plugs': instance.plugs,
      'deviceStatus': instance.deviceStatus,
      'slaverList': instance.slaverList,
    };

Plug _$PlugFromJson(Map<String, dynamic> json) => Plug(
      deviceName: json['deviceName'] as String?,
      deviceNo: json['deviceNo'] as String?,
      plugPower: (json['plugPower'] as num?)?.toDouble(),
      plugSwitch: json['plugSwitch'] as int?,
      modelKey: json['modelKey'] as String?,
      productKey: json['productKey'] as String?,
      requestStatus: json['requestStatus'] as int?,
      plugSwitchIdentify: json['plugSwitchIdentify'] as String?,
      timers: (json['timers'] as List<dynamic>?)
          ?.map((e) =>
              DevicePlugTimerResponse.fromJson(e as Map<String, dynamic>))
          .toList(),
    )..deviceStatus = json['deviceStatus'] as int?;

Map<String, dynamic> _$PlugToJson(Plug instance) => <String, dynamic>{
      'deviceName': instance.deviceName,
      'deviceNo': instance.deviceNo,
      'deviceStatus': instance.deviceStatus,
      'plugPower': instance.plugPower,
      'plugSwitch': instance.plugSwitch,
      'modelKey': instance.modelKey,
      'productKey': instance.productKey,
      'plugSwitchIdentify': instance.plugSwitchIdentify,
      'requestStatus': instance.requestStatus,
      'timers': instance.timers,
    };

PvInfo _$PvInfoFromJson(Map<String, dynamic> json) => PvInfo(
      combinePower: (json['combinePower'] as num?)?.toDouble(),
      pvPower: (json['pvPower'] as num?)?.toDouble(),
      pvPowerList: (json['pvPowerList'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      deviceNo: json['deviceNo'] as String?,
      deviceName: json['deviceName'] as String?,
      slaverList: (json['slaverList'] as List<dynamic>?)
          ?.map((e) => PvInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PvInfoToJson(PvInfo instance) => <String, dynamic>{
      'combinePower': instance.combinePower,
      'pvPower': instance.pvPower,
      'pvPowerList': instance.pvPowerList,
      'deviceNo': instance.deviceNo,
      'deviceName': instance.deviceName,
      'slaverList': instance.slaverList,
    };

EnergyFlowCTVO _$EnergyFlowCTVOFromJson(Map<String, dynamic> json) =>
    EnergyFlowCTVO(
      ctStatus: $enumDecodeNullable(_$CTStatusTypeEnumMap, json['ctStatus']) ??
          CTStatusType.normal,
      ctDeviceName: json['ctDeviceName'] as String? ?? "",
      deviceNo: json['deviceNo'] as String? ?? "",
      productKey: json['productKey'] as String? ?? "",
    )..slaverList = (json['slaverList'] as List<dynamic>?)
        ?.map((e) => EnergyFlowCTVO.fromJson(e as Map<String, dynamic>))
        .toList();

Map<String, dynamic> _$EnergyFlowCTVOToJson(EnergyFlowCTVO instance) =>
    <String, dynamic>{
      'ctStatus': _$CTStatusTypeEnumMap[instance.ctStatus]!,
      'ctDeviceName': instance.ctDeviceName,
      'productKey': instance.productKey,
      'deviceNo': instance.deviceNo,
      'slaverList': instance.slaverList,
    };

const _$CTStatusTypeEnumMap = {
  CTStatusType.offline: 0,
  CTStatusType.normal: 1,
  CTStatusType.fault: 2,
  CTStatusType.alarm: 3,
};
