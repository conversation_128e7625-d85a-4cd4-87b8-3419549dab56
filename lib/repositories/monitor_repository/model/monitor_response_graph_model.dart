import 'package:flutter_basic/features/monitor/bloc/monitor_fetch_bloc.dart';
import 'package:flutter_basic/features/monitor/bloc/monitor_fetch_event.dart';
import 'package:flutter_basic/generated/l10n.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/platform/utils/sp_utils.dart';
import 'package:flutter_basic/repositories/custom_exception/ServiceException.dart';
import 'package:flutter_basic/repositories/device_repository/model/device_plug_timer_response.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/monitor_quick_control_model.dart';
import 'package:flutter_basic/repositories/system_repository/system_client_repository.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../features/devices/config/device_config.dart';
import '../../../platform/community/monitor/constants.dart';
import '../../../platform/utils/combine.dart';
import '../../../platform/utils/device_status.dart';

part 'monitor_response_graph_model.g.dart';

@JsonSerializable()
class EnergyFlowChartVo {
  // 使能开关
  @JsonKey(name: "enableConfig")
  int? enableConfig;
  @JsonKey(name: "emsGwVO")
  EmsGwVo? emsGwVo;
  @JsonKey(name: "toPvDirection")
  int? toPvDirection;
  // OnGrid 功率
  @JsonKey(name: "onGridPower")
  double? onGridPower;
  displayOnGridPower() {
    return "${onGridPower?.abs().toInt().toString() ?? '-'}w";
  }

  /// Jackery设备到 PV 流向, 0 不流动，1 往外，2往中央设备，-1 没有这个设备
  @JsonKey(name: "pvInfo")
  PvInfo? pvInfo;
  @JsonKey(name: "toAcOutputDirection")
  int? toAcOutputDirection;

  /// Jackery设备到 AC Output, 0 不流动，1 往外，2往中央设备，-1 没有这个设备
  @JsonKey(name: "acInfo")
  AcInfo? acInfo;
  @JsonKey(name: "toDcDirection")
  int? toDcDirection;

  /// Jackery设备到 DC Output, 0 不流动，1 往外，2往中央设备，-1 没有这个设备
  @JsonKey(name: "dcInfo")
  DcInfo? dcInfo;
  @JsonKey(name: "toPlugDirection")
  int? toPlugDirection;

  /// Jackery设备到 插座, 0 不流动，1 往外，2往中央设备，-1 没有这个设备
  @JsonKey(name: "plugInfo")
  PlugInfo? plugInfo;
  @JsonKey(name: "toAcMainDirection")
  int? toAcMainDirection;

  /// Jackery设备到 AC main, 0 不流动，1 往外，2往中央设备，-1 没有这个设备
  @JsonKey(name: "acMainVO")
  AcMainVo? acMainVo;
  @JsonKey(name: "toGridDirection")
  int? toGridDirection;

  /// Jackery设备到 Grid, 0 不流动，1 往外，2往中央设备，-1 没有这个设备
  @JsonKey(name: "gridVO")
  GridVo? gridVo;
  @JsonKey(name: "toOtherLoadDirection")
  int? toOtherLoadDirection;

  /// Jackery设备到 other load, 0 不流动，1 往外，2往中央设备，-1 没有这个设备
  @JsonKey(name: "otherLoadVO")
  OtherLoadVo? otherLoadVo;
  int? toAcOnGridDirection;

  /// 并机CT
  String? combineCtValue = '0';

  /// 中央设备到右边的 ac on grid 线

  String ctValue = '0';

  /// CT设备信息
  @JsonKey(name: 'energyFlowCTVO')
  EnergyFlowCTVO? ctDevice;

  List<DCPropertyModel>? dcs;
  // 获取所有DC设备
  List<DcInfo?> getAllDCList() {
    List<DcInfo?> dcInfoList = [];
    if (dcInfo?.deviceNo != null) {
      dcInfoList.add(dcInfo);
    }
    dcInfoList.addAll(dcInfo?.slaverList ?? []);
    return dcInfoList;
  }

  EnergyFlowChartVo(
      {this.emsGwVo,
      this.toPvDirection,
      this.pvInfo,
      this.toAcOutputDirection,
      this.acInfo,
      this.toDcDirection,
      this.dcInfo,
      this.toPlugDirection,
      this.plugInfo,
      this.toAcMainDirection,
      this.acMainVo,
      this.toGridDirection,
      this.gridVo,
      this.toOtherLoadDirection,
      this.otherLoadVo,
      this.ctDevice,
      this.ctValue = '0'});

  factory EnergyFlowChartVo.fromJson(Map<String, dynamic> json) =>
      _$EnergyFlowChartVoFromJson(json);

  Map<String, dynamic> toJson() => _$EnergyFlowChartVoToJson(this);
  @override
  toString() {
    return 'EnergyFlowChartVo{emsGwVo: $emsGwVo, toPvDirection: $toPvDirection, pvInfo: $pvInfo, toAcOutputDirection: $toAcOutputDirection, acInfo: $acInfo, toDcDirection: $toDcDirection, dcInfo: $dcInfo, toPlugDirection: $toPlugDirection, plugInfo: $plugInfo, toAcMainDirection: $toAcMainDirection, acMainVo: $acMainVo, toGridDirection: $toGridDirection, gridVo: $gridVo, toOtherLoadDirection: $toOtherLoadDirection, otherLoadVo: $otherLoadVo, toAcOnGridDirection: $toAcOnGridDirection, ctValue: $ctValue, ctDevice: $ctDevice, dcs: $dcs}';
  }
}

@JsonSerializable()
class AcInfo {
  // 并机开关
  @JsonKey(name: "combineSwitch")
  int? combineSwitch;
  @JsonKey(name: "acOutEnable")
  int? acOutEnable; // 开关 1开 0关
  @JsonKey(name: "epsLoadPower")
  double? epsLoadPower;
  @JsonKey(name: "combinePower")
  double? combinePower;
  @JsonKey(name: "deviceNo")
  String? deviceNo;
  @JsonKey(name: "deviceName")
  String? deviceName;
  @JsonKey(name: "modelKey")
  String? modelKey;
  @JsonKey(name: "productKey")
  String? productKey;
  @JsonKey(name: "acOutEnableIdentify")
  String? acOutEnableIdentify;
  List<AcInfo>? slaverList;
  String getPowerDisplayValue() {
    if (isCombine()) {
      return combinePower?.abs().toInt().toString() ?? '-';
    }
    return epsLoadPower?.abs().toInt().toString() ?? '-';
  }

  //
  String getPowerDisplayValueWithUnit() {
    if (acOutEnable == AcOutEnableStatus.off.toInt()) {
      return '0W';
    }
    return '${DeviceConfig.getPowerByDouble(epsLoadPower ?? 0)}W';
  }

  AcInfo(
      {this.acOutEnable,
      this.epsLoadPower,
      this.combinePower,
      this.combineSwitch,
      this.deviceNo,
      this.deviceName,
      this.modelKey,
      this.productKey,
      this.acOutEnableIdentify,
      this.slaverList});

  factory AcInfo.fromJson(Map<String, dynamic> json) => _$AcInfoFromJson(json);

  Map<String, dynamic> toJson() => _$AcInfoToJson(this);
  @override
  toString() {
    return 'AcInfo{acOutEnable: $acOutEnable, epsLoadPower: $epsLoadPower, deviceNo: $deviceNo, deviceName: $deviceName, modelKey: $modelKey, productKey: $productKey, acOutEnableIdentify: $acOutEnableIdentify, slaverList: $slaverList}';
  }
}

@JsonSerializable()
class AcMainVo {
  @JsonKey(name: "acMainPower")
  double? acMainPower;
  int? deviceStatus; // deviceConfig.deviceStatusAlarm etc

  AcMainVo({this.acMainPower});

  String getPowerDisplayValue() {
    return acMainPower?.abs().toInt().toString() ?? '-';
  }

  factory AcMainVo.fromJson(Map<String, dynamic> json) =>
      _$AcMainVoFromJson(json);

  Map<String, dynamic> toJson() => _$AcMainVoToJson(this);
}

@JsonSerializable()
class DcInfo {
  // 并机功率
  @JsonKey(name: "combinePower")
  double? combinePower;
  @JsonKey(name: "dcOutEnable")
  int? dcOutEnable; // DC 总开关
  @JsonKey(name: "dcOutPower")
  double? dcOutPower;
  @JsonKey(name: "deviceNo")
  String? deviceNo;
  @JsonKey(name: "deviceName")
  String? deviceName;
  @JsonKey(name: "typeC1Power")
  double? typeC1Power;
  @JsonKey(name: "typeC1Switch")
  int? typeC1Switch;
  @JsonKey(name: "typeC2Power")
  double? typeC2Power;
  @JsonKey(name: "typeC2Switch")
  int? typeC2Switch;
  @JsonKey(name: "usbSwitchPower")
  double? usbSwitchPower;
  @JsonKey(name: "usbaswitch")
  int? usbaswitch;
  int? deviceStatus; // deviceConfig.deviceStatusAlarm etc
  @JsonKey(name: "dcStatus")
  int? dcCommunicationStatus; // DC设备通信状态
  @JsonKey(name: "usbaSwitchIdentify")
  String? usbaSwitchIdentify;
  @JsonKey(name: "typeC2SwitchIdentify")
  String? typeC2SwitchIdentify;
  @JsonKey(name: "typeC1SwitchIdentify")
  String? typeC1SwitchIdentify;
  @JsonKey(name: "dcLoadSwitchIdentifier")
  String? dcLoadSwitchIdentifier;
  @JsonKey(name: "modelKey")
  String? modelKey;
  @JsonKey(name: "productKey")
  String? productKey;
  @JsonKey(name: 'dcModelKey')
  String? dcModelKey;
  @JsonKey(name: "dcProductKey")
  String? dcProductKey;
  @JsonKey(name: "emsDeviceNo")
  String? emsDeviceNo;
  @JsonKey(name: "slaverList")
  List<DcInfo>? slaverList;

  // 是否可用
  bool isEnable() {
    return dcCommunicationStatus == DcCommunicationStatus.normal.toInt();
  }

  bool get disable =>
      dcCommunicationStatus == DcCommunicationStatus.offline.toInt();

  // 开关状态
  bool get switchStatus => dcOutEnable == DcSwitchStatus.on.toInt();
  String getDCPowerDisplayValue() {
    if (isCombine()) {
      return combinePower?.abs().toInt().toString() ?? '-';
    }
    return dcOutPower?.abs().toInt().toString() ?? '-';
  }

  invokeDCSwitch(DcInfo? dcInfo, BuildContext context) async {
    try {
      if (dcInfo == null) return false;
      final currentState = dcInfo.dcOutEnable ==
          DcSwitchStatus.on.toInt(); // 1 means ON, 2 means OFF
      if (LocalModeManger.instance.isLocalModeNow) {
        await SystemClientRepository.instance.invoke(
          context
                  .read<MonitorFetchBloc>()
                  .state
                  .monitorModel
                  ?.energyFlowChartVO
                  ?.emsGwVo
                  ?.deviceNo ??
              "",
          dcInfo.dcModelKey ?? '',
          diy_point_dc_output_status_write,
          dcInfo.dcProductKey ?? '',
          currentState ? DIY_FLOW_SWITCH_CLOSE : DIY_FLOW_SWITCH_OPEN,
        );
        dcInfo.dcOutEnable = currentState
            ? DcSwitchStatus.off.toInt()
            : DcSwitchStatus.on.toInt();
      }

      if (!LocalModeManger.instance.isLocalModeNow) {
        await SystemClientRepository.instance.setProperties(
          context
                  .read<MonitorFetchBloc>()
                  .state
                  .monitorModel
                  ?.energyFlowChartVO
                  ?.emsGwVo
                  ?.deviceNo ??
              "",
          dcInfo.dcModelKey ?? '',
          "dcOutEnable",
          dcInfo.dcProductKey ?? '',
          currentState ? DIY_FLOW_SWITCH_CLOSE : DIY_FLOW_SWITCH_OPEN,
        );
        final curSystemId = SpUtil.getString(currentSystemIdKey, defValue: '');
        context
            .read<MonitorFetchBloc>()
            .add(MonitorInfoFetched(curSystemId, needLoading: false));
        await context
            .read<MonitorFetchBloc>()
            .stream
            .firstWhere((state) => state.isLoading == false);
      }
      return true;
    } catch (_) {
      final msg = _ is ServiceException ? _.msg : null;
      CustomToast.showToast(context, $t(msg ?? 'common.operateFail'));
      return false;
    }
  }

  DcInfo(
      {this.dcOutEnable,
      this.dcOutPower,
      this.deviceNo,
      this.deviceName,
      this.typeC1Power,
      this.typeC1Switch,
      this.typeC2Power,
      this.typeC2Switch,
      this.usbSwitchPower,
      this.usbaswitch,
      this.dcLoadSwitchIdentifier,
      this.typeC1SwitchIdentify,
      this.typeC2SwitchIdentify,
      this.usbaSwitchIdentify,
      this.modelKey,
      this.productKey,
      this.dcModelKey,
      this.dcProductKey,
      this.deviceStatus,
      this.dcCommunicationStatus,
      this.emsDeviceNo,
      this.slaverList});

  factory DcInfo.fromJson(Map<String, dynamic> json) => _$DcInfoFromJson(json);

  Map<String, dynamic> toJson() => _$DcInfoToJson(this);
}

@JsonSerializable()
class EmsGwVo {
  @JsonKey(name: "gwStatus")
  int? gwStatus; // 1 在线 2 离线
  @JsonKey(name: "chargeStatus")
  int? chargeStatus; // 0 空闲 1 充电 2 放电 3 待机
  @JsonKey(name: "energyRemain")
  double? energyRemain;
  @JsonKey(name: "powerPackList")
  List<PowerPackList?>? powerPackList;
  @JsonKey(name: "powerPacks")
  int? powerPacks;
  @JsonKey(name: "soc")
  double? soc;
  @JsonKey(name: 'workStatus')
  int? deviceStatus; // deviceConfig.deviceStatusAlarm etc
  @JsonKey(name: 'workStatusLanguageKey')
  String? workStatusLanguageKey;
  @JsonKey(name: 'modelKey')
  String? modelKey;
  @JsonKey(name: 'productKey')
  String? productKey;
  @JsonKey(name: 'deviceNo')
  String? deviceNo;
  @JsonKey(name: 'workModeIdentify')
  String? workModeIdentify;
  @JsonKey(name: 'csq4G')
  int? csq4G;
  @JsonKey(name: 'csqWifi')
  int? csqWifi;
  @JsonKey(name: 'netType')
  int? netType;

  /// 并网状态 // 0 离网，1并网
  @JsonKey(name: 'gridConnStatus')
  int? gridConnStatus;
  @JsonKey(name: "slaverList")
  List<EmsGwVo>? slaverList;

  String getSOCDisplayValue() {
    return soc?.abs().toInt().toString() ?? '-';
  }

  EmsGwVo(
      {this.chargeStatus,
      this.energyRemain,
      this.powerPackList,
      this.powerPacks,
      this.soc,
      this.workStatusLanguageKey,
      this.productKey,
      this.modelKey,
      this.deviceNo,
      this.workModeIdentify,
      this.csq4G,
      this.csqWifi,
      this.netType,
      this.gridConnStatus,
      this.slaverList});

  factory EmsGwVo.fromJson(Map<String, dynamic> json) =>
      _$EmsGwVoFromJson(json);

  Map<String, dynamic> toJson() => _$EmsGwVoToJson(this);
}

@JsonSerializable()
class PowerPackList {
  // BMS工作状态 0：空闲 1：充电 2：放电
  @JsonKey(name: "bmsOptStatus")
  int? bmsOptStatus;
  @JsonKey(name: "deviceNo")
  String? deviceNo;
  @JsonKey(name: "energyRemain")
  double? energyRemain;
  @JsonKey(name: "soc")
  double? soc;
  @JsonKey(name: "masterDevice")
  bool? masterDevice;

  PowerPackList(
      {this.bmsOptStatus,
      this.deviceNo,
      this.energyRemain,
      this.soc,
      this.masterDevice});

  factory PowerPackList.fromJson(Map<String, dynamic> json) =>
      _$PowerPackListFromJson(json);

  Map<String, dynamic> toJson() => _$PowerPackListToJson(this);
}

@JsonSerializable()
class GridVo {
  // 并机功率
  @JsonKey(name: "combinePower")
  double? combinePower;
  @JsonKey(name: "gridPower")
  double? gridPower;
  @JsonKey(name: "deviceName")
  String? deviceName;
  @JsonKey(name: "slaverList")
  List<GridVo>? slaverList;

  String getPowerDisplayValue() {
    if (isCombine()) {
      return combinePower?.abs().toInt().toString() ?? '-';
    }
    return gridPower?.abs().toInt().toString() ?? '-';
  }

  // 下钻页面并机功率
  String powerDisplayValue() {
    return gridPower?.abs().toInt().toString() ?? '0';
  }

  @JsonKey(name: "toGridDirection")
  int? toGridDirection;

  GridVo({this.gridPower, this.slaverList});

  factory GridVo.fromJson(Map<String, dynamic> json) => _$GridVoFromJson(json);

  Map<String, dynamic> toJson() => _$GridVoToJson(this);
}

@JsonSerializable()
class OtherLoadVo {
  // 并机功率
  @JsonKey(name: "combinePower")
  double? combinePower;
  @JsonKey(name: "otherLoadPower")
  double? otherLoadPower;
  @JsonKey(name: "otherLoadIdentify")
  String? otherLoadIdentify;
  @JsonKey(name: "otherLoadJson")
  String? otherLoadJson;
  @JsonKey(name: "modelKey")
  String? modelKey;
  @JsonKey(name: "productKey")
  String? productKey;
  @JsonKey(name: "deviceNo")
  String? deviceNo;
  @JsonKey(name: "slaverList")
  List<OtherLoadVo>? slaverList;
  @JsonKey(name: "deviceName")
  String? deviceName;
  String getPowerDisplayValue() {
    if (otherLoadPower == null) {
      return '-';
    }
    return otherLoadPower?.abs().toInt().toString() ?? '-';
  }

  // 显示能流图并机功率
  String getCombinePowerDisplayValue() {
    return combinePower?.abs().toInt().toString() ?? '-';
  }

  OtherLoadVo(
      {this.otherLoadPower,
      this.otherLoadIdentify,
      this.otherLoadJson,
      this.modelKey,
      this.productKey,
      this.deviceNo,
      this.slaverList});

  factory OtherLoadVo.fromJson(Map<String, dynamic> json) =>
      _$OtherLoadVoFromJson(json);

  Map<String, dynamic> toJson() => _$OtherLoadVoToJson(this);
}

@JsonSerializable()
class PlugInfo {
  @JsonKey(name: "plugNum")
  int? plugNum;
  @JsonKey(name: "plugPower")
  double? plugPower;
  @JsonKey(name: "plugs")
  List<Plug>? plugs;
  int? deviceStatus; // deviceConfig.deviceStatusAlarm etc
  @JsonKey(name: "slaverList")
  List<PlugInfo>? slaverList;

  String getPowerDisplayValue() {
    return plugPower?.abs().toInt().toString() ?? '-';
  }

  PlugInfo({
    this.plugNum,
    this.plugPower,
    this.plugs,
    this.slaverList,
  });

  factory PlugInfo.fromJson(Map<String, dynamic> json) =>
      _$PlugInfoFromJson(json);

  Map<String, dynamic> toJson() => _$PlugInfoToJson(this);
}

@JsonSerializable()
class Plug {
  @JsonKey(name: "deviceName")
  String? deviceName;
  @JsonKey(name: "deviceNo")
  String? deviceNo;
  @JsonKey(name: "deviceStatus")
  // 设备状态 0未激活 1正常 2离线
  int? deviceStatus;
  @JsonKey(name: "plugPower")
  double? plugPower;
  @JsonKey(name: "plugSwitch")
  int? plugSwitch;
  @JsonKey(name: "modelKey")
  String? modelKey;
  @JsonKey(name: "productKey")
  String? productKey;
  @JsonKey(name: "plugSwitchIdentify")
  String? plugSwitchIdentify;

  /// 下控后的状态
  int? requestStatus;

  List<DevicePlugTimerResponse>? timers;

  Plug(
      {this.deviceName,
      this.deviceNo,
      this.plugPower,
      this.plugSwitch,
      this.modelKey,
      this.productKey,
      this.requestStatus,
      this.plugSwitchIdentify,
      this.timers});

  factory Plug.fromJson(Map<String, dynamic> json) => _$PlugFromJson(json);

  Map<String, dynamic> toJson() => _$PlugToJson(this);
}

@JsonSerializable()
class PvInfo {
  // 并机功率
  @JsonKey(name: "combinePower")
  double? combinePower;
  @JsonKey(name: "pvPower")
  double? pvPower;
  @JsonKey(name: "pvPowerList")
  List<double>? pvPowerList;
  @JsonKey(name: "deviceNo")
  String? deviceNo;
  @JsonKey(name: "deviceName")
  String? deviceName;
  @JsonKey(name: "slaverList")
  List<PvInfo>? slaverList;

  String getPowerDisplayValue() {
    if (isCombine()) {
      return combinePower?.abs().toInt().toString() ?? '-';
    }
    return pvPower?.abs().toInt().toString() ?? '-';
  }

  PvInfo(
      {this.combinePower,
      this.pvPower,
      this.pvPowerList,
      this.deviceNo,
      this.deviceName,
      this.slaverList});

  factory PvInfo.fromJson(Map<String, dynamic> json) => _$PvInfoFromJson(json);

  Map<String, dynamic> toJson() => _$PvInfoToJson(this);
}

/// 能流图CT设备信息
@JsonSerializable()
class EnergyFlowCTVO {
  /// ct设备状态
  @JsonKey(name: 'ctStatus')
  CTStatusType ctStatus;

  /// CT设备名称
  @JsonKey(name: 'ctDeviceName')
  String ctDeviceName;

  @JsonKey(name: 'productKey')
  final String productKey;

  @JsonKey(name: 'deviceNo')
  final String deviceNo;

  @JsonKey(name: "slaverList")
  List<EnergyFlowCTVO>? slaverList;

  EnergyFlowCTVO({
    this.ctStatus = CTStatusType.normal,
    this.ctDeviceName = "",
    this.deviceNo = "",
    this.productKey = "",
  });

  factory EnergyFlowCTVO.fromJson(Map<String, dynamic> json) {
    return _$EnergyFlowCTVOFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EnergyFlowCTVOToJson(this);
}

/// CT设备状态
enum CTStatusType {
  @JsonValue(0)
  offline(0),
  @JsonValue(1)
  normal(1),
  @JsonValue(2)
  fault(2),
  @JsonValue(3)
  alarm(3);

  final int value;

  Color get color {
    switch (value) {
      case 2:
        return ColorsUtil.systemFaultColor;
      case 3:
        return ColorsUtil.systemAlarmColor;
      default:
        return ColorsUtil.contentColor;
    }
  }

  static CTStatusType fromValue(int value) {
    return CTStatusType.values.firstWhere((e) => e.value == value);
  }

  const CTStatusType(this.value);
}
