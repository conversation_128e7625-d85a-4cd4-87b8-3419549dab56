import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_basic/components/bottom_button.dart';
import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/platform/config/language_config.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/user/UserManager.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/platform/utils/global.dart';
import 'package:flutter_basic/platform/utils/language_utils.dart';
import 'package:flutter_basic/router/router.dart';
import 'package:flutter_js/quickjs/ffi.dart';
import 'package:flutter_page_lifecycle/flutter_page_lifecycle.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../components/cus_InkWell/CusInkWell.dart';

class WelcomePage extends StatefulWidget {
  const WelcomePage({Key? key}) : super(key: key);

  @override
  State<WelcomePage> createState() => _WelcomePageState();
}

class _WelcomePageState extends State<WelcomePage> with WidgetsBindingObserver {
  bool isFirst = true;

  late AssetImage backgroundImage =
      AssetImage(CustomImageAsset.getImagePathByName('welcome_bg_2'));

  @override
  void initState() {
    super.initState();
    // 检测是否有网络 没有提醒一下
    // 两次检测是因为第一次检测在ios下会返回none(无网络)
    Connectivity()
        .checkConnectivity()
        .then((r) => Connectivity().checkConnectivity())
        .then((value) {
      logger.i('value $value');
      var hasNetwork = !value.contains(ConnectivityResult.none);
      if (!hasNetwork) {
        CustomToast.showToast(context, S.current.text('diyNew.qr_no_network'));
      }
    });
  }

  @override
  void dispose() {
    // 清除图片缓存
    backgroundImage.evict();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Global.mainContext = context;
    var currentlangName = LanguageConfig.getCurrentAppLocaleLanguage();
    String language = LanguageUtils.list
            .firstWhereOrNull((v) => v.identify == currentlangName)
            ?.name ??
        '';
    var content = Stack(children: [
      Positioned(
        top: 1.h,
        right: 20.w,
        child: _buildSelectLanguage(context, language),
      ),
      Positioned(
        top: 110.w,
        width: ScreenUtil().screenWidth,
        height: 59.w,
        child: Center(
          child: CustomImageAsset('slogan_logo', width: 180.w, height: 59.w),
        ),
      ),
      Positioned(
        child: Container(
          margin: EdgeInsets.only(bottom: 20.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              BottomButtonWidget(
                margin: EdgeInsets.zero,
                text: S.current.text('login.btn_sign_in_text'),
                onPressed: () => signIn(UserRole.user),
              ),
              SizedBox(height: 23.w),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    S.current.text('login.account_tips'),
                    style: TextStyle(color: ColorsUtil.buttonTextColor),
                  ),
                  SizedBox(width: 5.w),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: signUp,
                    child: Text(
                      S.current.text('login.jump_register'),
                      style: TextStyle(
                          color: ColorsUtil.trendTabTextSelectedColor),
                    ),
                  )
                ],
              ),
              SizedBox(height: 60.w),
              // Container(
              //   margin: EdgeInsets.only(top: 30.w, bottom: 22.w),
              //   height: 1.w,
              //   decoration: const BoxDecoration(
              //     gradient: LinearGradient(
              //       begin: Alignment.centerLeft,
              //       end: Alignment.centerRight,
              //       colors: [
              //         Color(0x00FFFFFF),
              //         Color(0x33FFFFFF),
              //         Color(0x33FFFFFF),
              //         Color(0x00FFFFFF),
              //       ],
              //     ),
              //   ),
              // ),
              // TextButton.icon(
              //   icon: SvgPicture.asset('lib/assets/icons/icon_installer.svg',
              //       width: 22.w),
              //   label: Text(
              //     S.current.text('installer.welcome.login'),
              //     style: TextStyle(color: Colors.white, fontSize: 14.sp),
              //   ),
              //   onPressed: () => signIn(UserRole.installer),
              // )
            ],
          ),
        ),
      )
    ]);

    return PageLifecycle(
      stateChanged: (isShow) {
        if (isShow) UserManager.instance.resetRole();
      },
      child: Scaffold(
        backgroundColor: ColorsUtil.themeColor,
        body: Container(
          decoration: BoxDecoration(
            image: DecorationImage(image: backgroundImage, fit: BoxFit.cover),
          ),
          child: SafeArea(child: content),
        ),
      ),
    );
  }

  SizedBox _buildSelectLanguage(BuildContext context, String language) {
    var titleStyle = TextStyle(
      color: Colors.white,
      fontWeight: FontWeight.w600,
      fontSize: 13.sp,
      height: 1.25.h,
    );

    return SizedBox(
      height: 44.h,
      child: CusInkWell(
        onTap: () async {
          String language =
              await Navigator.of(context).push(PlatformRoutes.onGenerateRoute(
            const RouteSettings(name: PlatformRoutesType.changeLanguage),
          ));
          logger.d('选择了语言 $language');
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CustomImageAsset('icon_language', size: 18.w),
            Padding(padding: EdgeInsets.only(left: 5.w)),
            Text(language, style: titleStyle),
            SizedBox(width: 3.w),
            CustomImageAsset('icon_language_arrow', size: 18.sp)
          ],
        ),
      ),
    );
  }

  /// 普通用户登录
  void signIn(UserRole role) {
    // 检测是否有网络 没有提醒一下
    Connectivity().checkConnectivity().then((value) {
      var hasNetwork = !value.contains(ConnectivityResult.none);
      if (!hasNetwork) {
        CustomToast.showToast(context, S.current.text('diyNew.qr_no_network'));
        return;
      }

      Navigator.of(context).push(
        LoginRoutes.onGenerateRoute(
          RouteSettings(
            name: LoginRoutesType.loginRoute,
            arguments: {'role': role},
          ),
        ),
      );
    });
  }

  /// 注册
  void signUp() {
    Navigator.of(context).push(
      LoginRoutes.onGenerateRoute(
        const RouteSettings(name: LoginRoutesType.signupRoute),
      ),
    );
  }
}
