// ignore_for_file: control_flow_in_finally

import 'dart:convert';
import 'dart:io';

import 'package:archive/archive.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_basic/platform/cache/file_system.dart';
import 'package:flutter_basic/platform/utils/language_utils.dart';
import 'package:flutter_basic/platform/utils/sp_utils.dart';
import 'package:flutter_basic/repositories/download_repository/download_client_repository.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../repositories/download_repository/model/model.dart';
import 'log_utils.dart';

class FileUtils {
  static Future<int> getAndroidVersion() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidDeviceInfo = await deviceInfo.androidInfo;
    return int.parse(androidDeviceInfo.version.release);
  }

  static Directory? directory;
  static Directory? cacheDirectory;

  static Future<Directory?> getDirectory() async {
    directory ??= await getApplicationDocumentsDirectory();
    return directory;
  }

  static Future<Directory?> getCacheDirectory() async {
    cacheDirectory ??= await FileSystemImpl.instance.getTemporaryDirectory();
    return cacheDirectory;
  }

  static final FileUtils singleton = FileUtils();
  Map<String, String>? _protocolJSMap;
  Map<String, TslModel>? _tslJsonMap;

  Future<Map<String, String>> get protocolJSMap async {
    _protocolJSMap ??= await readProtocolJSFile();
    return _protocolJSMap!;
  }

  Future<Map<String, TslModel>> get tslJsonMap async {
    _tslJsonMap ??= await readTslFile();
    return _tslJsonMap!;
  }

  /// 初始化远端配置信息到本地
  static Future<void> init() async {
    logger.d('======start:${DateTime.timestamp().millisecondsSinceEpoch}');

    // First load local language file immediately
    await _loadLocalLanguageFile();

    // Then download remote files in background
    _downloadFilesInBackground();
  }

  static Future<void> _loadLocalLanguageFile() async {
    final String fileDirectory =
        "${(await getDirectory())?.path}${Platform.pathSeparator}${JsonFile.languageJsonAttach.fileDirectory}";

    // Try to find existing downloaded language file
    var languageFile = findLatestJsonFile(fileDirectory);
    if (languageFile != null && languageFile.existsSync()) {
      // Load existing downloaded language file
      LanguageUtils.list = await readLanguageJson(languageFile);
      logger.d('setList from existing downloaded file');
    } else {
      // Load from assets if no downloaded file exists
      List<LanguageModel> list = await readLanguageJsonFromAsset();
      LanguageUtils.list = list;
      logger.d('setList from assets');
    }
  }

  static Future<void> _downloadFilesInBackground() async {
    // Download files in parallel without blocking app startup
    await Future.wait([
      downloadAndUnzipFile(JsonFile.languageJsonAttach),
      downloadAndUnzipFile(JsonFile.protocolJSFile),
      downloadAndUnzipFile(JsonFile.tslFile),
    ]);
  }

  /// 文件下载
  static Future<void> downloadAndUnzipFile(JsonFile fileType) async {
    int start = DateTime.timestamp().millisecondsSinceEpoch;
    logger.d('=======downloadAndUnzipFile=${fileType.fileName} 开始 :$start');
    final LanguageJsonAttachModel result;
    final String filePath =
        '${(await getDirectory())?.path}${Platform.pathSeparator}${fileType.fileDirectory}${Platform.pathSeparator}${fileType.fileName}';
    final String fileDirectory =
        "${(await getDirectory())?.path}${Platform.pathSeparator}${fileType.fileDirectory}";
    File file = File(filePath);
    try {
      int startConfig = DateTime.timestamp().millisecondsSinceEpoch;
      logger.d(
          '=======downloadAndUnzipFile=${fileType.fileName} 开始请求配置文件 :$startConfig}');
      result = await DownloadClientRepository.instance.readJsonConfig(fileType);
      int endConfig = DateTime.timestamp().millisecondsSinceEpoch;
      logger.d(
          '=======downloadAndUnzipFile=${fileType.fileName} 请求配置文件完成 :$endConfig  下载配置文件耗费时间${endConfig - startConfig}');
      int preModifyTime = -1;
      preModifyTime = SpUtil.getInt(fileType.spKey, defValue: -1) ?? -1;
      final int millisecondsSinceEpoch =
          DateTime.parse(result.modifyTime).millisecondsSinceEpoch;
      logger.d(
          'file exit ${file.statSync().type} fileType.spKey ${fileType.spKey} preModifyTime $preModifyTime millisecondsSinceEpoch $millisecondsSinceEpoch if value ${preModifyTime == -1 || millisecondsSinceEpoch > preModifyTime || !file.existsSync()}');
      // need download file
      if (preModifyTime == -1 ||
          millisecondsSinceEpoch > preModifyTime ||
          (fileType == JsonFile.languageJsonAttach &&
              FileSystemEntityType.notFound == file.statSync().type)) {
        if (kDebugMode) {
          print('download file : $filePath');
        }
        final urlPath = result.jsonFileUrl ?? result.url;
        if (urlPath != null) {
          int downFileStart = DateTime.timestamp().millisecondsSinceEpoch;
          logger.d(
              '=======downloadAndUnzipFile=${fileType.fileName} 开始下载文件 :$downFileStart}');
          await DownloadClientRepository.instance
              .getDownloadFile(fileType, urlPath);
          int downFileEnd = DateTime.timestamp().millisecondsSinceEpoch;
          logger.d(
              '=======downloadAndUnzipFile=${fileType.fileName} 下载文件完成 :$downFileEnd  下载文件耗费时间：${downFileEnd - downFileStart}');
          logger
              .d('after download file urlpath : $urlPath filepath :$filePath');
          SpUtil.putInt(fileType.spKey, millisecondsSinceEpoch);
          // 解压文件
          // if (fileType != JsonFile.languageJsonAttach) {
          int unzipStart = DateTime.timestamp().millisecondsSinceEpoch;
          logger.d(
              '=======downloadAndUnzipFile=${fileType.fileName} 开始解压文件 :$unzipStart');
          await unzip(filePath, fileDirectory);
          int unzipEnd = DateTime.timestamp().millisecondsSinceEpoch;

          logger.d(
              '=======downloadAndUnzipFile=${fileType.fileName} 解压文件完成 :$unzipEnd  解压文件耗费时间 ： ${unzipEnd - unzipStart}');
          // }
        } else {
          if (kDebugMode) {
            print('download path is null');
          }
        }
        // update the file config modifyTime after download the current file success
      } else {
        if (kDebugMode) {
          print('read native file: $filePath');
        }
      }
    } catch (_) {
      logger.d("url path $_");
      logger.d('read service config exception fileType：$fileType');
      logger.d('read service config exception filePath：$filePath');
    } finally {
      if (fileType == JsonFile.languageJsonAttach) {
        int readStart = DateTime.timestamp().millisecondsSinceEpoch;
        // 查找最新语言包文件
        var languageFile = findLatestJsonFile(fileDirectory);
        logger.d(
            '=======downloadAndUnzipFile=${fileType.fileName} 开始读取文件 :$readStart');
        if (languageFile != null && file.existsSync()) {
          LanguageUtils.list = await readLanguageJson(languageFile);
          logger.d('setList from file');
        } else {
          List<LanguageModel> list =
              await FileUtils.readLanguageJsonFromAsset();
          if (LanguageUtils.list.isEmpty) {
            LanguageUtils.list = list;
            logger.d('setList from asset after finally');
          }
        }
        int readEnd = DateTime.timestamp().millisecondsSinceEpoch;
        logger.d(
            '=======downloadAndUnzipFile=${fileType.fileName} 读取文件结束 :$readEnd 读取文件耗费时间： ${readEnd - readStart}');
      }
    }
    int end = DateTime.timestamp().millisecondsSinceEpoch;
    logger.d(
        '=======downloadAndUnzipFile=${fileType.fileName} 结束 $end 耗费时间  :${end - start}');
  }

  /// 找到文件夹中最新的json文件
  static File? findLatestJsonFile(String fileDirectory) {
    Directory directory = Directory(fileDirectory);
    if (directory.existsSync() == false) {
      return null;
    }
    List<FileSystemEntity> files = directory.listSync();
    List<File> jsonFiles = files
        .where((file) => file is File && file.path.endsWith('.json'))
        .map((file) => file as File)
        .toList();
    jsonFiles
        .sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
    return jsonFiles.isNotEmpty ? jsonFiles.first : null;
  }

  static Future<void> unzip(String zipFilePath, String fileDirectory) async {
    if (!File(zipFilePath).existsSync()) {
      logger.d("压缩包文件不存在！$zipFilePath");
      return;
    }
    logger.d('开始解压');
    // var permissionList =  [
    // Permission.storage,
    // ];
    // if(Platform.isAndroid) {
    //   int version = await FileUtils.getAndroidVersion();
    //   logger.d('android version in file utils: $version');
    //   if(version >= 13 ) {
    //     permissionList = [Permission.manageExternalStorage];
    //   }
    // }
    // var permission = await PermissionUtils.checkPermissionSync(permissionList: permissionList, goSetting: (){});
    if (true) {
      logger.d("checkPermission on Success");
      // 从磁盘读取Zip文件。
      try {
        List<int> bytes = File(zipFilePath).readAsBytesSync();
        // 解码Zip文件
        Archive archive = ZipDecoder().decodeBytes(bytes);
        // 将Zip存档的内容解压缩到磁盘。
        for (ArchiveFile file in archive) {
          if (file.isFile) {
            logger.d("$fileDirectory${Platform.pathSeparator}${file.name}");
            List<int> data = file.content;
            File("$fileDirectory${Platform.pathSeparator}${file.name}")
              ..createSync(recursive: true)
              ..writeAsBytesSync(data);
          } else {
            logger.d('=-=-=-=-=00000');
            logger.d("$fileDirectory${Platform.pathSeparator}${file.name}");
            Directory("$fileDirectory${Platform.pathSeparator}${file.name}")
                .create(recursive: true);
          }
        }
        logger.d("解压成功");
      } catch (e) {
        logger.d(e.toString());
        logger.d("解压失败");
      }
    }
  }

  static Future<List<LanguageModel>> readLanguageJson(File file) async {
    final String res = await file.readAsString(encoding: utf8);
    final List<LanguageModel> list = List<LanguageModel>.from(
      json
          .decode(res)
          .map((e) => LanguageModel.fromJson(e as Map<String, dynamic>)),
    );
    return list;
  }

  static Future<List<LanguageModel>> readLanguageJsonFromAsset() async {
    final String res =
        await rootBundle.loadString('lib/assets/json/language.json');
    final List<LanguageModel> list = List<LanguageModel>.from(
      json
          .decode(res)
          .map((e) => LanguageModel.fromJson(e as Map<String, dynamic>)),
    );
    return list;
  }

  static Future<Map<String, String>> readProtocolJSFile() async {
    final String fileDirectory =
        "${(await getDirectory())?.path}${Platform.pathSeparator}${JsonFile.protocolJSFile.fileDirectory}";
    logger.d("开始读取js压缩文件$fileDirectory");
    Map<String, String> fileMap = <String, String>{};
    Directory directory =
        Directory("$fileDirectory${Platform.pathSeparator}js");
    // 如果有压缩文件 就直接解压
    final String zipFilePath =
        '$fileDirectory${Platform.pathSeparator}${JsonFile.protocolJSFile.fileName}';
    final zipFile = File(zipFilePath);
    if (zipFile.existsSync()) {
      await unzip(zipFilePath, directory.path);
      zipFile.delete();
    }
    if (directory.existsSync() && directory.listSync().isNotEmpty) {
      fileMap.addAll(await readProtocolJSFileFromNative(directory));
    }
    if (fileMap.isEmpty) {
      // 从本地assets中读取文件
      final manifestJson =
          json.decode(await rootBundle.loadString('AssetManifest.json'));
      final List<String> jsKeyList = manifestJson.keys
          .where((String key) => key.contains('protocolJS'))
          .toList();
      for (var element in jsKeyList) {
        fileMap[path.basenameWithoutExtension(element)] =
            await rootBundle.loadString(manifestJson[element][0]);
      }
    }
    return fileMap;
  }

  static Future<Map<String, TslModel>> readTslFile() async {
    final String fileDirectory =
        "${(await getDirectory())?.path}${Platform.pathSeparator}${JsonFile.tslFile.fileDirectory}";
    logger.d("---------------开始读取tsl -- json压缩文件$fileDirectory");

    Map<String, TslModel> fileMap = <String, TslModel>{};
    Directory directory =
        Directory("$fileDirectory${Platform.pathSeparator}tsltemp");

    final String zipPath =
        '$fileDirectory${Platform.pathSeparator}${JsonFile.tslFile.fileName}';
    final zipFile = File(zipPath);
    if (zipFile.existsSync()) {
      await unzip(zipPath, directory.path);
      zipFile.delete();
    }
    if (directory.existsSync() && directory.listSync().isNotEmpty) {
      fileMap.addAll(await readTslJsonFileFromNative(directory));
    }
    if (fileMap.isEmpty) {
      // 从本地assets中读取文件
      final manifestJson =
          json.decode(await rootBundle.loadString('AssetManifest.json'));
      final List<String> jsKeyList =
          manifestJson.keys.where((String key) => key.contains('tsl')).toList();
      for (var element in jsKeyList) {
        var filePath = manifestJson[element][0];
        var str = await rootBundle.loadString(filePath);
        var json = jsonDecode(str);
        // var fileName = path.basenameWithoutExtension(filePath);
        final TslModel tslModel = TslModel.fromJson(json['result']);
        fileMap[path.basenameWithoutExtension(element)] = tslModel;
      }
    }
    return fileMap;
  }

  static Future<Map<String, String>> readProtocolJSFileFromNative(
      Directory directory) async {
    Map<String, String> fileMap = <String, String>{};
    final entities = directory.listSync();
    if (entities.isNotEmpty) {
      for (var element in entities) {
        logger.d('element.path=====');
        logger.d(element.path);
        if (FileSystemEntity.isDirectorySync(element.path)) {
          fileMap.addAll(
              await readProtocolJSFileFromNative(Directory(element.path)));
        } else {
          File file = File(element.path);
          final relativePath =
              path.relative(file.absolute.path); // 计算相对于应用程序目录的相对路径
          String fileName = path.basenameWithoutExtension(relativePath);
          logger.d('fileName=====');
          logger.d(fileName);
          try {
            final String res = await file.readAsString(encoding: utf8);
            fileMap[fileName] = res;
          } catch (_) {
            logger.d(_.toString());
            logger.d("_读取tsl 的json文件出现异常$fileName");
          }
        }
      }
    }
    return fileMap;
  }

  static Future<Map<String, TslModel>> readTslJsonFileFromNative(
      Directory directory) async {
    Map<String, TslModel> fileMap = <String, TslModel>{};
    List<FileSystemEntity> entities = directory.listSync();
    if (entities.isNotEmpty) {
      for (var element in entities) {
        if (FileSystemEntity.isDirectorySync(element.path)) {
          fileMap
              .addAll(await readTslJsonFileFromNative(Directory(element.path)));
        } else {
          File file = File(element.path);
          if (!file.existsSync()) {
            continue;
          }
          final relativePath =
              path.relative(file.absolute.path); // 计算相对于应用程序目录的相对路径
          String fileName = path.basenameWithoutExtension(relativePath);
          // 读取文件json内容
          try {
            final String res = await file.readAsString(encoding: utf8);
            final TslModel tslModel =
                TslModel.fromJson(jsonDecode(res)['result']);
            fileMap[fileName] = tslModel;
          } catch (_) {
            logger.d(_.toString());
            logger.d("_读取tsl 的json文件出现异常$fileName");
          }
        }
      }
    }
    return fileMap;
  }

  /// 压缩方式二 File -> File
  static Future<File?> fileToFile(File file) async {
    // 图片质量
    int quality = imageQuality(file.readAsBytesSync().length);
    // 缓存路径
    Directory cache = await getTemporaryDirectory();
    int time = DateTime.now().millisecondsSinceEpoch;
    String savePath = "${cache.path}/AllenSu_$time.jpg"; // 指定压缩后图片的路径
    File? result = File((await FlutterImageCompress.compressAndGetFile(
          file.path,
          savePath,
          quality: quality,
        ))
            ?.path ??
        '');
    if (result != null) {
      logger.d("压缩后图片的大小：${size(result.readAsBytesSync().length)}");
    }
    return result;
  }

  /// 根据传入的图片字节长度，返回指定的图片质量
  static int imageQuality(int length) {
    logger.d("压缩前图片的大小：${size(length)}");
    int quality = 100; // 图片质量指数
    int m = 1024 * 1024; // 1 兆
    if (length < 0.5 * m) {
      quality = 70;
      logger.d("图片小于 0.5 兆，质量设置为 70");
    } else if (length >= 0.5 * m && length < 1 * m) {
      quality = 60;
      logger.d("图片大于 0.5 兆小于 1 兆，质量设置为 60");
    } else if (length >= 1 * m && length < 2 * m) {
      quality = 50;
      logger.d("图片大于 1 兆小于 2 兆，质量设置为 50");
    } else if (length >= 2 * m && length < 3 * m) {
      quality = 40;
      logger.d("图片大于 2 兆小于 3 兆，质量设置为 40");
    } else if (length >= 3 * m && length < 4 * m) {
      quality = 30;
      logger.d("图片大于 3 兆小于 4 兆，质量设置为 30");
    } else {
      quality = 20;
      logger.d("图片大于 4 兆，质量设置为 20");
    }
    return quality;
  }

  /// 根据传入的字节长度，转换成相应的 M 和 KB
  static String size(int length) {
    String res = "";
    const int unit = 1024;
    const int m = unit * unit; // 1M
    // 如果小于 1 兆，显示 KB
    if (length < 1 * m) {
      res = "${(length / unit).toStringAsFixed(0)} KB";
    }
    // 如果大于 1 兆，显示 MB，并保留一位小数
    if (length >= 1 * m) {
      res = "${(length / m).toStringAsFixed(1)} MB";
    }
    return res;
  }

  //循环获取缓存大小
  static Future<double> getTotalSizeOfFilesInDir(
      final FileSystemEntity file) async {
    if (file is File && file.existsSync()) {
      int length = await file.length();
      return double.parse(length.toString());
    }
    if (file is Directory && file.existsSync()) {
      List children = file.listSync();
      double total = 0;
      if (children.isNotEmpty) {
        for (final FileSystemEntity child in children) {
          total += await getTotalSizeOfFilesInDir(child);
        }
      }
      return total;
    }
    return 0;
  }

  static Future clearCache(FileSystemEntity file) async {
    PermissionStatus status = await Permission.storage.status;
    await delDir(file);
  }

  static Future delDir(FileSystemEntity file) async {
    if (file is Directory && file.existsSync()) {
      final List<FileSystemEntity> children =
          file.listSync(recursive: true, followLinks: true);
      for (final FileSystemEntity child in children) {
        await delDir(child);
      }
    }
    try {
      if (file.existsSync()) {
        await file.delete(recursive: true);
      }
    } catch (err) {
      //
    }
  }
}
