import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';
import 'package:flutter_basic/config.dart';
import 'package:flutter_mxlogger/flutter_mxlogger.dart';
import 'package:logger/logger.dart';

abstract class BaseLogger {
  void d(Object? message);
  void i(Object? message);
  void w(Object? message);
  void e(Object? message);
  void v(Object? message);
  String logFilePath();
  Future<void> init() async {}
}

class DebugLogger implements BaseLogger {
  late Logger logger;
  late FileOutput fileOutput;

  DebugLogger() {}
  @override
  Future<void> init() async {
    logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        colors: false,
      ),
      filter: CustomLogFilter(),
      output: MultiOutput([
        MyConsoleOutput(),
      ]),
    );
  }

  void _init() {}

  @override
  void d(Object? message) => logger.d(message);

  @override
  void i(Object? message) => logger.i(message);

  @override
  void w(Object? message) => logger.w(message);

  @override
  void e(Object? message) => logger.e(message);

  @override
  void v(Object? message) => logger.v(message);

  @override
  logFilePath() {
    return "";
  }
}

class ReleaseLogger implements BaseLogger {
  late MXLogger logger;

  ReleaseLogger() {}

  Future<void> init() async {
    logger = await MXLogger.initialize(
        nameSpace: "flutter.mxlogger",
        cryptKey: "DMCNytVfhKUqPzyY8Bad",
        iv: "f3cufTR8m8XNwZYo4LGM");
    logger.setMaxDiskAge(60 * 60 * 24 * 7); // 一周
    logger.setMaxDiskSize(1024 * 1024 * 10); // 10M
  }

  @override
  String logFilePath() {
    return logger.getDiskcachePath();
  }

  @override
  void d(Object? message) {
    logger.debug(message.toString());
  }

  @override
  void i(Object? message) {
    logger.info(message.toString());
  }

  @override
  void w(Object? message) {
    logger.warn(message.toString());
  }

  @override
  void e(Object? message) {
    logger.error(message.toString());
  }

  @override
  void v(Object? message) {
    logger.debug(message.toString());
  }
}

class LoggerManager {
  //私有构造函数
  LoggerManager._internal() {
    _logger = kDebugMode ? DebugLogger() : ReleaseLogger();
  }

  //保存单例
  static final LoggerManager _instance = LoggerManager._internal();

  //工厂构造函数
  factory LoggerManager() => _instance;

  late BaseLogger _logger;
  init() {
    _logger.init();
    return _logger;
  }

  void d(Object? message) => _logger.d(message);
  void i(Object? message) => _logger.i(message);
  void w(Object? message) => _logger.w(message);
  void e(Object? message) => _logger.e(message);
  void v(Object? message) => _logger.v(message);
}

// 全局单例
late BaseLogger logger;

class MyConsoleOutput extends ConsoleOutput {
  @override
  void output(OutputEvent event) {
    event.lines.forEach(developer.log);
  }
}

class CustomLogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    return showLog;
  }
}
