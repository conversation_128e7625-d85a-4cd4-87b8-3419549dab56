import 'package:flutter_basic/features/devices/config/type.dart';

///  V0.0.22 版本号对比
const String versionV22 = 'V0.0.22';
const String versionV14 = 'V0.00.014';
const String versionV19 = 'V0.00.019';

/// 是否大于等于 V0.0.22
bool isGreaterThanV0022(String version) {
  if (version.isEmpty) return false;
  return compareVersion(version, versionV22) >= 0;
}

/// 是否大于等于V0.00.014
bool isGreaterThanV14(String version) {
  if (version.isEmpty) return false;
  return compareVersion(version, versionV14) >= 0;
}

/// 是否大于V0.00.019
bool isGreaterThanV19(String version) {
  if (version.isEmpty) return false;
  return compareVersion(version, versionV19) > 0;
}

/// 是否是二代DIY
bool isDIY2(String factoryStr) {
  return factoryStr == DIYFactoryType.diy_2.value ||
      factoryStr == DIYFactoryType.diy_2_plus.value;
}

/// 是否是二代DIY Plus
bool isDIY2Plus(String factoryStr) {
  return factoryStr == DIYFactoryType.diy_2_plus.value;
}

/// otherload 支持 24:00
bool isOtherLoadSupport2400(String factoryStr, String version) {
  if (isDIY2(factoryStr) && isGreaterThanV14(version)) return true;
  return false;
}

int compareVersion(String version1, String version2) {
  List<int> v1 =
      version1.replaceAll('V', '').split('.').map(int.parse).toList();
  List<int> v2 =
      version2.replaceAll('V', '').split('.').map(int.parse).toList();

  for (int i = 0; i < v1.length && i < v2.length; i++) {
    if (v1[i] > v2[i]) return 1;
    if (v1[i] < v2[i]) return -1;
  }

  return v1.length.compareTo(v2.length);
}
