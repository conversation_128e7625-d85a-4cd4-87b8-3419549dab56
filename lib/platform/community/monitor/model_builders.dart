import 'dart:convert';

import 'package:flutter_basic/platform/bluetooth/model/mqtt_message_response.dart';
import 'package:flutter_basic/platform/community/monitor/data_parsers.dart';
import 'package:flutter_basic/platform/community/receiver_manager.dart';
import 'package:flutter_basic/platform/utils/combine.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/monitor_response_graph_model.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/monitor_response_model.dart';

import '../../../features/devices/config/device_config.dart';
import '../../../repositories/monitor_repository/model/monitor_quick_control_model.dart';
import '../../../repositories/monitor_repository/model/monitor_system_model.dart';
import '../../bluetooth/local_mode_manger.dart';
import './constants.dart';
import 'combine_system_handler.dart';

MonitorResponseModel buildModel(String msg) {
  var messageResponse = MqttMessageResponse.fromJson(json.decode(msg));

  /// 使用外部 model
  var monitorModel = ReceiverManager.instance.model ??
      ReceiverManager.instance.cachedOldModel ??
      MonitorResponseModel();
  monitorModel.energyFlowChartVO ??= EnergyFlowChartVo();

  // AC ongrid功率
  double acOngridPower =
      (monitorModel.energyFlowChartVO?.acMainVo?.acMainPower ?? 0) +
          (monitorModel.energyFlowChartVO?.plugInfo?.plugPower ?? 0);
  messageResponse.info?.devList?.forEach((element) {
    var elementProductKey = '';
    for (var key in ReceiverManager.instance.keepDevices.keys) {
      if (ReceiverManager.instance.keepDevices[key]?.contains(element.devSn) ??
          false) {
        elementProductKey = key;
      }
    }
    var devSn = element.devSn;
    var meterList = element.meterList;

    /// 能流图 direction == true 代表往中央设备

    switch (elementProductKey) {
      case diy_Productkey_PCS:
        meterList?.forEach((meter) {
          switch (meter[0]) {
            case diy_point_pv_children_power:
              var value = meter[1];
              var pvChildPower = formatNum(value, 1).abs();
              monitorModel.energyFlowChartVO?.pvInfo?.pvPowerList ??=
                  List.filled(2, 0);
              if ((monitorModel
                          .energyFlowChartVO?.pvInfo?.pvPowerList?.length ??
                      0) <
                  2) {
                monitorModel.energyFlowChartVO?.pvInfo?.pvPowerList =
                    List.filled(2, 0);
              }
              monitorModel.energyFlowChartVO?.pvInfo?.pvPowerList?[0] =
                  pvChildPower;
              break;
            case diy_point_pv_children2_power:
              var value = meter[1];
              var pvChild2Power = formatNum(value, 1).abs();
              monitorModel.energyFlowChartVO?.pvInfo?.pvPowerList ??=
                  List.filled(2, 0);
              if ((monitorModel
                          .energyFlowChartVO?.pvInfo?.pvPowerList?.length ??
                      0) <
                  2) {
                monitorModel.energyFlowChartVO?.pvInfo?.pvPowerList =
                    List.filled(2, 0);
              }
              monitorModel.energyFlowChartVO?.pvInfo?.pvPowerList?[1] =
                  pvChild2Power;
              break;
          }
        });
        break;
      case diy_Productkey_EMS:
        meterList?.forEach((meter) {
          switch (meter[0]) {
            case diy_point_enable_config:
              var value = meter[1];
              monitorModel.energyFlowChartVO?.enableConfig = int.parse(value);
              break;
            case diy_point_other_load_timeslot_1:
              parseOtherLoad(meterList, monitorModel);
              break;
            case diy_point_pv_total_power:
              var value = meter[1];
              monitorModel.energyFlowChartVO?.pvInfo ??= PvInfo();
              monitorModel.energyFlowChartVO?.pvInfo?.pvPower =
                  formatPowerBoundary(formatNum(value, 1).abs());
              monitorModel.energyFlowChartVO?.toPvDirection =
                  double.parse(value).toInt() > DIY_FLOW_DIRECTION_MAX
                      ? DIY_FLOW_DIRECTION_TO_CENTER
                      : DIY_FLOW_DIRECTION_NONE;
              break;
            case diy_point_ac_output_power:
              var value = meter[1];
              monitorModel.energyFlowChartVO?.acInfo ??= AcInfo();
              monitorModel.energyFlowChartVO?.acInfo?.epsLoadPower =
                  formatPowerBoundary(formatNum(value, 1).abs());
              if (formatNum(value, 1) > DIY_FLOW_DIRECTION_MAX) {
                monitorModel.energyFlowChartVO?.toAcOutputDirection =
                    DIY_FLOW_DIRECTION_TO_OUT;
              } else if (formatNum(value, 1) < DIY_FLOW_DIRECTION_MIN) {
                monitorModel.energyFlowChartVO?.toAcOutputDirection =
                    DIY_FLOW_DIRECTION_TO_CENTER;
              } else {
                monitorModel.energyFlowChartVO?.toAcOutputDirection =
                    DIY_FLOW_DIRECTION_NONE;
              }
              break;
            case diy_point_ac_output_switch:
              var value = meter[1];
              monitorModel.energyFlowChartVO?.acInfo ??= AcInfo();
              monitorModel.energyFlowChartVO?.acInfo?.acOutEnable =
                  formatSwitchValue(value);
            case diy_point_dc_output_power:
              var value = meter[1];
              // monitorModel.energyFlowChartVO?.dcInfo ??= DcInfo();
              monitorModel.energyFlowChartVO?.dcInfo?.dcOutPower =
                  formatPowerBoundary(formatNum(value, 1).abs());
              monitorModel.energyFlowChartVO?.toDcDirection =
                  double.parse(value).toInt() > DIY_FLOW_DIRECTION_MAX
                      ? DIY_FLOW_DIRECTION_TO_OUT
                      : DIY_FLOW_DIRECTION_NONE;
              break;
            case diy_point_dc_output_status_read:
              var value = meter[1];
              // monitorModel.energyFlowChartVO?.dcInfo ??= DcInfo();
              monitorModel.energyFlowChartVO?.dcInfo?.dcOutEnable =
                  formatSwitchValue(value);
            case diy_point_plug_count:
              var value = meter[1];
              monitorModel.energyFlowChartVO?.plugInfo?.plugNum =
                  int.parse(value);
              break;
            case diy_point_plug_total_power:
              var value = meter[1];
              // monitorModel.energyFlowChartVO?.plugInfo ??= PlugInfo();
              monitorModel.energyFlowChartVO?.plugInfo?.plugPower =
                  formatPowerBoundary(formatNum(value, 1).abs());
              break;
            case diy_point_CT_status:
              var value = int.parse(meter[1]);
              var status =
                  value == 1 ? CTStatusType.normal : CTStatusType.offline;
              monitorModel.energyFlowChartVO?.ctDevice?.ctStatus = status;
              break;
            case diy_point_ct_power:
              monitorModel.energyFlowChartVO?.ctValue = meter[1];
              break;
            case diy_point_other_load:
              // 当Others =< -3W时，电量由Others 流向Ongrid节点；
              // 当Others >= 3W时，电量由ongrid 节点流向Others;
              // -3W<Others<3W时，电量显示为0W。
              var value = meter[1];
              var formatValue = formatPowerBoundary(formatNum(value, 1));
              monitorModel.energyFlowChartVO?.otherLoadVo ??= OtherLoadVo();
              monitorModel.energyFlowChartVO?.otherLoadVo?.otherLoadPower =
                  formatValue;
              if (formatValue < DIY_FLOW_DIRECTION_MAX &&
                  formatValue > DIY_FLOW_DIRECTION_MIN) {
                monitorModel.energyFlowChartVO?.toOtherLoadDirection =
                    DIY_FLOW_DIRECTION_NONE;
                monitorModel.energyFlowChartVO?.otherLoadVo?.otherLoadPower = 0;
              } else if (formatValue >= DIY_FLOW_DIRECTION_MAX) {
                monitorModel.energyFlowChartVO?.toOtherLoadDirection =
                    DIY_FLOW_DIRECTION_TO_OUT;
              } else {
                monitorModel.energyFlowChartVO?.toOtherLoadDirection =
                    DIY_FLOW_DIRECTION_TO_CENTER;
              }

              break;
            case diy_point_SOC:
              double step = 0.1;

              /// 电池的剩余电量百分比
              var value = meter[1];
              monitorModel.energyFlowChartVO?.emsGwVo ??= EmsGwVo();
              monitorModel.energyFlowChartVO?.emsGwVo?.soc =
                  formatNum((double.parse(value) * step).toString(), 1);
              break;
            case diy_point_current_remaining_power:

              /// 电池的剩余电量
              var value = meter[1];
              monitorModel.energyFlowChartVO?.emsGwVo ??= EmsGwVo();
              monitorModel.energyFlowChartVO?.emsGwVo?.energyRemain =
                  formatNum((double.parse(value) / 1000).toString(), 1);
              break;
            case diy_point_battery_status:
              var value = meter[1];
              monitorModel.energyFlowChartVO?.emsGwVo ??= EmsGwVo();
              monitorModel.energyFlowChartVO?.emsGwVo?.chargeStatus =
                  int.parse(value);
              break;
            case diy_point_machine_status:
              var value = meter[1];
              // logger.d('diy_point_machine_status $value ${int.parse(value)}');
              monitorModel.energyFlowChartVO?.emsGwVo ??= EmsGwVo();
              monitorModel.energyFlowChartVO?.emsGwVo?.deviceStatus =
                  int.parse(value);
              break;
            case diy_point_fault:
              // 收到的数据是 类似 2147483648 的十进制数据，需要转成2进制处理
              var value = meter[1];
              var bits = int.parse(value).toRadixString(2).padLeft(32, '0');
              if (bits[1] == '1' || bits[0] == '1') {
                monitorModel.energyFlowChartVO?.emsGwVo?.deviceStatus =
                    deviceWorkStatusFault;
              }

              monitorModel.energyFlowChartVO?.dcInfo?.deviceStatus =
                  bits[2] == '1'
                      ? deviceWorkStatusFault
                      : deviceWorkStatusNormal;
              monitorModel.energyFlowChartVO?.plugInfo?.deviceStatus =
                  bits[3] == '1'
                      ? deviceWorkStatusFault
                      : deviceWorkStatusNormal;
              if (bits[4] == "1") {
                monitorModel.energyFlowChartVO?.ctDevice?.ctStatus =
                    CTStatusType.fault;
              }
              break;
            case diy_point_warning:
              // var value = meter[1];
              var value = "0";
              var bits = int.parse(value).toRadixString(2).padLeft(32, '0');

              if (bits[1] == '1' || bits[0] == '1') {
                monitorModel.energyFlowChartVO?.emsGwVo?.deviceStatus =
                    deviceWorkStatusAlarm;
              }
              monitorModel.energyFlowChartVO?.dcInfo?.deviceStatus =
                  bits[2] == '1'
                      ? deviceWorkStatusAlarm
                      : deviceWorkStatusNormal;
              monitorModel.energyFlowChartVO?.plugInfo?.deviceStatus =
                  bits[3] == '1'
                      ? deviceWorkStatusAlarm
                      : deviceWorkStatusNormal;
              if (bits[4] == '1') {
                monitorModel.energyFlowChartVO?.ctDevice?.ctStatus =
                    CTStatusType.alarm;
              }
              break;
            case diy_point_grid_connect_status:
              var value = meter[1];
              monitorModel.gridConnectStatus = int.parse(value);
              break;
            case diy_point_on_grid_power:
              var value = meter[1];
              acOngridPower = double.parse(value);
              monitorModel.energyFlowChartVO?.onGridPower = acOngridPower;
              monitorModel.energyFlowChartVO?.toAcOnGridDirection =
                  (acOngridPower.toInt() <= DIY_FLOW_DIRECTION_MAX &&
                          acOngridPower.toInt() >= DIY_FLOW_DIRECTION_MIN)
                      ? DIY_FLOW_DIRECTION_NONE
                      : acOngridPower.toInt() > DIY_FLOW_DIRECTION_MAX
                          ? DIY_FLOW_DIRECTION_TO_OUT
                          : DIY_FLOW_DIRECTION_TO_CENTER;
              break;
            case diy_point_runMode:
              var value = meter[1];
              monitorModel.systemVO ??= MonitorSystemModel();
              monitorModel.systemVO?.runModel = int.parse(value);
              break;
            case diy_point_ems_turn_off_system:
              monitorModel.emsPowerStatus = meter[1].toString();
              break;
          }
        });
        break;
      case diy_Productkey_BMS:
        monitorModel.energyFlowChartVO?.emsGwVo ??= EmsGwVo();
        monitorModel.energyFlowChartVO?.emsGwVo?.powerPackList
            ?.forEach((powerPack) {
          if (powerPack?.deviceNo == devSn) {
            meterList?.forEach((meter) {
              switch (meter[0]) {
                case diy_point_bms:

                  /// 3是充电，4是放电
                  var value = meter[1];
                  powerPack?.bmsOptStatus = int.parse(value);
                  break;

                case diy_point_bms_soc:
                  double step = 0.1;

                  /// 电池的剩余电量百分比
                  var value = meter[1];
                  powerPack?.soc =
                      formatNum((double.parse(value) * step).toString(), 1);
                  break;
                case diy_point_bms_current_power:

                  /// 电池的剩余电量
                  var value = meter[1];
                  powerPack?.energyRemain =
                      formatNum((double.parse(value) / 1000).toString(), 1);
                  break;
              }
            });
          }
        });
        break;
      case diy_Productkey_PLUG:
        // monitorModel.energyFlowChartVO?.plugInfo ??= PlugInfo();
        var plugNum = monitorModel.energyFlowChartVO?.plugInfo?.plugNum ?? 0;
        monitorModel.energyFlowChartVO?.plugInfo?.plugNum = plugNum;
        monitorModel.energyFlowChartVO?.plugInfo?.plugs?.forEach((plugItem) {
          if (devSn == plugItem.deviceNo) {
            meterList?.forEach((meter) {
              switch (meter[0]) {
                case diy_point_plug_children_switch:
                  var value = meter[1];
                  plugItem.plugSwitch = formatSwitchValue(value);
                  break;
                case diy_point_plug_communication_status:
                  plugItem.deviceStatus = meter[1] == "1"
                      ? DeviceStatus.OFFLINE.status
                      : DeviceStatus.NORMAL.status;
                  break;
                case diy_point_plug_children_power:
                  var value = meter[1];
                  plugItem.plugPower = formatNum(value, 1).abs();
                  break;
                case diy_point_plug_children_time_start_point:
                  parsePlugTimerSwitch(meterList, plugItem);
                  break;
              }
            });
          }
        });
        break;
      case diy_Productkey_DC:
        // logger.d(
        //     'diy_Productkey_DC dcs length ${monitorModel.energyFlowChartVO?.dcs?.length} keepDevice length ${keepDevices[diy_Productkey_DC]?.length}');
        monitorModel.energyFlowChartVO?.dcs ??= List.filled(
            ReceiverManager.instance.keepDevices[diy_Productkey_DC]?.length ??
                0,
            DCPropertyModel());
        monitorModel.energyFlowChartVO?.dcs?.forEach((element) {
          element.deviceNo = devSn;
          // logger.d('diy_Productkey_DC 数组解析 dcs ${devSn}');
          meterList?.forEach((meter) {
            switch (meter[0]) {
              case diy_point_dc_communication_status:
                var value = meter[1];
                element.deviceStatus =
                    DcCommunicationStatus.fromInt(int.parse(value)).toInt();
                monitorModel.energyFlowChartVO?.dcInfo?.dcCommunicationStatus =
                    DcCommunicationStatus.fromInt(int.parse(value)).toInt();
                break;
              case diy_point_dc_output_status_read:
                var value = meter[1];
                element.dcOutEnable = formatSwitchValue(value);
                element.upsDCLoadSwitchIndetifier =
                    diy_point_dc_output_status_read;
                monitorModel.energyFlowChartVO?.dcInfo?.dcOutEnable =
                    formatSwitchValue(value);
                break;
              case diy_point_dc_output_typec1_switch:
                var value = meter[1];
                // monitorModel.energyFlowChartVO?.dcInfo ??= DcInfo();
                monitorModel.energyFlowChartVO?.dcInfo?.typeC1Switch =
                    formatSwitchValue(value);
              case diy_point_dc_output_typec1_power:
                var value = meter[1];
                // monitorModel.energyFlowChartVO?.dcInfo ??= DcInfo();
                monitorModel.energyFlowChartVO?.dcInfo?.typeC1Power =
                    formatNum(value, 1).abs();
                break;
              case diy_point_dc_output_typec2_switch:
                var value = meter[1];
                // monitorModel.energyFlowChartVO?.dcInfo ??= DcInfo();
                monitorModel.energyFlowChartVO?.dcInfo?.typeC2Switch =
                    formatSwitchValue(value);
              case diy_point_dc_output_typec2_power:
                var value = meter[1];
                // monitorModel.energyFlowChartVO?.dcInfo ??= DcInfo();
                monitorModel.energyFlowChartVO?.dcInfo?.typeC2Power =
                    formatNum(value, 1).abs();
                break;
              case diy_point_dc_output_usb_switch:
                var value = meter[1];
                // monitorModel.energyFlowChartVO?.dcInfo ??= DcInfo();
                monitorModel.energyFlowChartVO?.dcInfo?.usbaswitch =
                    formatSwitchValue(value);
              case diy_point_dc_output_usb_power:
                var value = meter[1];
                // monitorModel.energyFlowChartVO?.dcInfo ??= DcInfo();
                monitorModel.energyFlowChartVO?.dcInfo?.usbSwitchPower =
                    formatNum(value, 1).abs();
                break;
            }
          });
        });
        // 修复蓝牙模式下DC 没有的问题
        if (LocalModeManger.instance.isLocalModeNow) {
          var dc = monitorModel.energyFlowChartVO?.dcs?.first;
          dc?.deviceName = monitorModel.energyFlowChartVO?.dcInfo?.deviceName;
          dc?.dcOutEnable = monitorModel.energyFlowChartVO?.dcInfo?.dcOutEnable;
          dc?.upsDCLoadSwitchIndetifier = diy_point_dc_output_status_read;
          dc?.deviceStatus =
              monitorModel.energyFlowChartVO?.dcInfo?.dcCommunicationStatus ??
                  0;
          dc?.modelKey = monitorModel.energyFlowChartVO?.dcInfo?.modelKey;
        }
        break;
    }
    if (isCombine()) {
      handleCombineSystem(monitorModel, meterList ?? [], elementProductKey);
    }
  });

  var emsStatus = monitorModel.energyFlowChartVO?.emsGwVo?.deviceStatus;
  if (emsStatus != null && !isCombine()) {
    // 避免显示语言包时出现 -1 的情况  因为离线没有语言包，会显示 --
    // 在离线本地模式切换时，有可能会触发这个逻辑
    emsStatus = emsStatus == -1 ? 0 : emsStatus;
    monitorModel.energyFlowChartVO?.emsGwVo?.workStatusLanguageKey =
        'tsl.${monitorModel.energyFlowChartVO?.emsGwVo?.modelKey ?? 'HB-EMS-MODEL'}.properties.workStatus.value.$emsStatus';
  }

  var plugNum = monitorModel.energyFlowChartVO?.plugInfo?.plugNum ?? 0;
  var plugPower = monitorModel.energyFlowChartVO?.plugInfo?.plugPower ?? 0;
  monitorModel.energyFlowChartVO?.toPlugDirection = plugNum == 0
      ? DIY_FLOW_DIRECTION_NO_DEVICE
      : (plugPower <= DIY_FLOW_DIRECTION_MAX &&
              plugPower >= DIY_FLOW_DIRECTION_MIN)
          ? DIY_FLOW_DIRECTION_NONE
          : DIY_FLOW_DIRECTION_TO_OUT;

  monitorModel.energyFlowChartVO?.gridVo ??= GridVo();
  monitorModel.energyFlowChartVO?.acMainVo = null;
  monitorModel.energyFlowChartVO?.toAcMainDirection =
      DIY_FLOW_DIRECTION_NO_DEVICE;
  if (isCombine()) {
    String combineCtValue =
        monitorModel.energyFlowChartVO?.combineCtValue ?? '0';
    monitorModel.energyFlowChartVO?.gridVo?.combinePower =
        formatPowerBoundary(formatNum(combineCtValue, 1));
    var doubleValue = double.parse(combineCtValue).toInt();
    monitorModel.energyFlowChartVO?.toGridDirection =
        (doubleValue <= DIY_FLOW_DIRECTION_MAX &&
                doubleValue >= DIY_FLOW_DIRECTION_MIN)
            ? DIY_FLOW_DIRECTION_NONE
            : doubleValue > DIY_FLOW_DIRECTION_MAX
                ? DIY_FLOW_DIRECTION_TO_OUT
                : DIY_FLOW_DIRECTION_TO_CENTER;
  } else {
    var ctValue = monitorModel.energyFlowChartVO?.ctValue ?? '0';
    monitorModel.energyFlowChartVO?.gridVo?.gridPower =
        formatPowerBoundary(formatNum(ctValue, 1));
    var doubleValue = double.parse(ctValue).toInt();
    monitorModel.energyFlowChartVO?.toGridDirection =
        (doubleValue <= DIY_FLOW_DIRECTION_MAX &&
                doubleValue >= DIY_FLOW_DIRECTION_MIN)
            ? DIY_FLOW_DIRECTION_NONE
            : doubleValue > DIY_FLOW_DIRECTION_MAX
                ? DIY_FLOW_DIRECTION_TO_OUT
                : DIY_FLOW_DIRECTION_TO_CENTER;
  }

  monitorModel.isFromMqtt = true;
  ReceiverManager.instance.model = monitorModel;
  ReceiverManager.instance.model?.isOffline = false;
  return monitorModel;
}

MonitorResponseModel buildEmptyModel() {
  var monitorModel = ReceiverManager.instance.model ??
      ReceiverManager.instance.cachedOldModel ??
      MonitorResponseModel();
  monitorModel.energyFlowChartVO ??= EnergyFlowChartVo();
  monitorModel.energyFlowChartVO!.acInfo?.epsLoadPower = null;
  monitorModel.energyFlowChartVO!.toAcOutputDirection = 0;
  monitorModel.energyFlowChartVO!.emsGwVo?.chargeStatus = 0;
  monitorModel.energyFlowChartVO!.emsGwVo?.soc = 0;
  monitorModel.energyFlowChartVO!.emsGwVo?.energyRemain = null;
  monitorModel.energyFlowChartVO!.acMainVo?.acMainPower = null;
  monitorModel.energyFlowChartVO!.toAcMainDirection = 0;

  monitorModel.energyFlowChartVO!.toAcOnGridDirection = 0;

  monitorModel.energyFlowChartVO!.dcInfo?.dcOutPower = null;
  monitorModel.energyFlowChartVO!.toDcDirection = 0;

  monitorModel.energyFlowChartVO!.pvInfo?.pvPower = null;
  monitorModel.energyFlowChartVO!.toPvDirection = 0;

  monitorModel.energyFlowChartVO!.plugInfo?.plugPower = null;
  monitorModel.energyFlowChartVO!.plugInfo?.plugs?.forEach((element) {
    element.plugPower = 0;
  });
  monitorModel.energyFlowChartVO!.toPlugDirection = 0;

  monitorModel.energyFlowChartVO!.gridVo?.gridPower = null;
  monitorModel.energyFlowChartVO!.toGridDirection = 0;
  monitorModel.energyFlowChartVO!.otherLoadVo?.otherLoadPower = null;
  monitorModel.energyFlowChartVO!.toOtherLoadDirection = 0;
  monitorModel.isFromMqtt = true;
  return monitorModel;
}

MonitorResponseModel buildOfflineModel() {
  var monitorModel = ReceiverManager.instance.model ??
      ReceiverManager.instance.cachedOldModel ??
      MonitorResponseModel();
  monitorModel.energyFlowChartVO ??= EnergyFlowChartVo();
  monitorModel.energyFlowChartVO!.acInfo?.epsLoadPower = null;
  monitorModel.energyFlowChartVO!.toAcOutputDirection = 0;
  monitorModel.energyFlowChartVO!.emsGwVo?.chargeStatus = 0;
  monitorModel.energyFlowChartVO!.emsGwVo?.soc = null;
  monitorModel.energyFlowChartVO!.emsGwVo?.deviceStatus = -1; // -1 为离线
  monitorModel.energyFlowChartVO!.emsGwVo?.energyRemain = null;
  monitorModel.energyFlowChartVO!.acMainVo?.acMainPower = null;
  monitorModel.energyFlowChartVO!.toAcMainDirection = 0;
  monitorModel.energyFlowChartVO!.toAcOnGridDirection = 0;

  monitorModel.energyFlowChartVO!.dcInfo?.dcOutPower = null;
  monitorModel.energyFlowChartVO!.toDcDirection = 0;

  monitorModel.energyFlowChartVO!.pvInfo?.pvPower = null;
  monitorModel.energyFlowChartVO!.toPvDirection = 0;

  monitorModel.energyFlowChartVO!.plugInfo?.plugPower = null;
  monitorModel.energyFlowChartVO!.plugInfo?.plugs?.forEach((element) {
    element.plugPower = 0;
  });
  monitorModel.energyFlowChartVO!.toPlugDirection = 0;

  monitorModel.energyFlowChartVO!.gridVo?.gridPower = 0;
  monitorModel.energyFlowChartVO!.toGridDirection = 0;

  monitorModel.energyFlowChartVO!.otherLoadVo?.otherLoadPower = 0;
  monitorModel.energyFlowChartVO!.toOtherLoadDirection = 0;
  monitorModel.isFromMqtt = true;
  monitorModel.isOffline = true;
  ReceiverManager.instance.model = monitorModel;
  return monitorModel;
}
