import 'package:flutter_basic/features/devices/config/device_config.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/bluetooth/plug_chose_time_slot_utils.dart';
import 'package:flutter_basic/platform/community/receiver_manager.dart';
import 'package:flutter_basic/platform/mqtt/mqtt_manager.dart';
import 'package:flutter_basic/platform/utils/combine.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/monitor_quick_control_model.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/monitor_response_graph_model.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/monitor_response_model.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/plug_switch.dart';

import '../../../repositories/system_repository/model/devices/ems_device_model.dart';
import './constants.dart';

/// 处理快捷控制
MonitorQuickControlModel getModelByMonitor(
  MonitorResponseModel monitorModel, {
  MonitorQuickControlModel? quickModel,
}) {
  MonitorQuickControlModel model = quickModel ?? MonitorQuickControlModel();
  bool clustered = DeviceConfig.clustered(model.clustered);

  if (monitorModel.systemVO?.runModel != null) {
    var isLocal = LocalModeManger.instance.isLocalModeNow;
    model.emsProperty ??= EMSPropertyModel();
    var list = ReceiverManager.instance.keepDevices[diy_Productkey_EMS] ?? [];
    var deviceNo = list.isNotEmpty
        ? ReceiverManager.instance.keepDevices[diy_Productkey_EMS]?.first
        : monitorModel.energyFlowChartVO?.emsGwVo?.deviceNo;
    model.emsProperty?.deviceNo = deviceNo;
    model.emsProperty?.deviceName =
        model.emsProperty?.deviceName ?? MqttManager.instance.deviceName;
    model.emsProperty?.deviceStatus = monitorModel.isOffline
        ? DeviceStatus.OFFLINE.status
        : DeviceStatus.NORMAL.status;
    model.emsProperty?.deviceWorkStatus = DeviceWorkStatus.fromInt(
        monitorModel.energyFlowChartVO?.emsGwVo?.deviceStatus);
    model.emsProperty?.productKey = diy_Productkey_EMS;
    model.emsProperty?.modelKey = 'HB-EMS-MODEL';

    // 运行模式
    model.emsProperty?.workModeValue =
        monitorModel.systemVO?.runModel.toString();
    model.emsProperty?.workModeIdentifier ??= diy_point_runMode;

    // ac output
    model.emsProperty?.acOutEnableValue =
        monitorModel.energyFlowChartVO?.acInfo?.acOutEnable.toString();
    model.emsProperty?.slaverList?.forEach((element) {
      monitorModel.energyFlowChartVO?.acInfo?.slaverList?.forEach((slaver) {
        if (element.deviceNo == slaver.deviceNo) {
          element.acOutEnableValue = slaver.acOutEnable.toString();
        }
      });
    });
    model.emsProperty?.acOutEnableIdentifier ??= diy_point_ac_output_switch;
    if (clustered) {
      for (int i = 0; i < (model.emsProperty?.slaverList!.length ?? 0); i++) {
        model.emsProperty?.slaverList?[i].acOutEnableIdentifier ??=
            diy_point_ac_output_switch;
      }
    }

    // 插座
    List<Plug> plugList = [];
    var plugs = monitorModel.energyFlowChartVO?.plugInfo?.plugs;
    if (isCombine()) {
      monitorModel.energyFlowChartVO?.plugInfo?.slaverList?.forEach((element) {
        if (element.plugs != null && element.plugs!.isNotEmpty) {
          plugList.addAll(element.plugs!);
        }
      });
    }
    plugList.addAll(plugs ?? []);
    if ((plugList.length) > 0) {
      model.plugSwitch = plugList!.map((plug) {
        PlugSwitch plugSwitch = PlugSwitch();
        plugSwitch.deviceNo = plug.deviceNo;
        plugSwitch.deviceName = plug.deviceName;
        plugSwitch.deviceStatus = plug.deviceStatus;
        plugSwitch.productKey = plug.productKey;
        plugSwitch.modelKey = plug.modelKey;
        plugSwitch.devCtrlInvokeIdentifier =
            isLocal ? diy_point_plug_switch_control : plug.plugSwitchIdentify;
        plugSwitch.switchStatus = plug.plugSwitch.toString();
        plugSwitch.socketPower = plug.plugPower;
        if (plug.timers != null && plug.timers!.isNotEmpty) {
          var recentTime =
              PlugChoseTimeSlotUtils.getNextTimeSlotData(plug.timers!);
          if (recentTime != null) {
            // plugSwitch.latestJobTime = recentTime!.recentTime;
          }
        }

        /// todo 取定时器最接近当前时间的 时间和值
        return plugSwitch;
      }).toList();
    }

    if (isLocal) {
      // turn off
      model.emsProperty?.deviceRebootIdentifier = diy_point_ems_restart;
      model.emsProperty?.turnOffIdentifier = diy_point_ems_turn_off_system;
    }
    if (monitorModel.emsPowerStatus != null) {
      model.emsProperty?.turnOffValue =
          monitorModel.emsPowerStatus ?? OnOffStatus.on.value;
    }
    // 二进制都是 1111111111111111111
    if (monitorModel.energyFlowChartVO?.enableConfig != null) {
      model.emsProperty?.enableConfig =
          monitorModel.energyFlowChartVO!.enableConfig!;
    }
  }

  return model;
}
